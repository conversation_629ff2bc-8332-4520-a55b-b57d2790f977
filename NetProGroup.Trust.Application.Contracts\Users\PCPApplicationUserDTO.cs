﻿// <copyright file="PCPApplicationUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Identity.Models;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// ApplicationUserDTO extension for the trident portal.
    /// </summary>
    public class PCPApplicationUserDTO : ApplicationUserDTO
    {
        /// <summary>
        /// Gets or sets the list of permissions for the user.
        /// </summary>
        public List<UserPermissionDTO> Permissions { get; set; } = new List<UserPermissionDTO>();

        /// <summary>
        /// Gets or sets the role in the portal of the user (Client or Management).
        /// </summary>
        public string PrimaryRoleLabel { get; set; }
    }
}
