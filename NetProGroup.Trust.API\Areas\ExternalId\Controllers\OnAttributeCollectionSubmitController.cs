﻿// <copyright file="OnAttributeCollectionSubmitController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Areas.ExternalId.Models;
using NetProGroup.Trust.API.Security;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace NetProGroup.Trust.API.Areas.ExternalId.Controllers
{
    /// <summary>
    /// Controller for the custom extension in an Entra ExternalID UserFlow.
    /// </summary>
    // [Authorize]
    [ApiController]
    [Area("ExternalId")]
    [Route("api/v{version:apiVersion}/[Area]/On-Attribute-Collection-Submit")]
    [Route("OnAttributeCollectionSubmit")]
    public class OnAttributeCollectionSubmitController : ControllerBase
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Style", "IDE0300:Simplify collection initialization", Justification = "Syntax conflict")]
        private static readonly char[] Separator = new[] { ' ' };

        private readonly ILogger<OnAttributeCollectionSubmitController> _logger;
        private readonly IMasterClientsAppService _masterClientsAppService;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="OnAttributeCollectionSubmitController"/> class.
        /// </summary>
        /// <param name="logger">A logger instance.</param>
        /// <param name="configuration">The configuration for the application.</param>
        /// <param name="masterClientsAppService">Instance of MasterClientAppService.</param>
        public OnAttributeCollectionSubmitController(ILogger<OnAttributeCollectionSubmitController> logger,
                                                     IConfiguration configuration,
                                                     IMasterClientsAppService masterClientsAppService)
        {
            _logger = logger;
            _masterClientsAppService = masterClientsAppService;
            _configuration = configuration;
        }

        /// <summary>
        /// Gets the MasterClientCode attribute from the body.
        /// </summary>
        /// <param name="body">The body of the request.</param>
        /// <returns>The found MasterClientCode or an empty string if not found.</returns>
        public static string GetClientMasterCode(string body)
        {
            var jobject = JObject.Parse(body);

            var attrs = jobject.SelectTokens("$.data.userSignUpInfo.attributes");

            foreach (JObject attr in attrs.Cast<JObject>())
            {
                var property = attr.First as JProperty;

                if (property != null && property.Name.EndsWith("masterclientcode", StringComparison.OrdinalIgnoreCase))
                {
                    return ((JValue)((JProperty)property.First.First).Value).Value.ToString();
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// The endpoint for a submit in the Signup flow (custom extension).
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch all")]

        public async Task<ActionResult<AttributeCollectionSubmitResponse>> PostAsync()
        {
            try
            {
                if (!await CustomExtensionsValidator.AuthenticateCallerAsync(Request, _configuration, _logger))
                {
                    return Unauthorized();
                }

                string body = string.Empty;
                AttributeCollectionRequest requestPayload = null;

                try
                {
                    var request = this.Request;
                    request.EnableBuffering();
                    request.Body.Position = 0;

                    using (var reader = new StreamReader(request.Body, Encoding.UTF8))
                    {
                        body = await reader.ReadToEndAsync().ConfigureAwait(false);
                    }

                    requestPayload = JsonConvert.DeserializeObject<AttributeCollectionRequest>(body);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Getting the body failed in OnAttributeCollectionSubmit()");
                }

                ArgumentNullException.ThrowIfNull(requestPayload, nameof(requestPayload));

                // Message object to return to Microsoft Entra ID
                var r = new AttributeCollectionSubmitResponse();

                // Get the MasterClientCode by reading the attributes
                var masterClientCode = GetClientMasterCode(body);
                if (string.IsNullOrEmpty(masterClientCode))
                {
                    r.data.actions[0].odatatype = AttributeCollectionSubmitResponse_ActionTypes.ShowBlockPage;
                    r.data.actions[0].message = "Can't find the MasterClientCode attribute.";
                    return r;
                }

                // Use an AppService to check for a correct match
                var email = requestPayload.data.userSignUpInfo.identities.First().issuerAssignedId;

                var exists = await _masterClientsAppService.EmailExistsForMasterClientCodeAsync(masterClientCode, email);

                if (!exists)
                {
                    _logger.LogWarning("Combination master client '{MasterClientCode}' and email '{Email}' not found in signup extension", masterClientCode, email);
                    r.data.actions[0].odatatype = AttributeCollectionSubmitResponse_ActionTypes.ShowBlockPage;
                    r.data.actions[0].message = "The MCC or email is not valid.";
                    return r;
                }

                // No issues have been identified, proceed to create the account
                r.data.actions[0].odatatype = AttributeCollectionSubmitResponse_ActionTypes.ContinueWithDefaultBehavior;

                return r;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Signup extension failed");
                throw;
            }
        }

        #region Utils

        /// <summary>
        /// Converts the Jwt token to a JwtSecurityToken.
        /// </summary>
        /// <param name="jwtToken">The Jwt token as a string.</param>
        /// <returns>The converted JwtSecurityToken.</returns>
#pragma warning disable SA1204 // Static elements should appear before instance elements
        private static JwtSecurityToken ConvertJwtStringToJwtSecurityToken(string jwtToken)
#pragma warning restore SA1204 // Static elements should appear before instance elements
        {
            var handler = new JwtSecurityTokenHandler();
            var token = handler.ReadJwtToken(jwtToken);

            return token;
        }

        #endregion

        /// <summary>
        /// Authenticates the caller of the extension.
        /// </summary>
        /// <remarks>
        /// https://learn.microsoft.com/en-us/entra/external-id/customers/concept-custom-extensions.
        /// </remarks>
        /// <param name="request">The request holdign a Bearer Token.</param>
        /// <returns>True if authenticated.</returns>
        private bool AuthenticateCaller(HttpRequest request)
        {
            if (request.Headers.TryGetValue("Authorization", out var headerAuth))
            {
                var jwtToken = headerAuth.First().Split(Separator, StringSplitOptions.RemoveEmptyEntries)[1];
                var jwtSecurityToken = ConvertJwtStringToJwtSecurityToken(jwtToken);

                var claimAZP = jwtSecurityToken.Claims.FirstOrDefault(x => x.Type == "azp");
                var claimAudience = jwtSecurityToken.Claims.FirstOrDefault(x => x.Type == "aud");
                var claimIssuer = jwtSecurityToken.Claims.FirstOrDefault(x => x.Type == "iss");

                if (claimAZP == null || claimAudience == null || claimIssuer == null)
                {
                    _logger.LogError("Not all required claims found in the Bearer Token.");
                    return false;
                }

                if (claimAZP.Value != "99045fe1-7639-4a75-9d4a-577b6ca3810f")
                {
                    _logger.LogError("zap claim not found or not the correct value (expected {Expected} but found {Found}).", "99045fe1-7639-4a75-9d4a-577b6ca3810f", claimAZP.Value);
                    return false;
                }

                var expectedAudience = _configuration.GetValue<string>("ExternalId:ClientId");
                if (claimAudience.Value != expectedAudience)
                {
                    _logger.LogError("aud claim not found or not the correct value (expected {Expected} but found {Found}).", expectedAudience, claimAudience.Value);
                    return false;
                }

                var expectedTenant = _configuration.GetValue<string>("ExternalId:TenantId");
                var expectedIssuer = $"https://{expectedTenant}.ciamlogin.com/{expectedTenant}";
                if (!claimIssuer.Value.StartsWith(expectedIssuer, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogError("iss claim not found or not the correct value (expected issuer to start with {Expected} but found {Found}).", expectedIssuer, claimIssuer.Value);
                    return false;
                }

                return true;
            }

            return false;
        }
    }
}