﻿// <copyright file="ActivityLogItemDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.EFAuditing.EFModels;

namespace NetProGroup.Trust.Application.Contracts.Audits
{
    /// <summary>
    /// Represents an item in the list with activities.
    /// </summary>
    public class ActivityLogItemDTO : EntityDTO<Guid?>
    {
        /// <summary>
        /// Gets or sets the dat and tiem that this activity took place.
        /// </summary>
        public DateTime ActionDate { get; set; }

        /// <summary>
        /// Gets or sets the username that performed the activity.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Gets or sets the email address of the user that performed the activity.
        /// </summary>
        public string EmailAddress { get; set; }

        /// <summary>
        /// Gets or sets the ActivityType.
        /// </summary>
        public string ActivityType { get; set; }

        /// <summary>
        /// Gets or sets the Action derived from ActivityType.
        /// </summary>
        public string Action { get; set; }

        /// <summary>
        /// Gets or sets the short description for the activity.
        /// </summary>
        public string ShortDescription { get; set; }

        /// <summary>
        /// Gets or sets the full text for the activity.
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Gets or sets the type name of the entity that was affected by the activity.
        /// </summary>
        public string EntityName { get; set; }

        /// <summary>
        /// Gets or sets the audits for the activity.
        /// </summary>
        public List<AuditUnitOfWorkDTO> Audits { get; set; }
    }
}
