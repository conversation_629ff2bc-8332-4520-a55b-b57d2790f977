// <copyright file="PaymentTransactionResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Payments.Response
{
    /// <summary>
    /// Represents the data transfer object for a payment transaction response.
    /// </summary>
    public class PaymentTransactionResponseDTO
    {
        /// <summary>
        /// Gets or sets the result of the transaction.
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// Gets or sets the result code of the transaction.
        /// </summary>
        public string ResultCode { get; set; }

        /// <summary>
        /// Gets or sets the result message of the transaction.
        /// </summary>
        public string ResultMessage { get; set; }

        /// <summary>
        /// Gets or sets the transaction ID.
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Gets or sets the status of the transaction.
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the transaction was created.
        /// </summary>
        public DateTime? ProcessCreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the transaction was paid.
        /// </summary>
        public DateTime? PaidAt { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the transaction is finished.
        /// </summary>
        public bool IsFinished { get; set; }

        /// <summary>
        /// Gets or sets the foreign key identifier for the associated payment provider.
        /// </summary>
        public Guid PaymentProviderId { get; set; }
    }
}