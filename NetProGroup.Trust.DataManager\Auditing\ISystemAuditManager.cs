﻿// <copyright file="ISystemAuditManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.Communication.EFModels;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Interface for SystemAuditManager.
    /// </summary>
    public interface ISystemAuditManager : IScopedService
    {
        /// <summary>
        /// Selects ActivityLogs for the given specifications in the request.
        /// </summary>
        /// <param name="request">Request with the selection parameters.</param>
        /// <returns>Response for the ActivityLogs.</returns>
        Task<ListActivityLogResponse> ListActivityLogsAsync(ListActivityLogRequest request);

        /// <summary>
        /// Selects activitylogs for the given list of entityids and activitytypes.
        /// </summary>
        /// <param name="entityIds">Collection of entity ids.</param>
        /// <param name="activityTypes">Collection of acctivitytypes.</param>
        /// <returns>Collection of ActivityLog items.</returns>
        Task<List<ActivityLog>> ListActivityLogsAsync(IEnumerable<Guid> entityIds, IEnumerable<string> activityTypes);

        /// <summary>
        /// Adds an ActivityLog entry when a user is registered for the first time (objectid update from null).
        /// </summary>
        /// <param name="registeredUser">The registered user.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddUserRegisteredActivityLogAsync(ApplicationUser registeredUser, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry when a user is authenticated (log in).
        /// </summary>
        /// <param name="authenticatedUser">The authenticated user.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddUserAuthenticatedActivityLogAsync(ApplicationUser authenticatedUser, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry when a user is authenticated (log in).
        /// </summary>
        /// <param name="authenticatedUser">The authenticated user.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddUserAuthenticatedActivityLogAsync(ApplicationUserDTO authenticatedUser, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry when a user is logged out.
        /// </summary>
        /// <param name="loggedoutUser">The logged out user.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddUserLogoutActivityLogAsync(ApplicationUser loggedoutUser, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry when a user is logged out.
        /// </summary>
        /// <param name="loggedoutUser">The logged out user.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddUserLogoutActivityLogAsync(ApplicationUserDTO loggedoutUser, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for adding a Master Client.
        /// </summary>
        /// <param name="addedMasterClient">The added master client.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddMasterClientAddedActivityLogAsync(MasterClient addedMasterClient, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for adding a Legal Entity to a Master Client.
        /// </summary>
        /// <param name="addedLegalEntity">The added legal entity.</param>
        /// <param name="addedToMasterClient">The master client the legal entity was added to.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddLegalEntityAddedToMasterClientActivityLogAsync(LegalEntity addedLegalEntity,
            MasterClient addedToMasterClient,
            bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for adding a Legal Entity to a Master Client.
        /// </summary>
        /// <param name="addedLegalEntity">The added legal entity.</param>
        /// <param name="addedToMasterClient">The master client the legal entity was added to.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<ActivityLog> CreateLegalEntityAddedToMasterClientActivityLogAsync(LegalEntity addedLegalEntity, MasterClient addedToMasterClient);

        /// <summary>
        /// Adds an ActivityLog entry for submitting a submission.
        /// </summary>
        /// <param name="submission">The submitted submission.</param>
        /// <param name="previousStatus">The previous submission status (before submitting the submission) used to determine if the submission was submitted or re-submitted.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddSubmissionSubmittedActivityLogAsync(Submission submission, SubmissionStatus previousStatus, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for scheduling the submit of a submission.
        /// </summary>
        /// <param name="submission">The submission to schedule.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddSubmissionScheduledActivityLogAsync(Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for custom activity.
        /// </summary>
        /// <param name="entity">The entity to link the activity to.</param>
        /// <param name="activityType">The type of activity from <see cref="ActivityLogActivityTypes"/>.</param>
        /// <param name="shortDescription">The short description.</param>
        /// <param name="text">The full description.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, string text, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for custom activity.
        /// </summary>
        /// <param name="entity">The entity to link the activity to.</param>
        /// <param name="activityType">The type of activity from <see cref="ActivityLogActivityTypes"/>.</param>
        /// <param name="shortDescription">The short description.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for adding a user to a Master Client.
        /// </summary>
        /// <param name="addedUser">The added user.</param>
        /// <param name="addedToMasterClient">The master client the user was added to.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddUserAddedToMasterClientActivityLogAsync(ApplicationUser addedUser,
            MasterClient addedToMasterClient,
            bool saveChanges = false);

        /// <summary>
        /// Creates an ActivityLog entry for adding a user to a Master Client.
        /// </summary>
        /// <param name="addedUser">The added user.</param>
        /// <param name="addedToMasterClient">The master client the user was added to.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<List<ActivityLog>> CreateUserAddedToMasterClientActivityLogAsync(ApplicationUser addedUser, MasterClient addedToMasterClient);

        /// <summary>
        /// Adds an ActivityLog entry for removing a user from a Master Client.
        /// </summary>
        /// <param name="removedUser">The removed user.</param>
        /// <param name="removedFromMasterClient">The master client the user was removed from.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddUserRemovedFromMasterClientActivityLogAsync(ApplicationUser removedUser, MasterClient removedFromMasterClient, bool saveChanges);

        /// <summary>
        /// Creates an ActivityLog entry for removing a user from a Master Client.
        /// </summary>
        /// <param name="removedUser">The removed user.</param>
        /// <param name="removedFromMasterClient">The master client the user was removed from.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<List<ActivityLog>> CreateUserRemovedFromMasterClientActivityLogAsync(ApplicationUser removedUser, MasterClient removedFromMasterClient);

        /// <summary>
        /// Adds an ActivityLog entry for deactivating a module.
        /// </summary>
        /// <param name="legalEntity">The legal entity.</param>
        /// <param name="deactivatedModule">The module.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<ActivityLog> CreateModuleDeactivatedActivityLogAsync(LegalEntity legalEntity, Module deactivatedModule);

        /// <summary>
        /// Gets a specific audit by ID.
        /// </summary>
        /// <param name="auditId">The ID of the audit to get.</param>
        /// <returns>The audit if found, null otherwise.</returns>
        Task<AuditUnitOfWorkDTO> GetAuditAsync(Guid auditId);

        /// <summary>
        /// Lists audits for a given entity.
        /// </summary>
        /// <param name="request">The request parameters.</param>
        /// <returns>Response containing audit items.</returns>
        Task<ListAuditResponse> ListAuditsAsync(ListAuditRequest request);

        /// <summary>
        /// Adds an activitylog entry for sending a company related email to the recipient.
        /// </summary>
        /// <param name="legalEntity">The compnay the email is related to.</param>
        /// <param name="recipient">Teh recipient of the email.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddMessageSentToEmployeeForCompanyActivityLogAsync(LegalEntity legalEntity, string recipient, bool saveChanges);

        /// <summary>
        /// Creates an ActivityLog entry for custom activity.
        /// </summary>
        /// <param name="entity">The entity to link the activity to.</param>
        /// <param name="activityType">The type of activity from <see cref="ActivityLogActivityTypes"/>.</param>
        /// <param name="shortDescription">The short description.</param>
        /// <param name="text">The full description.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<ActivityLog> CreateActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, string text);

        /// <summary>
        /// Adds an ActivityLog entry for updating the IsRead status of an inbox item.
        /// </summary>
        /// <param name="inboxMessage">The inbox message that is updated.</param>
        /// <param name="isRead">The new IsRead value.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>Task.</returns>
        Task AddInboxIsReadActivityLogAsync(Inbox inboxMessage, bool isRead, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a created request for information to a submission.
        /// </summary>
        /// <param name="submission">The submission entity.</param>
        /// <param name="requestForInformation">The request for information entity.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddSubmissionInformationRequestedActivityLogAsync(Submission submission, RequestForInformation requestForInformation, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a financial period changed to a submission.
        /// </summary>
        /// <param name="submission">The submission entity.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddSubmissionFinancialPeriodChangedActivityLogAsync(Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a cancelled request for information.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRequestForInformationCancelledActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a completed request for information.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRequestForInformationCompletedActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a notification of a completed request for information.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRfiCompletedNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a notification of a created request for information.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRfiCreatedNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a notification of a request for information due in one week.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRfiDueInOneWeekNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a notification of a request for information due in one day.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRfiDueInOneDayNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for a notification of a request for information three days overdue.
        /// </summary>
        /// <param name="requestForInformation">The RequestForInformation entity.</param>
        /// <param name="submission">The submission.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddRfiThreeDaysOverdueNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false);

        /// <summary>
        /// Adds an ActivityLog entry for an annual fee status update.
        /// </summary>
        /// <param name="annualFee">The anual fee.</param>
        /// <param name="legalEntity">The target legal entity.</param>
        /// <param name="saveChanges">Denotes whether to save the data immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddAnnualFeeStatusUpdatedActivityLogAsync(LegalEntityAnnualFee annualFee, LegalEntity legalEntity, bool saveChanges);
    }
}