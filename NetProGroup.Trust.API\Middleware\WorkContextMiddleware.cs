﻿// <copyright file="WorkContextMiddleware.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Services;

namespace NetProGroup.Trust.API.Middleware
{
    /// <summary>
    /// The middleware for setting up the WorkContext when a request comes in.
    /// </summary>
    public class WorkContextMiddleware
    {
        private readonly RequestDelegate _next;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkContextMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next delegate to call.</param>
        public WorkContextMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        /// <summary>
        /// The invokation of this middleware.
        /// </summary>
        /// <param name="ctx">The HttpContext for this call.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task InvokeAsync(HttpContext ctx)
        {
            ArgumentNullException.ThrowIfNull(ctx);

            var workContext = ctx.RequestServices.GetService<IWorkContext>();
            ILogger logger = ctx.RequestServices.GetService<ILogger<WorkContextMiddleware>>();
            var configuration = ctx.RequestServices.GetService<IConfiguration>();

            // Get the userid from the header
            var userId = ctx.Request.Headers["x-userid"].FirstOrDefault();
#if DEBUG
            userId = configuration.GetValue<string>("UserId", userId);
#endif
            if (Guid.TryParse(userId, out var userIdGuid))
            {
                // logger.LogDebug($"User from header (x-userid) is '{userId}'");
                workContext.IdentityUserId = userIdGuid;

                var userManager = ctx.RequestServices.GetService<IUserManager>();
                workContext.User = await userManager.GetUserByIdAsync(userIdGuid);
                workContext.UserName = workContext.User?.Username;
            }

            // Get the ApplicationId from claims or optionally from config if not in claim
            if (ctx.User != null)
            {
                var appClaims = ctx.User.Claims.ToList();
                if (appClaims.Count > 0 && appClaims.Exists(x => x.Type.Equals("appid", StringComparison.OrdinalIgnoreCase)))
                {
                    var appId = appClaims.FirstOrDefault(x => x.Type.Equals("appid", StringComparison.OrdinalIgnoreCase))?.Value;
                    if (appId != null)
                    {
                        workContext.SetProperty("appid", new Guid(appId));
                    }
                }
            }

            if (workContext.GetProperty<Guid?>("appid") == null)
            {
                var appId = configuration.GetValue<Guid?>("Application:ClientId");
                workContext.SetProperty("appid", appId);
            }

            // Switch culture, this will affect the localization
            Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("en-TC"); // Turks and Caicos
            Thread.CurrentThread.CurrentUICulture = Thread.CurrentThread.CurrentCulture;

            await _next.Invoke(ctx);
        }
    }
}
