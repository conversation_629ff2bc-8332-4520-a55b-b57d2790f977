// <copyright file="StatusProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.Status.Models;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// AutoMapper profile for status-related mappings.
    /// </summary>
    public class StatusProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="StatusProfile"/> class.
        /// </summary>
        public StatusProfile()
        {
            CreateMap<ViewPointSyncJobData, ViewPointSyncStatusDTO>();
            CreateMap<SyncDetails, SyncDetailsDTO>();
        }
    }
}
