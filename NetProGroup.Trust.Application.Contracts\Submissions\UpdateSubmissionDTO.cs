﻿// <copyright file="UpdateSubmissionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Model for updating an existing submission.
    /// </summary>
    public class UpdateSubmissionDTO
    {
        /// <summary>
        /// Gets or sets the id of the submission.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the updated form.
        /// </summary>
        public FormBuilder FormBuilder { get; set; }

        /// <summary>
        /// Gets or sets the document ids related to the submission.
        /// </summary>
        public List<Guid> DocumentIds { get; set; }
    }
}
