// <copyright file="AnnouncementDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Represent the necessary data to create an announcement.
    /// </summary>
    public class AnnouncementDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the announcement subject.
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets the email subject.
        /// </summary>
        public string EmailSubject { get; set; }

        /// <summary>
        /// Gets or sets the body of the announcement.
        /// </summary>
        public string Body { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement is going to be send now or not.
        /// </summary>
        public bool IsSent { get; set; }

        /// <summary>
        /// Gets or sets the scheduled send date for the announcement.
        /// </summary>
        public DateTime SendAt { get; set; }

        /// <summary>
        /// Gets or sets the sent date for the announcement.
        /// </summary>
        public DateTime? SentAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the master clients related to the announcement.
        /// </summary>
        public List<Guid> MasterClientIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the codes of the master clients related to the announcement.
        /// </summary>
        public List<string> MasterClientCodes { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the id of the legal entities related to the announcement.
        /// </summary>
        public List<Guid> LegalEntityIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the id of the jurisdictions related to the announcement.
        /// </summary>
        public List<Guid> JurisdictionIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the names of the jurisdictions related to the announcement.
        /// </summary>
        public List<string> JurisdictionNames { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the id of the users related to the announcement.
        /// </summary>
        public List<Guid> UserIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the announcement documents.
        /// </summary>
        public List<AnnouncementDocumentDTO> Documents { get; set; }
    }
}