﻿// <copyright file="VerifyMFACodeDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// DTO for verifiying the MFA code (either authenticator or e-mail).
    /// </summary>
    public class VerifyMFACodeDTO
    {
        /// <summary>
        /// Gets or sets the id of the user to verify the code for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the verification code.
        /// </summary>
        public string VerificationCode { get; set; }
    }
}
