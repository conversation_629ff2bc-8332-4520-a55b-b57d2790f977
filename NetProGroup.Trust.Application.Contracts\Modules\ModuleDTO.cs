﻿// <copyright file="ModuleDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a module.
    /// </summary>
    public class ModuleDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the (display)name of the module.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the key of the module.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets whether the module is active (global).
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? IsActive { get; set; }

        /// <summary>
        /// Gets or sets whether the module is enabled for the enetity that it is requested for or always false for a complete list.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? IsEnabled { get; set; }
    }
}
