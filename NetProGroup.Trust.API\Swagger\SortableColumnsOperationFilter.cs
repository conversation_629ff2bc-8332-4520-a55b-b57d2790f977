// <copyright file="SortableColumnsOperationFilter.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;
using System.Reflection;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace NetProGroup.Trust.API.Swagger
{
    /// <summary>
    /// Operation filter that adds enum values from SortableColumns attribute to swagger documentation.
    /// </summary>
    public class SortableColumnsOperationFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the filter to the specified operation using the given context.
        /// </summary>
        /// <param name="operation">The operation to apply the filter to.</param>
        /// <param name="context">The current operation filter context.</param>
#pragma warning disable CA1062 // Validate arguments of public methods
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.Parameters == null)
            {
                return;
            }

            foreach (var parameter in operation.Parameters)
            {
                var parameterDescription = context.ApiDescription.ParameterDescriptions
                    .FirstOrDefault(p => p.Name == parameter.Name);

                if (parameterDescription?.ModelMetadata?.ContainerType == null)
                {
                    continue;
                }

                var propertyInfo = parameterDescription.ModelMetadata.ContainerType
                    .GetProperties()
                    .FirstOrDefault(p => p.Name == parameter.Name);

                if (propertyInfo == null)
                {
                    continue;
                }

                var sortableColumnsAttribute = propertyInfo.GetCustomAttribute<SortableColumnsAttribute>();
                if (sortableColumnsAttribute == null)
                {
                    continue;
                }

                var sortableColumns = sortableColumnsAttribute.SortableColumns;
                if (!sortableColumns.Any())
                {
                    continue;
                }

                parameter.Schema.Enum = sortableColumns.Select(x => new OpenApiString(x)).Cast<IOpenApiAny>().ToList();
                parameter.Schema.Type = "string";
                parameter.Schema.Format = null;
            }
        }
#pragma warning restore CA1062 // Validate arguments of public methods
    }
}
