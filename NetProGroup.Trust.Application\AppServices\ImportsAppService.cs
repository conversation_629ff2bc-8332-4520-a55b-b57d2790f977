﻿// <copyright file="ImportsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Imports;
using NetProGroup.Trust.Import.Interfaces;
using NetProGroup.Trust.DataManager.Security;

namespace NetProGroup.Trust.Application.AppServices
{
    /// <summary>
    /// Application service for file imports.
    /// </summary>
    public class ImportsAppService : IImportsAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly ISecurityManager _securityManager;
        private readonly IMasterClientImport _masterClientImport;

        /// <summary>
        /// Initializes a new instance of the <see cref="ImportsAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="securityManager">Instance of the security manager.</param>
        /// <param name="masterClientImport">The importer implementation for MasterClients.</param>
        public ImportsAppService(ILogger<ImportsAppService> logger,
                               IMapper mapper,
                               ISecurityManager securityManager,
                               IMasterClientImport masterClientImport)
        {
            _logger = logger;
            _mapper = mapper;
            _securityManager = securityManager;
            _masterClientImport = masterClientImport;
        }

        /// <summary>
        /// Imports a MasterClient Excel file.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to upload the file for.</param>
        /// <param name="formFile">The uploaded IFormFile.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ImportMasterClients(Guid jurisdictionId, IFormFile formFile)
        {
            ArgumentNullException.ThrowIfNull(formFile, nameof(formFile));

            await _securityManager.RequireManagementUserAsync();

            await _masterClientImport.ImportFileAsync(jurisdictionId, formFile.OpenReadStream());
        }
    }
}
