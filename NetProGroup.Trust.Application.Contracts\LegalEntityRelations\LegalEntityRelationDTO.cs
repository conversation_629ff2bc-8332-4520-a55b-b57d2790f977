﻿// <copyright file="LegalEntityRelationDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations
{
    /// <summary>
    /// Represents a relation of a LegalEntity. Use a sbase class for BO/Dir/RoM etc.
    /// </summary>
    public class LegalEntityRelationDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the unique relation code.
        /// </summary>
        public string UniqueRelationCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the relation is an individual.
        /// </summary>
        public bool IsIndividual { get; set; }

        /// <summary>
        /// Gets or sets the officer type.
        /// </summary>
        public string OfficerTypeCode { get; set; }

        /// <summary>
        /// Gets or sets the officer type name.
        /// </summary>
        public string OfficerTypeName { get; set; }

        /// <summary>
        /// Gets or sets the id of the LegalEntity.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the name of the LegalEntity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the code of the BeneficialOwner.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the optional date of birth (individual).
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the place of birth (individual).
        /// </summary>
        public string PlaceOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country of birth (individual).
        /// </summary>
        public string CountryOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country code of birth (individual).
        /// </summary>
        public string CountryCodeOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the nationality (individual).
        /// </summary>
        public string Nationality { get; set; }

        /// <summary>
        /// Gets or sets the residential address (individual).
        /// </summary>
        public string ResidentialAddress { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number (company).
        /// </summary>
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Gets or sets the optional date of incorporation (company).
        /// </summary>
        public DateTime? DateOfIncorporation { get; set; }

        /// <summary>
        /// Gets or sets the address (company).
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the country of formation (company).
        /// </summary>
        public string CountryOfFormation { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction of regulator (company).
        /// </summary>
        public string JurisdictionOfRegulator { get; set; }

        /// <summary>
        /// Gets or sets the name of regulator (company).
        /// </summary>
        public string NameOfRegulator { get; set; }

        /// <summary>
        /// Gets or sets the sovereign state (company).
        /// </summary>
        public string SovereignState { get; set; }

        /// <summary>
        /// Gets or sets the TIN or other identification (individual and company).
        /// </summary>
        public string TIN { get; set; }

        /// <summary>
        /// Gets or sets the stock code.
        /// </summary>
        public string StockCode { get; set; }

        /// <summary>
        /// Gets or sets the stock exchange.
        /// </summary>
        public string StockExchange { get; set; }

        /// <summary>
        /// Gets or sets the metadata for the relation.
        /// </summary>
        public LegalEntityRelationMetaData MetaData { get; set; }
    }
}