// <copyright file="UpdateSubmissionInformationDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// The necessary data used to update the general information for a submission entity.
    /// </summary>
    public class UpdateSubmissionInformationDTO
    {
        /// <summary>
        /// gets or sets the starts at value as DateTime.
        /// </summary>
        public DateTime StartAt { get; set; }

        /// <summary>
        /// gets or sets the ends at value as DateTime.
        /// </summary>
        public DateTime EndAt { get; set; }
    }
}