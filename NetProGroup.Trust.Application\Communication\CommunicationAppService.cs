// <copyright file="CommunicationAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Collections.ObjectModel;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using System.Web;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Messaging.Tokens;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Framework.Services.Communication.EFRepository;
using NetProGroup.Framework.Services.Communication.Services;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.MessageTemplates;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.Application.Communication
{
    /// <summary>
    /// ApplicationService for communications.
    /// </summary>
    public class CommunicationAppService : ICommunicationAppService, ISystemCommunicationAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly Microsoft.Extensions.Configuration.IConfiguration _configuration;
        private readonly IInboxService _inboxService;
        private readonly IInboxRepository _inboxRepository;
        private readonly IEmailService _emailService;
        private readonly IUserManager _userManager;
        private readonly IUserRepository _userRepository;

        private readonly ICommunicationManager _communicationManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly Framework.Services.Configuration.IConfigurationManager _configurationManager;
        private readonly TrustOfficeOptions _trustOfficeOptions;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;

        private readonly HashSet<Guid> _userExistsCache = new HashSet<Guid>();

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationAppService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="mapper">The mapper instance.</param>
        /// <param name="workContext">The current work context.</param>
        /// <param name="configuration">Instance of configuration.</param>
        /// <param name="inboxService">Instance of InboxService.</param>
        /// <param name="inboxRepository">Instance of InboxRepository.</param>
        /// <param name="emailService">Instance of the EmailService.</param>
        /// <param name="userManager">Instance of the UserManager.</param>
        /// <param name="userRepository">Instance of the UserRepository.</param>
        /// <param name="communicationManager">Instance of the CommunicationManager.</param>
        /// <param name="systemAuditManager">Instance of the audit manager.</param>
        /// <param name="configurationManager">Instance of the ConfigurationManager.</param>
        /// <param name="trustOfficeOptions">Configuration for TrustOffice.</param>
        /// <param name="legalEntitiesDataManager">Instance of the LegalEntitiesDataManager.</param>
        public CommunicationAppService(
            ILogger<CommunicationAppService> logger,
            IMapper mapper,
            IWorkContext workContext,
            Microsoft.Extensions.Configuration.IConfiguration configuration,
            IInboxService inboxService,
            IInboxRepository inboxRepository,
            IEmailService emailService,
            IUserManager userManager,
            IUserRepository userRepository,
            ICommunicationManager communicationManager,
            ISystemAuditManager systemAuditManager,
            Framework.Services.Configuration.IConfigurationManager configurationManager,
            IOptions<TrustOfficeOptions> trustOfficeOptions,
            ILegalEntitiesDataManager legalEntitiesDataManager)
        {
            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));

            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _configuration = configuration;
            _inboxService = inboxService;
            _inboxRepository = inboxRepository;
            _emailService = emailService;
            _userManager = userManager;
            _communicationManager = communicationManager;
            _userRepository = userRepository;
            _configurationManager = configurationManager;
            _systemAuditManager = systemAuditManager;

            _legalEntitiesDataManager = legalEntitiesDataManager;

            _trustOfficeOptions = trustOfficeOptions.Value;
        }

        /// <summary>
        /// Sends an inbox message and an email simultaneously.
        /// </summary>
        /// <param name="fromUserId">The user ID of the sender.</param>
        /// <param name="fromRolId">The role ID of the sender.</param>
        /// <param name="toUserId">The user ID of the recipient.</param>
        /// <param name="toRoleId">The role ID of the recipient.</param>
        /// <param name="recipientEmail">The email address of the recipient for sending an email.</param>
        /// <param name="inboxMessageTemplateId">The ID of the inbox message template to use (optional).</param>
        /// <param name="inboxMessageTemplateName">The name of the inbox message template to use (optional).</param>
        /// <param name="tokensInbox">Tokens to be used in the inbox message template (optional).</param>
        /// <param name="emailMessageTemplateId">The ID of the email message template to use (optional).</param>
        /// <param name="emailMessageTemplateName">The name of the email message template to use (optional).</param>
        /// <param name="tokensEmail">Tokens to be used in the email message template (optional).</param>
        /// <param name="files">List of files to be attached to the inbox message (optional).</param>
        /// <param name="saveChanges">Indicates whther the changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendInboxAndEmail(Guid fromUserId, Guid? fromRolId, Guid toUserId, Guid? toRoleId, string recipientEmail,
                                            Guid? inboxMessageTemplateId = null, string inboxMessageTemplateName = null, TokenList tokensInbox = null,
                                            Guid? emailMessageTemplateId = null, string emailMessageTemplateName = null, TokenList tokensEmail = null,
                                            Collection<IFormFile> files = null,
                                            bool saveChanges = false)
        {
            #region Send inbox

            Guid inboxId = Guid.Empty;

            if (inboxMessageTemplateId != null)
            {
                inboxId = await _inboxService.CreateInboxItemAsync(fromUserId, fromRolId, toUserId, toRoleId, (Guid)inboxMessageTemplateId, tokensInbox, saveChanges: saveChanges);
            }
            else if (inboxMessageTemplateName != null)
            {
                inboxId = await _inboxService.CreateInboxItemAsync(fromUserId, fromRolId, toUserId, toRoleId, inboxMessageTemplateName, tokensInbox, saveChanges: saveChanges);
            }

            #endregion

            #region Send email

            if (emailMessageTemplateId != null)
            {
                await _emailService.SendEmailByTemplateIdAsync(recipientEmail, (Guid)emailMessageTemplateId, tokensEmail, saveChanges: saveChanges);
            }
            else if (emailMessageTemplateName != null)
            {
                await _emailService.SendEmailByTemplateNameAsync(recipientEmail, emailMessageTemplateName, tokensEmail, saveChanges: saveChanges);
            }

            if (files != null && inboxId != Guid.Empty)
            {
                await _inboxService.AppendDocumentListToInboxItemAsync(inboxId, files, 1, "Inbox document", saveChanges: saveChanges);
            }

            #endregion
        }

        /// <summary>
        /// Sends an email message with the MFA verification code to the user.
        /// </summary>
        /// <param name="userId">Id of th euser to send the email to.</param>
        /// <param name="verificationCode">The verification code to include in the message.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendMFAVerificationCodeAsync(Guid userId, string verificationCode, TokenList tokens = null)
        {
            #region Checks and validations

            Check.NotNullOrWhiteSpace(verificationCode, nameof(verificationCode));
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
            var userCheck = await _userManager.CheckUserByIdAsync(userId, true);

            #endregion

            var user = await _userManager.GetUserByIdAsync(userId);

            tokens ??= new TokenList();
            new MessageTokenProvider(tokens).AddCommonTokens().AddUserTokens(user);

            tokens.Add(new Token("code", verificationCode));

            var systemUserId = await GetSystemUserIdAsync();

            var systemRole = await _userManager.GetRoleByNameAsync(WellKnownRoleNames.System);

            await SendEmail(systemUserId, user.Email, null, SystemNames.MFACodeMessage, tokens, saveChanges: true);
        }

        /// <summary>
        /// Sends an email message with a request for update.
        /// </summary>
        /// <param name="productionOffice">Used for dicovery of recipient.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendRequestForUpdateAsync(string productionOffice, TokenList tokens = null)
        {
            #region Checks and validations

            ArgumentNullException.ThrowIfNull(productionOffice, nameof(productionOffice));

            #endregion

            // Use the configuration to find the recipient based on the production office
            var recipient = await GetProductionOfficeRecipientAsync(ConfigurationConsts.NOTIFICATION_UPDATEREQUEST_RECIPIENT_PREFIX,
                                                                productionOffice);

            if (recipient == null)
            {
                throw new ConstraintException($"No default recipient configured for [{ConfigurationConsts.NOTIFICATION_UPDATEREQUEST_RECIPIENT_PREFIX}]");
            }

            tokens ??= new TokenList();

            if (string.IsNullOrEmpty(productionOffice))
            {
                productionOffice = "(!Production office unknown) ";
            }

            tokens.Add("subject.prefix", productionOffice);

            var systemUserId = await GetSystemUserIdAsync();

            var systemRole = await _userManager.GetRoleByNameAsync(WellKnownRoleNames.System);

            await SendEmail(systemUserId, recipient, null, SystemNames.RequestUpdateMessage, tokens, saveChanges: false);
        }

        /// <summary>
        /// Sends an email message with a request for assistance.
        /// </summary>
        /// <param name="productionOffice">Used for dicovery of recipient.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendRequestForAssistanceAsync(string productionOffice, TokenList tokens = null)
        {
            ArgumentNullException.ThrowIfNull(productionOffice, nameof(productionOffice));

            // Use the configuration to find the recipient based on the production office
            var recipient = await GetProductionOfficeRecipientAsync(ConfigurationConsts.NOTIFICATION_ASSISTANCEREQUEST_RECIPIENT_PREFIX,
                                                                productionOffice);

            if (recipient == null)
            {
                throw new ConstraintException($"No default recipient configured for [{ConfigurationConsts.NOTIFICATION_ASSISTANCEREQUEST_RECIPIENT_PREFIX}]");
            }

            tokens ??= new TokenList();

            if (string.IsNullOrEmpty(productionOffice))
            {
                productionOffice = "(!Production office unknown) ";
            }

            tokens.Add("subject.prefix", productionOffice);

            var systemUserId = await GetSystemUserIdAsync();

            var systemRole = await _userManager.GetRoleByNameAsync(WellKnownRoleNames.System);

            await SendEmail(systemUserId, recipient, null, SystemNames.RequestAssistanceMessage, tokens, saveChanges: false);
        }

        /// <summary>
        /// Sends an email message with an invitation to register.
        /// </summary>
        /// <param name="userId">Id of the user to send the invitation to.</param>
        /// <param name="masterClientCode">The masterclient code to include in the email.</param>
        /// <param name="reregistration">Denotes whether this is for a reregistration.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendUserInvitationAsync(Guid userId, string masterClientCode, bool reregistration, TokenList tokens = null)
        {
            #region Checks and validations

            var user = await _userManager.GetUserByIdAsync(userId);
            if (user == null)
            {
                throw new NotFoundException(ApplicationErrors.USER_ID_NOT_FOUND.ToErrorCode(), "User for invitation not found");
            }

            var portalUrl = _trustOfficeOptions.ClientPortalUrl;
            if (string.IsNullOrEmpty(portalUrl))
            {
                throw new APIException("ClientPortalUrl is not configured");
            }

            portalUrl = portalUrl.Trim();

            #endregion

            tokens ??= new TokenList();
            tokens.Add(new Token("masterclient.code", masterClientCode));
            tokens.Add(new Token("portal.url", portalUrl));
            new MessageTokenProvider(tokens).AddCommonTokens().AddUserTokens(user);

            var systemUserId = await GetSystemUserIdAsync();

            var recipient = GetInvitationRecipient(user);

            // Select the template to use
            var templateName = reregistration ? SystemNames.UserInvitationReregistrationMessage : SystemNames.UserInvitationMessage;

            await SendEmail(systemUserId, recipient, emailMessageTemplateName: templateName, tokensEmail: tokens, saveChanges: false);
        }

        /// <summary>
        /// Sends an email notification for an announcement.
        /// </summary>
        /// <param name="recipientEmail">The email address of the recipient.</param>
        /// <param name="emailSubject">The subject of the email.</param>
        /// <param name="templateName">The name of the email template to use.</param>
        /// <param name="tokens">Optional tokens to use in the template.</param>
        /// <param name="saveChanges">Whether to save changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendAnnouncementAsync(string recipientEmail, string emailSubject, string templateName, TokenList tokens = null, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(recipientEmail, nameof(recipientEmail));
            ArgumentNullException.ThrowIfNull(templateName, nameof(templateName));

            _logger.LogInformation("Queueing announcement email {EmailProperties} to be sent",
                new
                {
                    recipientEmail,
                    emailSubject,
                    templateName
                });

            try
            {
                tokens ??= new TokenList();
                tokens.Add("emailSubject", emailSubject);

                // Apply recipient override if needed
                var recipient = GetAnnouncementRecipient(recipientEmail);

                await _emailService.SendEmailByTemplateNameAsync(recipient, templateName, tokens, saveChanges: saveChanges);
            }
            catch (EntityNotFoundException)
            {
                throw new EntityNotFoundException(ApplicationErrors.MESSAGETEMPLATE_NOT_FOUND.ToErrorCode(), $"MessageTemplate '{templateName}' not found");
            }
        }

        /// <inheritdoc/>
        public async Task SendEmailAsync(SendEmailDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            Check.NotNullOrEmpty(model.RecipientEmailAddress, nameof(model.RecipientEmailAddress));
            Check.NotNullOrEmpty(model.Subject, nameof(model.Subject));
            Check.NotNullOrEmpty(model.Body, nameof(model.Body));

            model.Body = HttpUtility.HtmlEncode(model.Body);

            var allowedDomains = _trustOfficeOptions.AllowedDomains.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.ToLower()).ToArray();
            if (!allowedDomains.Any(domain => model.RecipientEmailAddress.EndsWith($"@{domain}", StringComparison.OrdinalIgnoreCase)))
            {
                throw new BadRequestException($"Recipient email address should end with one of {allowedDomains.Aggregate((s, s1) => $"{s}, {s1}")}");
            }

            if (model.LegalEntityId.HasValue && model.LegalEntityId.Value != Guid.Empty)
            {
                var entity = new LegalEntity(model.LegalEntityId.Value);
                await _systemAuditManager.AddMessageSentToEmployeeForCompanyActivityLogAsync(entity, model.RecipientEmailAddress, saveChanges: false);
            }

            // Add some information from the company to the body
            if (model.LegalEntityId.HasValue && model.LegalEntityId.Value != Guid.Empty)
            {
                var legalEntity = await _legalEntitiesDataManager.GetCompanyByIdAsync(model.LegalEntityId.Value);

                var sentByUserEmail = string.Empty;
                if (_workContext.User != null)
                {
                    sentByUserEmail = _workContext.User.Email;
                }

                var additionalInfo = "<p>\r\n" +
                                     $"<b>Entity Name</b>:&nbsp;{HttpUtility.HtmlEncode(legalEntity.Name)}<br>\r\n" +
                                     $"<b>Entity Code</b>:&nbsp;{HttpUtility.HtmlEncode(legalEntity.Code)}<br>\r\n" +
                                     $"<b>Master Client Code</b>:&nbsp;{HttpUtility.HtmlEncode(legalEntity.MasterClientCode)}<br>\r\n" +
                                     $"<b>Sent By</b>:&nbsp;{sentByUserEmail}<br>\r\n" +
                                     "</p>";

                model.Body += additionalInfo;
            }

            var recipient = new EmailRecipient { EmailAddress = model.RecipientEmailAddress, DisplayName = model.RecipientEmailAddress };
            string plainText = Regex.Replace(model.Body, "<.*?>", String.Empty).Replace("&nbsp;", " ", StringComparison.OrdinalIgnoreCase);
            await _emailService.SendEmailAsync(recipient, model.Subject, plainText, model.Body, saveChanges: true, documentIds: new List<Guid>());
        }

        /// <inheritdoc/>
        public async Task SendMFAResetConfirmationCodeAsync(Guid userId, string confirmationCode, TokenList tokens = null)
        {
            #region Checks and validations

            Check.NotNullOrWhiteSpace(confirmationCode, nameof(confirmationCode));
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
            var user = await _userManager.CheckUserByIdAsync(userId);

            #endregion

            tokens ??= new TokenList();
            new MessageTokenProvider(tokens).AddCommonTokens().AddUserTokens(user);

            tokens.Add(new Token("code", confirmationCode));

            var systemUserId = await GetSystemUserIdAsync();

            var systemRole = await _userManager.GetRoleByNameAsync(WellKnownRoleNames.System);

            await SendEmail(systemUserId, user.Email, null, SystemNames.MFAResetRequestMessage, tokens, saveChanges: true);
        }

        /// <summary>
        /// Sends an inbox message and an email simultaneously.
        /// </summary>
        /// <param name="fromUserId">The user ID of the sender.</param>
        /// <param name="recipientEmail">The email address of the recipient for sending an email.</param>
        /// <param name="emailMessageTemplateId">The ID of the email message template to use (optional).</param>
        /// <param name="emailMessageTemplateName">The name of the email message template to use (optional).</param>
        /// <param name="tokensEmail">Tokens to be used in the email message template (optional).</param>
        /// <param name="saveChanges">Indicates whether to save the changes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendEmail(Guid fromUserId, string recipientEmail,
                                    Guid? emailMessageTemplateId = null,
                                    string emailMessageTemplateName = null,
                                    TokenList tokensEmail = null,
                                    bool saveChanges = false)
        {
            _logger.LogInformation("Queueing email {EmailProperties} to be sent",
                new
                {
                    fromUserId,
                    recipientEmail,
                    emailMessageTemplateId,
                    emailMessageTemplateName
                });
            try
            {
                #region Send email

                if (emailMessageTemplateId != null)
                {
                    await _emailService.SendEmailByTemplateIdAsync(recipientEmail, (Guid)emailMessageTemplateId, tokensEmail, saveChanges: saveChanges);
                }
                else if (emailMessageTemplateName != null)
                {
                    await _emailService.SendEmailByTemplateNameAsync(recipientEmail, emailMessageTemplateName, tokensEmail, saveChanges: saveChanges);
                }

                #endregion
            }
            catch (EntityNotFoundException)
            {
                if (emailMessageTemplateId != null)
                {
                    throw new EntityNotFoundException(ApplicationErrors.MESSAGETEMPLATE_NOT_FOUND.ToErrorCode(), $"MessageTemplate with id '{emailMessageTemplateId}' not found");
                }
                else
                {
                    throw new EntityNotFoundException(ApplicationErrors.MESSAGETEMPLATE_NOT_FOUND.ToErrorCode(), $"MessageTemplate '{emailMessageTemplateName}' not found");
                }
            }
        }

        #region Utils

        /// <summary>
        /// Returns an IFormFile for the given document.
        /// </summary>
        /// <param name="document">The document entity.</param>
        /// <returns>IFormFile.</returns>
        [SuppressMessage("Performance", "CA1859:Use concrete types when possible for improved performance", Justification = "Intended")]
        private static IFormFile DocumentToFormFile(NetProGroup.Framework.Services.Documents.EFModels.Document document)
        {
            // ToDo: Get the actual data.
            var data = new byte[] { 0 };
            var stream = new MemoryStream(data);

            return new FormFile(stream, 0, data.Length, document.Filename, document.Filename);
        }

        /// <summary>
        /// Gets the id of one of the system users.
        /// </summary>
        /// <returns>Id of the system user.</returns>
        private async Task<Guid> GetSystemUserIdAsync()
        {
            var systemUser = await _userManager.GetUserByIdAsync(UserConsts.SystemUserId);

            if (systemUser == null)
            {
                throw new NotFoundException((int)ErrorEnum.USER_NOT_FOUND, ErrorEnum.USER_NOT_FOUND.ToString(), $"No system user found");
            }

            return systemUser.Id;
        }

        /// <summary>
        /// Checks if the given user exists.
        /// </summary>
        /// <param name="userId">Id of the user.</param>
        private async Task CheckUserExistsAsync(Guid userId)
        {
            if (_userExistsCache.Contains(userId))
            {
                return;
            }

            await _userManager.CheckUserByIdAsync(userId, true);
            _userExistsCache.Add(userId);
        }

        /// <summary>
        /// Gets the emailaddress to use for a production office.
        /// </summary>
        /// <param name="prefix">The prefix to use for the configuration key.</param>
        /// <param name="productionOfficeCode">The code of the production office to send the email to.</param>
        /// <returns>The email address to use.</returns>
        private async Task<string> GetProductionOfficeRecipientAsync(string prefix, string productionOfficeCode)
        {
            // Get the configured address
            var recipient = await _configurationManager.GetConfigurationAsync($"{prefix}.default", "");
            recipient = await _configurationManager.GetConfigurationAsync($"{prefix}.{productionOfficeCode.ToLower()}", recipient);

            if (!IsAllowedEmailDomain(recipient))
            {
                // Get the override and use if valid
                var overrideRecipient = _trustOfficeOptions.RecipientOverride.ProductionOffice;
                if (!string.IsNullOrEmpty(overrideRecipient))
                {
                    _logger.LogInformation("Production office email recipient {RecipientEmail} is not allowed, using override recipient {OverrideRecipient}", recipient, overrideRecipient);
                    return overrideRecipient;
                }

                _logger.LogWarning("Recipient email '{RecipientEmail}' is not allowed and no override recipient is configured", recipient);
                throw new BadRequestException($"Recipient email '{recipient}' is not allowed and no override recipient is configured");
            }

            return recipient;
        }

        private string GetInvitationRecipient(ApplicationUserDTO user)
        {
            if (!IsAllowedEmailDomain(user.Email))
            {
                // Get the override and use if valid
                var overrideRecipient = _trustOfficeOptions.RecipientOverride.Invitation;
                if (!string.IsNullOrEmpty(overrideRecipient))
                {
                    _logger.LogInformation("Invitation email recipient {RecipientEmail} is not allowed, using override recipient {OverrideRecipient}", user.Email, overrideRecipient);
                    return overrideRecipient;
                }

                _logger.LogWarning("Recipient email '{RecipientEmail}' is not allowed and no override recipient is configured", user.Email);
                throw new BadRequestException($"Recipient email '{user.Email}' is not allowed and no override recipient is configured");
            }

            return user.Email;
        }

        /// <summary>
        /// Gets the recipient email address to use for an announcement, applying override if needed.
        /// </summary>
        /// <param name="recipientEmail">The original recipient email address.</param>
        /// <returns>The email address to use.</returns>
        private string GetAnnouncementRecipient(string recipientEmail)
        {
            if (!IsAllowedEmailDomain(recipientEmail))
            {
                // Get the override and use if valid
                var overrideRecipient = _trustOfficeOptions.RecipientOverride.Announcement;
                if (!string.IsNullOrEmpty(overrideRecipient))
                {
                    _logger.LogInformation("Announcement email recipient {RecipientEmail} is not allowed, using override recipient {OverrideRecipient}", recipientEmail, overrideRecipient);
                    return overrideRecipient;
                }

                _logger.LogWarning("Recipient email '{RecipientEmail}' is not allowed and no override recipient is configured", recipientEmail);
                throw new BadRequestException($"Recipient email '{recipientEmail}' is not allowed and no override recipient is configured");
            }

            return recipientEmail;
        }

        private bool IsAllowedEmailDomain(string emailAddress)
        {
            if (string.IsNullOrEmpty(emailAddress) || !emailAddress.Contains('@', StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(_trustOfficeOptions.AllowedDomains))
            {
                return true;
            }

            var allowedDomains = _trustOfficeOptions.AllowedDomains.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.ToLower()).ToArray();
            var domain = emailAddress.Split('@').Last();

            return allowedDomains.Contains(domain.ToLower());
        }

        #endregion
    }
}