﻿// <copyright file="ICurrenciesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using <PERSON>.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Currencies
{
    /// <summary>
    /// Interface for managing currencies.
    /// </summary>
    public interface ICurrenciesAppService : IScopedService
    {
        /// <summary>
        /// Asynchronously retrieves all currencies with pagination and optional filters.
        /// </summary>
        /// <param name="name">The name of the currency to filter by. Optional.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paged list of currencies.</returns>
        Task<IPagedList<CurrencyDTO>> GetAllCurrenciesAsync(string name, int pageNumber, int pageSize);

        /// <summary>
        /// Creates or updates a currency.
        /// </summary>
        /// <param name="model">The data transfer object containing the currency details.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task CreateCurrencyAsync(CreateCurrencyDTO model);

        /// <summary>
        /// Deletes a currency by its ID.
        /// </summary>
        /// <param name="currencyId">The ID of the currency to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteCurrencyAsync(Guid currencyId);
    }
}
