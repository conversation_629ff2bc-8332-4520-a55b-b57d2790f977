﻿// <copyright file="ModulesProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The modules profile for AutoMapper.
    /// </summary>
    public class ModulesProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ModulesProfile"/> class.
        /// </summary>
        public ModulesProfile()
        {
            CreateMap<Module, ModuleDTO>()
                .ForMember(dest => dest.IsEnabled, opt => opt.Ignore());
        }
    }
}
