﻿// <copyright file="MasterClientDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Users;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClient.
    /// </summary>
    public class MasterClientDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets a collection of JurisdictionDTO.
        /// </summary>
        public IReadOnlyCollection<JurisdictionDTO> Jurisdictions { get; set; }

        /// <summary>
        /// Gets or sets a collection of Master Client Users that are owners (client users).
        /// </summary>
        public IReadOnlyCollection<ListUserDTO> MasterClientUsers { get; set; }

        /// <summary>
        /// Gets or sets a collection of Master Client Users that are managers (trident users).
        /// </summary>
        public IReadOnlyCollection<ListUserDTO> MasterClientManagers { get; set; }
    }
}
