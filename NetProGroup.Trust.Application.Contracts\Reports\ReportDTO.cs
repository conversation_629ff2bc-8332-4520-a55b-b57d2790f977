// <copyright file="ReportDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Reports
{
    /// <summary>
    /// Represents a report.
    /// </summary>
    public class ReportDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the report.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the report.
        /// </summary>
        public string ReportName { get; set; }

        /// <summary>
        /// Gets or sets the name of the file.
        /// </summary>
        public string Filename { get; set; }

        /// <summary>
        /// Gets or sets the type of the report.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the report was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}