// <copyright file="AnnouncementsService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Scheduling;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.DomainShared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Application.BackgroundServices
{
    /// <summary>
    /// Background service for announcements in the application.
    /// </summary>
    public class AnnouncementsService : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        private IServiceProvider _scopedServiceProvider;
        private IServiceScope _servicescope;

        /// <summary>
        /// Initializes a new instance of the <see cref="AnnouncementsService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The serviceprovider to get new scoped services on execution.</param>
        /// <param name="logger">The logger instance.</param>
        public AnnouncementsService(
            IServiceProvider serviceProvider,
            ILogger<AnnouncementsService> logger)
        {
            // Use the container
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <inheritdoc/>
#pragma warning disable CA1816 // Dispose methods should call SuppressFinalize
        public override void Dispose()
#pragma warning restore CA1816 // Dispose methods should call SuppressFinalize
        {
            if (_servicescope != null)
            {
                _servicescope.Dispose();
                _servicescope = null;
            }

            base.Dispose();
        }

        /// <inheritdoc/>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Delay before actually starting the service
            await Task.Delay(5000, stoppingToken);

            using var tickTimer = new PeriodicTimer(TimeSpan.FromSeconds(10));

            while (await tickTimer.WaitForNextTickAsync(stoppingToken))
            {
                _servicescope = _serviceProvider.CreateScope();
                _scopedServiceProvider = _servicescope.ServiceProvider;

                // Small random delay to get out of a possible race condition
#pragma warning disable CA5394 // Do not use insecure randomness
                await Task.Delay(new Random().Next(100, 1000), stoppingToken);
#pragma warning restore CA5394 // Do not use insecure randomness

                var repository = _scopedServiceProvider.GetRequiredService<IAnnouncementsRepository>();
                var dataManager = _scopedServiceProvider.GetRequiredService<IAnnouncementDataManager>();

                // Retrieve the announcements
                var announcements = (await repository.FindByConditionAsync(
                    a => a.Status == AnnouncementStatus.Scheduled && a.SentAt <= DateTime.UtcNow)).ToList();

                foreach (var announcement in announcements)
                {
#pragma warning disable CA1031 // Do not catch general exception types
                    try
                    {
                        // Send the announcement
                        await dataManager.SendAnnouncementAsync(announcement.Id, true);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Announcement with id '{AnnouncementId}' failed", announcement.Id);
                    }
#pragma warning restore CA1031 // Do not catch general exception types
                }

                if (announcements.Count == 0)
                {
                    tickTimer.Period = TimeSpan.FromSeconds(60);
                }
                else
                {
                    tickTimer.Period = TimeSpan.FromSeconds(1);
                }
            }
        }
    }
}
