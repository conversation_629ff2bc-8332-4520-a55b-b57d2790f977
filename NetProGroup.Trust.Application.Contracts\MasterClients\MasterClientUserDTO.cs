﻿// <copyright file="MasterClientUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClientUser.
    /// </summary>
    public class MasterClientUserDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the id of the user.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user was manually added (means 'manager' instead of 'owner').
        /// </summary>
        public bool IsManuallyAdded { get; set; }

        /// <summary>
        /// Gets or sets the firstname of the user.
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// Gets or sets the lastname of the user.
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// Gets or sets the emial address of the user.
        /// </summary>
        public string Email { get; set; }
    }
}
