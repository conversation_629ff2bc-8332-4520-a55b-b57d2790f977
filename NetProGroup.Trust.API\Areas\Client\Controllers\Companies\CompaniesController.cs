﻿// <copyright file="CompaniesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Companies
{
    /// <summary>
    /// Use this controller for company related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class CompaniesController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly ILegalEntitiesAppService _legalEntitiesAppService;
        private readonly IModulesAppService _modulesAppService;
        private readonly ISubmissionsAppService _submissionsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompaniesController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="legalEntitiesAppService">The service for companies (legal entities).</param>
        /// <param name="modulesAppService">The service for modules.</param>
        /// <param name="submissionsAppService">The submission app service.</param>
        public CompaniesController(
            ILogger<CompaniesController> logger,
            IConfiguration configuration,
            ILegalEntitiesAppService legalEntitiesAppService,
            IModulesAppService modulesAppService,
            ISubmissionsAppService submissionsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _legalEntitiesAppService = legalEntitiesAppService;
            _modulesAppService = modulesAppService;
            _submissionsAppService = submissionsAppService;
        }

        /// <summary>
        /// Gets the list of modules for the given company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/companies/{companyid}/modules
        ///
        /// Only the modules that are active and enabled for the company are returned (menu display purpose).
        /// </remarks>
        /// <param name="companyId">The id of the company to get the modules for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpGet("{companyId}/modules")]
        [SwaggerOperation(OperationId = "Client_GetCompanyModules")]
        [ProducesResponseType(typeof(ListModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyModules(Guid companyId)
        {
            ListCompanyModulesDTO item = null;

            var result = await ProcessRequestAsync(

                executeAsync: async () =>
                {
                    item = await _modulesAppService.GetCompanyModulesAsync(companyId, forClientUI: true);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of Beneficial Owners (BOs) for the given company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/companies/{companyid}/beneficial-owners.
        ///
        /// </remarks>
        /// <param name="companyId">The id of the company to get the BOs for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the BOs.</returns>
        [HttpGet("{companyId}/beneficial-owners")]
        [SwaggerOperation(OperationId = "Client_GetCompanyBeneficialOwners")]
        [ProducesResponseType(typeof(ListBeneficialOwnersDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyBeneficialOwners(Guid companyId)
        {
            ListBeneficialOwnersDTO item = null;

            var result = await ProcessRequestAsync<ListBeneficialOwnersDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                },

                executeAsync: async () =>
                {
                    item = new ListBeneficialOwnersDTO { BeneficialOwners = await _legalEntitiesAppService.ListBeneficialOwnersAsync(companyId, true, 1, int.MaxValue) };
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of Directors for the given company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/companies/{companyid}/directors.
        ///
        /// </remarks>
        /// <param name="companyId">The id of the company to get the Directors for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the BOs.</returns>
        [HttpGet("{companyId}/directors")]
        [SwaggerOperation(OperationId = "Client_GetCompanyDirectors")]
        [ProducesResponseType(typeof(ListDirectorsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyDirectors(Guid companyId)
        {
            ListDirectorsDTO item = null;

            var result = await ProcessRequestAsync<ListDirectorsDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                },

                executeAsync: async () =>
                {
                    item = new ListDirectorsDTO { Directors = await _legalEntitiesAppService.ListDirectorsAsync(companyId, true, 1, int.MaxValue) };
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts an assistance request for the company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/companies/{companyid}/assistance-request
        ///
        /// Possible types of request:
        ///
        ///   NoBeneficialOwner = 101
        ///   NoDirector = 201
        ///   NoShareholder = 301.
        ///
        /// </remarks>
        /// <param name="companyId">The id of the company to create an assistance request for.</param>
        /// <param name="request">The request holding the parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("{companyId}/assistance-request")]
        [SwaggerOperation(OperationId = "Client_RequestAssistance")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> RequestAssistance(Guid companyId, RequestAssistanceDTO request)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                    request.LegalEntityId = companyId;
                },
                executeAsync: async () =>
                {
                    await _legalEntitiesAppService.RequestAssistanceAsync(request);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts a new submission for the company based on the module and year.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/companies/{companyid}/modules/{moduleId}/submissions?year=2024.
        ///
        /// </remarks>
        /// <param name="companyId">The id of the company to create the submission for.</param>
        /// <param name="moduleId">The id of the module to create the submission for.</param>
        /// <param name="year">The year to create the submission for. Only for Nevis submissions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("{companyId}/modules/{moduleId}/submissions")]
        [SwaggerOperation(OperationId = "Client_CreateSubmission")]
        [ProducesResponseType(typeof(Application.Contracts.Submissions.SubmissionDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateSubmission(
            Guid companyId,
            Guid moduleId,
            int? year)
        {
            Application.Contracts.Submissions.SubmissionDTO item = null;

            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                    Check.NotDefaultOrNull<Guid>(moduleId, nameof(moduleId));
                },
                executeAsync: async () =>
                {
                    var model = new StartSubmissionDTO
                    {
                        LegalEntityId = companyId,
                        ModuleId = moduleId,
                        FinancialYear = year
                    };
                    item = await _submissionsAppService.StartSubmissionAsync(model);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of submissions for the given company for a specific module.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/companies/{companyid}/modules/{moduleId}/submissions
        /// .
        /// </remarks>
        /// <param name="companyId">The id of the company to get the submissions for.</param>
        /// <param name="moduleId">The id of the module to get the submissions for.</param>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet("{companyId}/modules/{moduleId}/submissions")]
        [SwaggerOperation(OperationId = "Client_GetCompanyModuleSubmissions")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyModuleSubmissions(
            Guid companyId,
            Guid moduleId,
            [FromQuery] ListSubmissionsRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(null,
                 validate: () => { },

                 executeAsync: async (pagingInfo) =>
                 {
                     return await _submissionsAppService.ListSubmissionsAsync(companyId, moduleId, request);
                 });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of years to show when about to start a submission.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/companies/{companyid}/modules/{moduleId}/submission-years.
        ///
        /// </remarks>
        /// <param name="companyId">The id of the company to create the submission for.</param>
        /// <param name="moduleId">The id of the module to create the submission for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpGet("{companyId}/modules/{moduleId}/submission-years")]
        [SwaggerOperation(OperationId = "Client_GetAvailableSubmissionYears")]
        [ProducesResponseType(typeof(AvailableSubmissionYearsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAvailableSubmissionYears(
            Guid companyId,
            Guid moduleId)
        {
            AvailableSubmissionYearsDTO item = null;

            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                    Check.NotDefaultOrNull<Guid>(moduleId, nameof(moduleId));
                },
                executeAsync: async () =>
                {
                    item = await _submissionsAppService.GetAvailableSubmissionYears(companyId, moduleId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
