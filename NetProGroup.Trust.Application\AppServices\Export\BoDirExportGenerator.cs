// <copyright file="BoDirExportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using ClosedXML.Excel;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Export;
using NetProGroup.Trust.DataManager.BoDir;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;

namespace NetProGroup.Trust.Application.AppServices.Export
{
    /// <summary>
    /// Implementation of the Beneficial Owner and Director (BO/Dir) export generator.
    /// </summary>
    public class BoDirExportGenerator : IBoDirExportGenerator
    {
        private readonly IBoDirDataManager _boDirDataManager;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirExportGenerator"/> class.
        /// </summary>
        /// <param name="boDirDataManager">The manager to use for BO/Dir operations.</param>
        /// <param name="mapper">The AutoMapper instance.</param>
        public BoDirExportGenerator(IBoDirDataManager boDirDataManager, IMapper mapper)
        {
            _boDirDataManager = boDirDataManager;
            _mapper = mapper;
        }

        /// <inheritdoc />
        public async Task<BoDirDownloadResponseDTO> GenerateAsync(SearchBoDirRequestDTO request, List<Guid> jurisdictionIDs)
        {
            Check.NotNull(request, nameof(request));

            using var workbook = new XLWorkbook();

            // Set the first page to page 1
            request.PageNumber = 1;

            // MAX 5000 rows requested
            request.PageSize = 5000;

            var searchRequest = _mapper.Map<SearchBoDirRequest>(request);
            searchRequest.AuthorizedJurisdictionIDs = jurisdictionIDs;

            var data = await _boDirDataManager.SearchBoDirsAsync(searchRequest);

            ModifyWorkbook(workbook, data);

            // Save modified workbook to a new FileStream stream
            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);
            return CreateResponse(modifiedStream);
        }

        private static void ModifyWorkbook(XLWorkbook workbook, IEnumerable<BoDirItemDTO> data)
        {
            var worksheet = workbook.AddWorksheet(1);
            SetHeaders(worksheet);
            int row = 2; // Starting row (assuming the first row is for headers)

            foreach (var item in data)
            {
                worksheet.Cell(row, 1).Value = item.LegalEntityName;

                worksheet.Cell(row, 2).Value = item.VPEntityNumber; // This column is empty

                worksheet.Cell(row, 3).Value = item.EntityPortalCode;

                worksheet.Cell(row, 4).Value = item.MasterClientCode;

                worksheet.Cell(row, 5).Value = item.ReferralOffice;

                worksheet.Cell(row, 6).Value = item.ProductionOffice;

                worksheet.Cell(row, 7).Value = item.DirectorName;

                worksheet.Cell(row, 8).Value = item.Position;

                worksheet.Cell(row, 9).Value = item.Status.ToString();

                worksheet.Cell(row, 10).Value = item.Specifics;

                worksheet.Cell(row, 11).Value = item.RequestUpdateDate;

                worksheet.Cell(row, 12).Value = item.ConfirmedDate;

                row++;
            }
        }

        private static void SetHeaders(IXLWorksheet worksheet)
        {
            worksheet.Cell(1, 1).Value = "Entity Name";
            worksheet.Cell(1, 2).Value = "VP Entity Number";
            worksheet.Cell(1, 3).Value = "Entity Portal Code";
            worksheet.Cell(1, 4).Value = "Master Client Code";
            worksheet.Cell(1, 5).Value = "Referral Office";
            worksheet.Cell(1, 6).Value = "Production Office";
            worksheet.Cell(1, 7).Value = "Director Name";
            worksheet.Cell(1, 8).Value = "Position";
            worksheet.Cell(1, 9).Value = "Status";
            worksheet.Cell(1, 10).Value = "Specifics";
            worksheet.Cell(1, 11).Value = "Request Update Date";
            worksheet.Cell(1, 12).Value = "Confirmed Date";
        }

        private static BoDirDownloadResponseDTO CreateResponse(MemoryStream stream)
        {
            return new BoDirDownloadResponseDTO
            {
                FileContent = stream,
                FileName = "BoDirList.xlsx",
                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                Extension = ".xlsx"
            };
        }
    }
}