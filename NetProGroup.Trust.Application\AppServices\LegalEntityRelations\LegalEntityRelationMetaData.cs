﻿// <copyright file="LegalEntityRelationMetaData.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations
{
    /// <summary>
    /// Represents the status of a LegalEntityRelation.
    /// </summary>
    public abstract class LegalEntityRelationMetaData
    {
        /// <summary>
        /// Gets or sets the status of the relation.
        /// </summary>
        public LegalEntityRelationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// Gets or sets when an update of the data is requested.
        /// </summary>
        public DateTime? UpdateRequestedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that created the update request.
        /// </summary>
        public Guid? UpdateRequestedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the type of update request for the relation.
        /// </summary>
        public LegalEntityRelationUpdateRequestType? UpdateRequestType { get; set; }

        /// <summary>
        /// Gets or sets the comments for the update request.
        /// </summary>
        public string UpdateRequestComments { get; set; }
    }
}