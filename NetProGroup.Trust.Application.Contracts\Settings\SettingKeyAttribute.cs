﻿// <copyright file="SettingKeyAttribute.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Setting key attribute, uses by DTO models to mark setting properties.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public sealed class SettingKeyAttribute : Attribute
    {
        private readonly string _key;

        /// <summary>
        /// Initializes a new instance of the <see cref="SettingKeyAttribute"/> class.
        /// </summary>
        /// <param name="key">Name of the setting key.</param>
        public SettingKeyAttribute(string key)
        {
            this._key = key;
        }

        /// <summary>
        /// Gets the setting key.
        /// </summary>
        public string Key
        {
            get { return _key; }
        }
    }
}
