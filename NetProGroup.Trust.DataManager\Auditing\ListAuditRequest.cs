// <copyright file="ListAuditRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.EFAuditing.EFModels;
using <PERSON>.PagedList;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Request model for listing audits.
    /// </summary>
    public class ListAuditRequest
    {
        /// <summary>
        /// Gets or sets the ID of the entity to get audits for.
        /// </summary>
        public Guid EntityId { get; set; }

        /// <summary>
        /// Gets or sets the page number.
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Gets or sets the page size.
        /// </summary>
        public int PageSize { get; set; } = 20;
    }
}
