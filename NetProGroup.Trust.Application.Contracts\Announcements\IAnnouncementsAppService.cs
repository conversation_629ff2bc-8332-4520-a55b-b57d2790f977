// <copyright file="IAnnouncementsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Interface for the announcements.
    /// </summary>
    public interface IAnnouncementsAppService : IScopedService
    {
        /// <summary>
        /// Retrieve the created announcements in a paginated response.
        /// </summary>
        /// <param name="data">The necessary data used to filter announcements.</param>
        /// <returns>A <see cref="ListAnnouncementDTO"/> representing the created entity.</returns>
        Task<IPagedList<ListAnnouncementDTO>> FilterAnnouncementsAsync(FilterAnnouncementsDTO data);

        /// <summary>
        /// Creates or updates an announcement entity.
        /// </summary>
        /// <param name="data">The necessary data used to create or update an announcement.</param>
        /// <returns>A <see cref="Guid"/> representing the created or updated entity Id.</returns>
        Task<Guid> CreateUpdateAnnouncementAsync(CreateUpdateAnnouncementDTO data);

        /// <summary>
        /// Retrieve an announcement by its id.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <returns>A <see cref="AnnouncementDTO"/> representing the announcement.</returns>
        Task<AnnouncementDTO> GetAnnouncementByIdAsync(Guid announcementId);

        /// <summary>
        /// Creates an announcement document entity.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="data">The necessary dataset used to create an announcement document.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CreateAnnouncementDocumentAsync(Guid announcementId, CreateAnnouncementDocumentDTO data);

        /// <summary>
        /// Deletes an announcement document entity.
        /// </summary>
        /// <param name="announcementDocumentId">The announcementDocument id as Guid.</param>
        /// <param name="uploadComplete">The value indicating whether the upload of documents is finished or not.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteAnnouncementDocumentAsync(Guid announcementDocumentId, bool uploadComplete);

        /// <summary>
        /// Deletes an announcement entity.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteAnnouncementAsync(Guid announcementId);
    }
}