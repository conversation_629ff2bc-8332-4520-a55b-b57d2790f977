﻿// <copyright file="ISettingsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Interface describing the AppSettings service.
    /// </summary>
    public interface ISettingsAppService : IScopedService
    {
        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given jurisdiction.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="jurisdictionId">Id of the jurisdiction that the settings are for.</param>
        /// <returns>True if successful, otherwise false.</returns>
        Task<bool> SaveSettingsForJurisdictionAsync<TSettings>(TSettings settings, Guid jurisdictionId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given master client.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="masterClientId">Id of the master client that the settings are for.</param>
        /// <returns>True if successfull, otherwise false.</returns>
        Task<bool> SaveSettingsForMasterClientAsync<TSettings>(TSettings settings, Guid masterClientId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given legal entity.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="legalEntityId">Id of the legal entity that the settings are for.</param>
        /// <returns>True if successfull, otherwise false.</returns>
        Task<bool> SaveSettingsForCompanyAsync<TSettings>(TSettings settings, Guid legalEntityId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given jurisdiction.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="jurisdictionId">Id of the jurisdiction that the settings are for.</param>
        /// <returns>Returns jurisdiction settings of type <see cref="IAttributedSettings"/>.</returns>
        Task<TSettings> ReadSettingsForJurisdictionAsync<TSettings>(Guid jurisdictionId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Reads the set of settings identified by the key for the given jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to get the settings for.</param>
        /// <param name="year">(Optional) year to retrieve from.</param>
        /// <returns>SettingsDTO.</returns>
        Task<SettingsDTO> ReadSettingsForJurisdictionAsync(Guid jurisdictionId, int? year = null);

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given masterclient.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="masterClientId">Id of the masterclient that the settings are for.</param>
        /// <returns>Returns master client settings of type <see cref="IAttributedSettings"/>.</returns>
        Task<TSettings> ReadSettingsForMasterClientAsync<TSettings>(Guid masterClientId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given legal entity.
        /// </summary>
        /// <typeparam name="TSettings">The type of the settings.</typeparam>
        /// <param name="legalEntityId">Id of the legal entity that the settings are for.</param>
        /// <returns>Returns company settings of type <see cref="IAttributedSettings"/>.</returns>
        Task<TSettings> ReadSettingsForCompanyAsync<TSettings>(Guid legalEntityId) where TSettings : IAttributedSettings;

        /// <summary>
        /// Reads the set of settings identified by the key for the given masterclient.
        /// </summary>
        /// <param name="masterClientId">Id of the masterclient to get the settings for.</param>
        /// <param name="key">Key to identify the type of settings.</param>
        /// <returns>Returns a mast client setting.</returns>
        Task<object> ReadSettingsForMasterClientAsync(Guid masterClientId, string key);

        /// <summary>
        /// Reads the set of settings identified by the key for the given company.
        /// </summary>
        /// <param name="legalEntityId">Id of the legalentity to get the settings for.</param>
        /// <returns>SettingsDTO.</returns>
        Task<SettingsDTO> ReadSettingsForCompanyAsync(Guid legalEntityId);

        /// <summary>
        /// Saves a set of settings for a jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction that the settings are for.</param>
        /// <param name="key">Key to identify the type of settings.</param>
        /// <param name="data">The data to save.</param>
        /// <returns>True if successfull, otherwise false.</returns>
        Task<bool> SaveSettingsForJurisdictionAsync(Guid jurisdictionId, string key, string data);

        /// <summary>
        /// Saves a set of settings for a master client.
        /// </summary>
        /// <param name="masterClientId">Id of the masterclient that the settings are for.</param>
        /// <param name="key">Key to identify the type of settings.</param>
        /// <param name="data">The data to save.</param>
        /// <returns>True if successfull, otherwise false.</returns>
        Task<bool> SaveSettingsForMasterClientAsync(Guid masterClientId, string key, string data);

        /// <summary>
        /// Saves a set of settings for a legal entity.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity that the settings are for.</param>
        /// <param name="key">Key to identify the type of settings.</param>
        /// <param name="data">The data to save.</param>
        /// <returns>True if successfull, otherwise false.</returns>
        Task<bool> SaveSettingsForCompanyAsync(Guid legalEntityId, string key, string data);

        /// <summary>
        /// Saves a set of settings for a jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <param name="settings">Instance of settings.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation returnin a bool.</returns>
        Task SaveSettingsForJurisdictionAsync(Guid jurisdictionId, SettingsDTO settings);

        /// <summary>
        /// Saves a set of settings for a company.
        /// </summary>
        /// <param name="legalEntityId">Id of the company.</param>
        /// <param name="settings">Instance of settings.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation returnin a bool.</returns>
        Task SaveSettingsForCompanyAsync(Guid legalEntityId, SettingsDTO settings);
    }
}