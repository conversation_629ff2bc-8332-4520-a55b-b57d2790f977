// <copyright file="UpdateCompanyAnnualFeeStatusDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Models
{
    /// <summary>
    /// Data transfer object for updating company annual fee status.
    /// </summary>
    public class UpdateCompanyAnnualFeeStatusDTO
    {
        /// <summary>
        /// Gets or sets the financial year for which the annual fee status is being updated.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the annual fee is paid.
        /// </summary>
        public bool IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the list of legal entity IDs for which to update the annual fee status.
        /// </summary>
        public List<Guid> LegalEntityIds { get; set; }
    }
}
