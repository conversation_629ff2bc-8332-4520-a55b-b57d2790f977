﻿// <copyright file="CreateMasterClientUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClient for create.
    /// </summary>
    public class CreateMasterClientUserDTO : EntityDTO
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the email address of the user to add.
        /// </summary>
        [EmailAddress]
        public string EmailAddress { get; set; }
    }
}
