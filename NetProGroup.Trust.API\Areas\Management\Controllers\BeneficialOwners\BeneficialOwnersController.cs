﻿// <copyright file="BeneficialOwnersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.BeneficialOwners
{
    /// <summary>
    /// Use this controller for beneficialowner related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/beneficial-owners")]
    public class BeneficialOwnersController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IManagementBeneficialOwnersAppService _beneficialOwnersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnersController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="beneficialOwnersAppService">The service for BeneficialOwners.</param>
        public BeneficialOwnersController(
            ILogger<BeneficialOwnersController> logger,
            IConfiguration configuration,
            IManagementBeneficialOwnersAppService beneficialOwnersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _beneficialOwnersAppService = beneficialOwnersAppService;
        }

        /// <summary>
        /// Gets the given BeneficialOwner.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/beneficial-owners/{beneficialownerId}
        /// .
        /// </remarks>
        /// <param name="beneficialownerId">The id of the beneficial owner to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerDTO"/>.</returns>
        [HttpGet("{beneficialownerId:guid}")]
        [SwaggerOperation(OperationId = "Management_GetBeneficialOwner")]
        [ProducesResponseType(typeof(BeneficialOwnerDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBeneficialOwner(Guid beneficialownerId)
        {
            BeneficialOwnerDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _beneficialOwnersAppService.GetBeneficialOwnerAsync(beneficialownerId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given BeneficialOwner with the current and the prior version.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/beneficial-owners/{relationId}/comparison
        /// .
        /// </remarks>
        /// <param name="beneficialownerId">The id of the beneficial owner to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerComparisonDTO"/>.</returns>
        [HttpGet("{beneficialownerId:guid}/comparison")]
        [SwaggerOperation(OperationId = "Management_GetBeneficialOwnerForComparison")]
        [ProducesResponseType(typeof(BeneficialOwnerComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBeneficialOwnerForComparison(Guid beneficialownerId)
        {
            BeneficialOwnerComparisonDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerComparisonDTO>(
                validate: () =>
                {
                },
            executeAsync: async () =>
            {
                item = await _beneficialOwnersAppService.GetBeneficialOwnerForComparisonAsync(beneficialownerId);
            },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
