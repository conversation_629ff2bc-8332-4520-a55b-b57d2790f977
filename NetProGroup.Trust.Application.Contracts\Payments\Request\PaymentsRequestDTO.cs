// <copyright file="PaymentsRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Payment reqest list DTO model.
    /// </summary>
    public class PaymentsRequestDTO
    {
        /// <summary>
        /// Gets or sets the search term for filtering payments.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the start date for filtering payments.
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Gets or sets the end date for filtering payments.
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Gets or sets the company ID for filtering payments.
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the payment status for filtering payments.
        /// </summary>
        public PaymentStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets the financial year for filtering payments.
        /// </summary>
        public int? FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the field name to sort the payments by. Defaults to "InvoiceDate".
        /// </summary>
        public string SortBy { get; set; } = "InvoiceDate";

        /// <summary>
        /// Gets or sets the sort order for payments. Defaults to "desc" (descending).
        /// </summary>
        public string SortOrder { get; set; } = "desc";

        /// <summary>
        /// Gets or sets the page number for pagination. Defaults to 1.
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Gets or sets the page size for pagination. Defaults to 10.
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
}