// <copyright file="IZipFileGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Application.Contracts.Tools
{
    /// <summary>
    /// Interface for zip file events.
    /// </summary>
    public interface IZipFileGenerator : IScopedService
    {
        /// <summary>
        /// Generate a zip file containing the selected documents.
        /// </summary>
        /// <param name="documentIds">A list of document ids as Guid.</param>
        /// <returns>The generated zip file as ZipFileDTO.</returns>
        Task<ZipFileDTO> GenerateZipFileAsync(List<Guid> documentIds);

        /// <summary>
        /// Generate a zip file containing the selected submission documents.
        /// </summary>
        /// <param name="submissions">A list of submissions.</param>
        /// <returns>The generated zip file as ZipFileDTO.</returns>
        Task<ZipFileDTO> GenerateZipFileAsync(List<SubmissionDTO> submissions);
    }
}