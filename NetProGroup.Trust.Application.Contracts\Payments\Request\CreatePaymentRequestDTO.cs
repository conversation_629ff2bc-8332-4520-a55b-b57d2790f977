// <copyright file="CreatePaymentRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Payments.Request
{
    /// <summary>
    /// Payment request DTO model.
    /// </summary>
    public class CreatePaymentRequestDTO
    {
        /// <summary>
        /// Gets or sets the legal entity ID.
        /// </summary>
        /// <value>The unique identifier for the legal entity involved in the payment.</value>
        [Required(ErrorMessage = "Legal Entity ID is required.")]
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the currency ID.
        /// </summary>
        /// <value>The unique identifier for the currency type used in the transaction.</value>
        [Required(ErrorMessage = "Currency ID is required.")]
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the list of invoice IDs associated with the transaction.
        /// </summary>
        /// <value>
        /// A list of <see cref="Guid"/> representing the unique identifiers of the invoices.
        /// </value>
        public List<Guid> InvoiceIds { get; set; }
    }
}