// <copyright file="IDateTimeProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.Common
{
    /// <summary>
    /// Provides an abstraction for accessing the current date and time.
    /// </summary>
    public interface IDateTimeProvider : IScopedService
    {
        /// <summary>
        /// Gets the current date and time in UTC.
        /// </summary>
        DateTime UtcNow { get; }

        /// <summary>
        /// Gets the current date and time in local time.
        /// </summary>
        DateTime Now { get; }

        /// <summary>
        /// Gets the current date.
        /// </summary>
        DateTime Today { get; }

        /// <summary>
        /// Gets the current date and time in Saint Kitts and Nevis time.
        /// </summary>
        DateTime NevisNow { get; }
    }
}