// <copyright file="TransactionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Transactions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Payments.Transaction
{
    /// <summary>
    /// Use this controller for payments  transaction related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/payments/{paymentId}/[controller]")]
    public class TransactionsController : TrustAPIControllerBase
    {
        private readonly IPaymentTransactionAppService _paymentTransactionAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="TransactionsController"/> class.
        /// </summary>
        /// <param name="paymentTransactionAppService">Service to manage payment transactions.</param>
        /// <param name="logger">The logger instance used for logging operations.</param>
        public TransactionsController(
            IPaymentTransactionAppService paymentTransactionAppService,
            ILogger<TransactionsController> logger) : base(logger)
        {
            _paymentTransactionAppService = paymentTransactionAppService;
        }

        /// <summary>
        /// Creates a new payment transaction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/payments/{paymentId}/transactions
        ///     {
        ///         "FirstName": "Juan",
        ///         "LastName": "Hey",
        ///         "Email": "<EMAIL>",
        ///         "Description": "Small Order",
        ///         "OrderId": "1234",
        ///         "PaymentRedirectUrl": "https://payments.com/form",
        ///         "CancelUrl": "https://example.com/cancel",
        ///         "CompanyName": "Demo Inc",
        ///         "PhoneNumber": "+**********",
        ///         "MerchantEmail": "<EMAIL>"
        ///     }
        ///
        /// Sample response:
        ///
        ///     {
        ///         "paymentProcessorResponse": {
        ///             "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
        ///             "providerTransactionId": "tx_123",
        ///             "callBackUrl": "https://example.com/callback",
        ///             "result": 0,
        ///             "resultText": "Approved",
        ///             "resultNumber": 200,
        ///             "provider": {
        ///                 // Provider details
        ///             }
        ///         }
        ///     }.
        /// </remarks>
        /// <param name="paymentId">The unique identifier for the payment.</param>
        /// <param name="createTransactionRequestDto">The model containing details for creating the payment transaction, including customer information.</param>
        /// <returns>The created payment transaction response.</returns>
        /// <response code="201">Returns the created payment transaction response.</response>
        /// <response code="400">If the request is invalid.</response>
        /// <response code="401">If the caller is not authenticated.</response>
        /// <response code="403">If the caller is not authorized to perform this operation.</response>
        [HttpPost]
        [SwaggerOperation(OperationId = "Client_CreatePaymentTransaction")]
        [ProducesResponseType(typeof(CreateTransactionResponseDTO), StatusCodes.Status201Created)]
        public async Task<IActionResult> CreatePaymentTransaction([FromRoute] Guid paymentId, [FromBody] CreateTransactionRequestDTO createTransactionRequestDto)
        {
            CreateTransactionResponseDTO createdTransaction = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(createTransactionRequestDto, nameof(createTransactionRequestDto));

                    // Add other validation checks here as needed
                },
                executeAsync: async () =>
                {
                    createTransactionRequestDto.PaymentId = paymentId;
                    createdTransaction = await _paymentTransactionAppService.AddPaymentTransactionAsync(createTransactionRequestDto);
                },
                createResponseModel: () =>
                {
                    return createdTransaction;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Submits a payment for processing based on the provided details in the request body.
        /// </summary>
        /// <remarks>
        /// This endpoint processes a payment by submitting a request containing the transaction details, including the `TransactionId` and `TokenId`.
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/client/payments/{paymentId}/transactions/{transactionId}/submit
        ///     {
        ///         "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
        ///         "tokenId": "token_abc123"
        ///     }
        ///
        /// Sample response (Status 200 OK):
        ///
        ///     {
        ///         "transactionId": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
        ///         "providerTransactionId": "txn_987654",
        ///         "result": 1,
        ///         "resultText": "Approved",
        ///         "resultNumber": 1001
        ///     }.
        ///
        /// </remarks>
        /// <param name="transactionId">The transaction id.</param>
        /// <param name="submitPaymentRequestDto">
        /// The model containing payment submission details such as the `TransactionId` (guid of the payment transaction) and the `TokenId` (token received from the payment provider).
        /// </param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result contains:
        /// <list type="bullet">
        /// <item><description>An <see cref="IActionResult"/> with <see cref="SubmitPaymentResponseDTO"/> on success.</description></item>
        /// <item><description>A 400 status code if the request contains invalid data.</description></item>
        /// <item><description>A 401 status code if the client is unauthorized.</description></item>
        /// <item><description>A 403 status code if the client is forbidden from submitting the payment.</description></item>
        /// </list>
        /// </returns>
        /// <response code="200">The payment was successfully submitted and processed by the provider.</response>
        /// <response code="400">The request contains invalid or missing payment data (e.g., missing TransactionId or TokenId).</response>
        /// <response code="401">Unauthorized access. The client must authenticate to submit the payment.</response>
        /// <response code="403">Client is not permitted to submit the payment, typically due to insufficient permissions.</response>
        [HttpPost("{transactionId}/Submit")]
        [SwaggerOperation(OperationId = "Client_SubmitPaymentTransaction")]
        [ProducesResponseType(typeof(SubmitPaymentResponseDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitPayment([FromRoute] Guid transactionId, [FromBody] SubmitPaymentRequestDTO submitPaymentRequestDto)
        {
            SubmitPaymentResponseDTO paymentResponse = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(submitPaymentRequestDto, nameof(submitPaymentRequestDto));

                    // Add additional validation logic for submitPaymentRequestDto here if needed
                },
                executeAsync: async () =>
                {
                    submitPaymentRequestDto.TransactionId = transactionId;
                    paymentResponse = await _paymentTransactionAppService.SubmitPaymentTransactionAsync(submitPaymentRequestDto);
                },
                createResponseModel: () =>
                {
                    return paymentResponse;
                });

            return result.AsResponse();
        }
    }
}