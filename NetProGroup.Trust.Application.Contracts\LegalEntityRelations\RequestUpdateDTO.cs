﻿// <copyright file="RequestUpdateDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations
{
    /// <summary>
    /// Model for requesting an update.
    /// </summary>
    public class RequestUpdateDTO
    {
        /// <summary>
        /// Gets or sets the unique relation id.
        /// </summary>
        public string UniqueRelationId { get; set; }

        /// <summary>
        /// Gets or sets the type of requested update.
        /// </summary>
        public LegalEntityRelationUpdateRequestType UpdateRequestType { get; set; }

        /// <summary>
        /// Gets or sets the comments for the update request.
        /// </summary>
        public string UpdateRequestComments { get; set; }
    }
}
