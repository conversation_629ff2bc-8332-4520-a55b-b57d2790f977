﻿// <copyright file="AddActivityLogDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Audits
{
    /// <summary>
    /// DTO model for adding an ActivityLog.
    /// </summary>
    public class AddActivityLogDTO
    {
        /// <summary>
        /// Gets or sets the type of activity to log.
        /// </summary>
        public virtual string ActivityType { get; set; }

        /// <summary>
        /// Gets or sets the optional short description.
        /// </summary>
        /// <remarks>
        /// If not given, the text is used and truncated if needed.
        /// For ActivityType user.authenticate and user.logout, an internal description is generated.
        /// </remarks>
        public virtual string ShortDescription { get; set; }

        /// <summary>
        /// Gets or sets the text for the log.
        /// </summary>
        /// <remarks>
        /// For ActivityType user.authenticate and user.logout, internal text is generated.
        /// </remarks>
        public virtual string Text { get; set; }
    }
}
