﻿// <copyright file="AttributeCollectionStartResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
    public class AttributeCollectionStartResponse
    {
        public AttributeCollectionStartResponse()
        {
            data = new AttributeCollectionStartResponse_Data();
            data.odatatype = "microsoft.graph.onAttributeCollectionStartResponseData";

            this.data.actions = new List<AttributeCollectionStartResponse_Action>();
            this.data.actions.Add(new AttributeCollectionStartResponse_Action());
        }

        [JsonPropertyName("data")]
        public AttributeCollectionStartResponse_Data data { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionStartResponse_Data
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        public List<AttributeCollectionStartResponse_Action> actions { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionStartResponse_Action
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string message { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public AttributeCollectionStartResponse_Inputs inputs { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionStartResponse_Inputs
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("displayName")]
        public string DisplayName { get; set; }
    }

#pragma warning disable SA1204 // Static elements should appear before instance elements
#pragma warning disable SA1402 // File may only contain a single type
    public static class AttributeCollectionStartResponse_ActionTypes
#pragma warning restore SA1402 // File may only contain a single type
#pragma warning restore SA1204 // Static elements should appear before instance elements
    {
        public const string SetPrefillValues = "microsoft.graph.attributeCollectionStart.setPrefillValues";
        public const string ContinueWithDefaultBehavior = "microsoft.graph.attributeCollectionStart.continueWithDefaultBehavior";
        public const string ShowBlockPage = "microsoft.graph.attributeCollectionStart.showBlockPage";
    }
#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}