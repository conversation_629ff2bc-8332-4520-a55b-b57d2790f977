﻿// <copyright file="CurrenciesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Currencies;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Currencies
{
    /// <summary>
    /// Controller for Currencies.
    /// </summary>
    [ApiController]
    [AllowAnonymous]
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class CurrenciesController : TrustAPIControllerBase
    {
        private readonly ILogger<CurrenciesController> _logger;
        private readonly ICurrenciesAppService _currenciesAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="CurrenciesController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="currenciesAppService">The service for currencies.</param>
        public CurrenciesController(
            ILogger<CurrenciesController> logger,
            ICurrenciesAppService currenciesAppService)
            : base(logger)
        {
            _logger = logger;
            _currenciesAppService = currenciesAppService;
        }

        /// <summary>
        /// Gets all currencies with pagination and optional filters.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/currencies?name={name}&amp;pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="name">The name of the currency to filter by. Optional.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with currencies.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetAllCurrencies", Summary = "Get all currencies with pagination and optional filters.")]
        [ProducesResponseType(typeof(PaginatedResponse<CurrencyDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllCurrencies(
            string name,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },
                executeAsync: async (pagingInfo) =>
                {
                    return await _currenciesAppService.GetAllCurrenciesAsync(name, pageNumber, pageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Creates a currency.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/management/currencies
        ///     {
        ///         "name": "Dollar",
        ///         "code": "USD",
        ///         "symbol": "$"
        ///     }.
        /// </remarks>
        /// <param name="model">The data transfer object containing the currency details.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "CreateCurrency", Summary = "Creates A currency.")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateCurrency([FromBody] CreateCurrencyDTO model)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotNullOrEmpty(model.Name, nameof(model.Name));
                    Check.NotNullOrEmpty(model.Code, nameof(model.Code));
                },
                executeAsync: async () =>
                {
                    await _currenciesAppService.CreateCurrencyAsync(model);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Deletes a currency by its ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE /api/management/currencies/{id}.
        /// </remarks>
        /// <param name="id">The ID of the currency to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpDelete("{id}")]
        [SwaggerOperation(OperationId = "DeleteCurrency", Summary = "Deletes a currency by its ID.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> DeleteCurrency(Guid id)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(id, nameof(id));
                },
                executeAsync: async () =>
                {
                    await _currenciesAppService.DeleteCurrencyAsync(id);
                });

            return result.AsNoContentResponse();
        }
    }
}
