﻿// <copyright file="IJurisdictionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Forms;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Jurisdictions
{
    /// <summary>
    /// Interface for the Jurisdiction AppService.
    /// </summary>
    public interface IJurisdictionsAppService : IScopedService
    {
        /// <summary>
        /// Creates a jurisdiction.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<JurisdictionDTO> CreateJurisdictionAsync(CreateJurisdictionDTO model);

        /// <summary>
        /// Updates a jurisdiction.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<JurisdictionDTO> UpdateJurisdictionAsync(JurisdictionDTO model);

        /// <summary>
        /// Gets the paged list of jurisdictions.
        /// </summary>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the jurisdictions as a paged list.</returns>
        Task<IPagedList<JurisdictionDTO>> GetJurisdictionsAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Gets a list of form templates for the given jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to get the form templates for.</param>
        /// <returns>A <see cref="Task{ListFormTemplatesDTO}"/> representing the asynchronous operation. The task result contains the form templates for the given jurisdiction.</returns>
        Task<ListFormTemplatesDTO> GetFormTemplatesByJurisdictionAsync(Guid jurisdictionId);
    }
}
