﻿// <copyright file="IBeneficialOwnersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners
{
    /// <summary>
    /// Interface for the BeneficialOwners AppService.
    /// </summary>
    public interface IBeneficialOwnersAppService : IScopedService
    {
        /// <summary>
        /// Gets a paged list with BeneficialOwners.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the beneficial owners as a paged list.</returns>
        Task<IPagedList<BeneficialOwnerDTO>> ListBeneficialOwnersAsync(Guid legalEntityId, int pageNumber, int pageSize);

        /// <summary>
        /// Gets the current version of a particular BeneficialOwner using the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The beneficial owner id.</param>
        /// <returns>The found beneficial owner DTO.</returns>
        Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(string uniqueRelationId);

        /// <summary>
        /// Gets both current and prior version of a particular BeneficialOwner using the unique relation id so they can be compared.
        /// </summary>
        /// <param name="uniqueRelationId">The beneficial owner id.</param>
        /// <returns>A beneficial owner comparison object.</returns>
        Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(string uniqueRelationId);

        /// <summary>
        /// Confirmation of the data for the BeneficialOwner with the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The beneficial owner id.</param>
        /// <returns>The confirmed beneficial owner DTO.</returns>
        Task<BeneficialOwnerDTO> SetConfirmationAsync(string uniqueRelationId);

        /// <summary>
        /// Request for updating the data of the BeneficialOwner with the unique relation id.
        /// </summary>
        /// <param name="requestUpdate">The model holding the parameters for the update request.</param>
        /// <returns>The updated beneficial owner DTO.</returns>
        Task<BeneficialOwnerDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate);

        /// <summary>
        /// Request for assistance related to BeneficialOwners.
        /// </summary>
        /// <param name="requestAssistance">The model holding the parameters for the assistance request.</param>
        /// <returns>An awaitable task.</returns>
        Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance);

        /// <summary>
        /// Simulates the update of a beneficial owner name.
        /// </summary>
        /// <param name="uniqueRelationId">The beneficial owner id.</param>
        /// <param name="newName">The new name.</param>
        /// <returns>An awaitable task.</returns>
        Task SimulateUpdateSync(string uniqueRelationId, string newName);
    }
}
