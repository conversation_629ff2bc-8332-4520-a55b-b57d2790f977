﻿// <copyright file="IModulesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Interface for the Modules AppService.
    /// </summary>
    public interface IModulesAppService : IScopedService
    {
        /// <summary>
        /// Gets a collection of all modules for the jurisdiction including the 'enabled' and 'active' status.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <returns>A <see cref="Task{ListModulesDTO}"/> representing the asynchronous operation with the ListModulesDTO as response.</returns>
        Task<ListModulesDTO> GetJurisdictionModulesAsync(Guid jurisdictionId);

        /// <summary>
        /// Gets a collection of all modules status for the company including the 'enabled' and 'active' status.
        /// </summary>
        /// <param name="companyId">Id of the company.</param>
        /// <param name="forClientUI">Indicates whether only the list with active and enabled modules must be returned for the menu in the client UI.</param>
        /// <returns>A <see cref="Task{ListModulesDTO}"/> representing the asynchronous operation with the ListModulesDTO as response.</returns>
        Task<ListCompanyModulesDTO> GetCompanyModulesAsync(Guid companyId, bool forClientUI);

        /// <summary>
        /// Gets a collection of all modules including the 'active' status.
        /// </summary>
        /// <param name="isActive">Indicates whether to get only active/inactive or all modules.</param>
        /// <returns>A <see cref="Task{ListModulesDTO}"/> representing the asynchronous operation with the ListModulesDTO as response.</returns>
        Task<ListModulesDTO> GetAllModulesAsync(bool? isActive = null);

        /// <summary>
        /// Sets the available modules for the given jurisdiction and returns the final result.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <param name="modules">The collection of ModuleDTOs that are available for the jurisdiction.</param>
        /// <returns>A <see cref="Task{ListModulesDTO}"/> representing the asynchronous operation with the ListModulesDTO as response.</returns>
        Task<ListModulesDTO> SetJurisdictionModulesAsync(Guid jurisdictionId, IReadOnlyCollection<SetModuleDTO> modules);

        /// <summary>
        /// Sets the available modules for the given jurisdiction and returns the final result.
        /// </summary>
        /// <param name="companyId">Id of the company.</param>
        /// <param name="modules">The collection of ModuleDTOs that are available for the company.</param>
        /// <returns>A <see cref="Task{ListModulesDTO}"/> representing the asynchronous operation with the ListModulesDTO as response.</returns>
        Task<ListCompanyModulesDTO> SetCompanyModulesAsync(Guid companyId, IReadOnlyCollection<SetCompanyModuleDTO> modules);
    }
}