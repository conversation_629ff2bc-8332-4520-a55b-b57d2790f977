// <copyright file="CreateRFIDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represent the necessary data used to create a request for information.
    /// </summary>
    public class CreateRFIDTO
    {
        /// <summary>
        /// Gets or sets the submission id as Guid.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the deadline date for the request for information.
        /// </summary>
        public DateTime DeadLine { get; set; }

        /// <summary>
        /// Gets or sets the comments for the request for information.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the rfi will include documents.
        /// </summary>
        /// <remarks>
        /// If the value is true the rfi status will be set to Draft.
        /// </remarks>
        public bool IncludeAttachments { get; set; }
    }
}