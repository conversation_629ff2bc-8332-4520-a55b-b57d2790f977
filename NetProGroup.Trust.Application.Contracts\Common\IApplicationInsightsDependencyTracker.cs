// <copyright file="IApplicationInsightsDependencyTracker.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.Common
{
    /// <summary>
    /// Interface for tracking dependencies in Application Insights.
    /// </summary>
    public interface IApplicationInsightsDependencyTracker : IScopedService
    {
        /// <summary>
        /// Tracks a dependency operation and returns its result.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="operation">The operation to track.</param>
        /// <param name="type">The type of dependency (e.g., "Email", "Background", "Database").</param>
        /// <param name="target">The target system (e.g., "SendGrid", "SMTP", "SQL Server").</param>
        /// <param name="data">Additional data about the operation.</param>
        /// <returns>The result of the operation.</returns>
        Task<T> TrackDependencyAsync<T>(
            string operationName,
            Func<Task<T>> operation,
            string type = null,
            string target = null,
            string data = null);

        /// <summary>
        /// Tracks a dependency operation that doesn't return a result.
        /// </summary>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="operation">The operation to track.</param>
        /// <param name="type">The type of dependency (e.g., "Email", "Background", "Database").</param>
        /// <param name="target">The target system (e.g., "SendGrid", "SMTP", "SQL Server").</param>
        /// <param name="data">Additional data about the operation.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task TrackDependencyAsync(
            string operationName,
            Func<Task> operation,
            string type = null,
            string target = null,
            string data = null);
    }
}