// <copyright file="BoDirController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.BoDir;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.BoDir
{
    /// <summary>
    /// Controller for Beneficial Owner and Director (BO/Dir) related operations.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/bodir")]
    public class BoDirController : TrustAPIControllerBase
    {
        private readonly IBoDirAppService _boDirAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="boDirAppService">The service for BO/Dir operations.</param>
        public BoDirController(
            ILogger<BoDirController> logger,
            IBoDirAppService boDirAppService)
            : base(logger)
        {
            _boDirAppService = boDirAppService;
        }

        /// <summary>
        /// Gets the list of Beneficial Owners and Directors that match the criteria.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/bodir.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the BO/Dir entries.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_ListBoDirs")]
        [ProducesResponseType(typeof(PaginatedResponse<BoDirItemDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ListBoDirs([FromQuery] SearchBoDirRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                },

                executeAsync: async _ => await _boDirAppService.SearchBoDirsAsync(request));

            return result.AsResponse();
        }

        /// <summary>
        /// Exports a list of beneficial owners and directors based on the provided search criteria.
        /// </summary>
        /// <param name="request">
        /// An instance of <see cref="SearchBoDirRequestDTO"/> containing the search parameters to filter
        /// the beneficial owners and directors to be exported. This is provided as a query parameter.
        /// </param>
        /// <remarks>
        /// This endpoint allows exporting a list of beneficial owners and directors based on the specified criteria.
        /// The user's identity and permissions are validated before executing the operation.
        /// </remarks>
        /// <returns>A <see cref="FileContentResult"/> containing the file with appropriate content type and file name if the download is successful. Returns an appropriate response code if the request is unauthorized or forbidden.</returns>
        [HttpGet("download")]
        [SwaggerOperation(OperationId = "Management_DownloadBoDirList")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DownloadList([FromQuery] SearchBoDirRequestDTO request)
        {
            BoDirDownloadResponseDTO item = null;
            var result = await ProcessRequestAsync<BoDirDownloadResponseDTO>(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                },
                executeAsync: async () =>
                {
                    item = await _boDirAppService.GenerateBoDirRSearchResultsExportAsync(request);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }
    }
}
