﻿// <copyright file="CreateJurisdictionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Jurisdictions
{
    /// <summary>
    /// Create Jurisdiction DTO.
    /// </summary>
    public class CreateJurisdictionDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateJurisdictionDTO"/> class.
        /// </summary>
        public CreateJurisdictionDTO()
        {
        }

        /// <summary>
        /// Gets or sets the name of the jurisdictions.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the jurisdictions.
        /// </summary>
        public string Code { get; set; }
    }
}
