﻿// <copyright file="AnnualFeeDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// DTO holding the the information for an annual fee.
    /// </summary>
    public class AnnualFeeDTO
    {
        /// <summary>
        /// Gets or sets the financial year.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the fee for the finacial period is paid.
        /// </summary>
        public bool IsPaid { get; set; }
    }
}
