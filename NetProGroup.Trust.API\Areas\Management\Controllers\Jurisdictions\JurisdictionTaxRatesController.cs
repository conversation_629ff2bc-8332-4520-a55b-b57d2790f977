// <copyright file="JurisdictionTaxRatesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Jurisdictions
{
    /// <summary>
    /// Controller for Jurisdiction Tax Rates.
    /// </summary>
    [ApiController]
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/jurisdiction-tax-rates")]
    public class JurisdictionTaxRatesController : TrustAPIControllerBase
    {
        private readonly ILogger<JurisdictionTaxRatesController> _logger;
        private readonly IJurisdictionTaxRatesAppService _jurisdictionTaxRatesAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionTaxRatesController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="jurisdictionTaxRatesAppService">The service for jurisdiction tax rates.</param>
        public JurisdictionTaxRatesController(
            ILogger<JurisdictionTaxRatesController> logger,
            IJurisdictionTaxRatesAppService jurisdictionTaxRatesAppService)
            : base(logger)
        {
            _logger = logger;
            _jurisdictionTaxRatesAppService = jurisdictionTaxRatesAppService;
        }

        /// <summary>
        /// Gets tax rates for a specific jurisdiction with pagination.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/jurisdiction-tax-rates?jurisdictionId={jurisdictionId}?pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="jurisdictionId">The ID of the jurisdiction.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with tax rates for the jurisdiction.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetTaxRatesByJurisdiction", Summary = "Get tax rates for a specific jurisdiction with pagination.")]
        [ProducesResponseType(typeof(PaginatedResponse<JurisdictionTaxRateDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTaxRatesByJurisdiction(Guid jurisdictionId,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },
                executeAsync: async (pagingInfo) =>
                {
                    return await _jurisdictionTaxRatesAppService.GetTaxRatesByJurisdictionAsync(jurisdictionId, pageNumber, pageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Creates or updates a tax rate for a specific jurisdiction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/management/jurisdiction-tax-rates
        ///     {
        ///         "jurisdictionId": "Guid",
        ///         "rate": 0.10,
        ///         "startDate": "2023-01-01T00:00:00Z"
        ///     }.
        /// </remarks>
        /// <param name="model">The data transfer object containing the tax rate details.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "CreateOrUpdateTaxRate", Summary = "Creates or updates a tax rate for a specific jurisdiction.")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateOrUpdateTaxRate([FromBody] CreateOrUpdateTaxRateDTO model)
        {
            var result = await ProcessRequestAsync<object>(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(model.JurisdictionId, nameof(model.JurisdictionId));
                    Check.Positive(model.TaxRate, nameof(model.TaxRate));
                    Check.NotDefaultOrNull<DateTime>(model.StartDate, nameof(model.StartDate));
                },
                executeAsync: async () =>
                {
                    await _jurisdictionTaxRatesAppService.CreateOrUpdateTaxRateAsync(model);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Deletes a tax rate by its ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE /api/v1/management/jurisdiction-tax-rates/{id}.
        /// </remarks>
        /// <param name="jurisdictionTaxRateId">The ID of the tax rate to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpDelete("{jurisdictionTaxRateId}")]
        [SwaggerOperation(OperationId = "DeleteTaxRate", Summary = "Deletes a tax rate by its ID.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> DeleteTaxRate(Guid jurisdictionTaxRateId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionTaxRateId, nameof(jurisdictionTaxRateId));
                },
                executeAsync: async () =>
                {
                    await _jurisdictionTaxRatesAppService.DeleteTaxRateAsync(jurisdictionTaxRateId);
                });

            return result.AsNoContentResponse();
        }
    }
}
