// <copyright file="UsersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.TermsConditions
{
    /// <summary>
    /// Controller for managing Terms and Conditions acceptance status.
    /// </summary>
    [ApiController]
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/users")]
    public class UsersController : TrustAPIControllerBase
    {
        private readonly IUsersAppService _usersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UsersController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="usersAppService">Instance of UsersAppService.</param>
        public UsersController(
            ILogger<UsersController> logger,
            IUsersAppService usersAppService)
            : base(logger)
        {
            _usersAppService = usersAppService;
        }

        /// <summary>
        /// Gets the Terms and Conditions acceptance status for a user.
        /// </summary>
        /// <remarks>
        /// Returns the acceptance status and timestamp of when Terms and Conditions were accepted.
        ///
        /// Sample request:
        ///
        ///     GET /api/client/users/{userId}/terms-and-conditions-status.
        /// </remarks>
        /// <param name="userId">ID of the user to get the status for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the acceptance status.</returns>
        [HttpGet("{userId}/terms-and-conditions-status")]
        [SwaggerOperation(OperationId = "GetTermsConditionsStatus")]
        [ProducesResponseType(typeof(TermsConditionsStatusDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetStatus(Guid userId)
        {
            TermsConditionsStatusDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.GetTermsConditionsStatusAsync(userId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Sets the Terms and Conditions acceptance status for a user.
        /// </summary>
        /// <remarks>
        /// Updates the acceptance status and records the timestamp.
        ///
        /// Sample request:
        ///
        ///     PUT /api/client/users/{userId}/terms-and-conditions-status.
        /// </remarks>
        /// <param name="userId">Id of the user accepting the terms.</param>
        /// <param name="model">The acceptance model containing version information.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{userId}/terms-and-conditions-status")]
        [SwaggerOperation(OperationId = "AcceptTermsConditions")]
        [ProducesResponseType(typeof(AcceptTermsConditionsResultDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Accept(Guid userId, AcceptTermsConditionsDTO model)
        {
            await ProcessRequestAsync<AcceptTermsConditionsResultDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                    Check.NotNull(model, nameof(model));
                    Check.NotNullOrEmpty(model.Version, nameof(model.Version));
                },

                executeAsync: async () =>
                {
                    await _usersAppService.AcceptTermsConditionsAsync(userId, model);
                });

            return NoContent();
        }
    }
}
