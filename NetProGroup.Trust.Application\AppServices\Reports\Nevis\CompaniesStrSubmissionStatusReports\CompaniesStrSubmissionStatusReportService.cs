// <copyright file="CompaniesStrSubmissionStatusReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.CompaniesStrSubmissionStatusReports
{
    /// <summary>
    /// Service for generating "companies without submissions" report.
    /// </summary>
    public class CompaniesStrSubmissionStatusReportService : ICompaniesStrSubmissionStatusReportService
    {
        private readonly ICompaniesStrSubmissionStatusReportGenerator _companiesStrSubmissionStatusReportGenerator;
        private readonly IReportsDataManager _reportDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly ILockManager _lockManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompaniesStrSubmissionStatusReportService"/> class.
        /// </summary>
        /// <param name="companiesStrSubmissionStatusReportGenerator">The financial report generator.</param>
        /// <param name="reportDataManager">The report data manager.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="lockManager">The lock manager.</param>
        public CompaniesStrSubmissionStatusReportService(ICompaniesStrSubmissionStatusReportGenerator companiesStrSubmissionStatusReportGenerator,
            IReportsDataManager reportDataManager,
            IDocumentManager documentManager,
            ILockManager lockManager)
        {
            _companiesStrSubmissionStatusReportGenerator = companiesStrSubmissionStatusReportGenerator;
            _reportDataManager = reportDataManager;
            _documentManager = documentManager;
            _lockManager = lockManager;
        }

        /// <inheritdoc/>
        public async Task GenerateReportAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock);
            var jobLockId = Check.NotDefaultOrNull(jobLock.Id, nameof(jobLock));

            await _lockManager.RefreshLockAsync(jobLockId);

            var fileTemplate = _companiesStrSubmissionStatusReportGenerator.GenerateReportNameForTodayAsync();
            var fileName = $"{fileTemplate}.xlsx";

            if (await _reportDataManager.ReportExists(new Report
            {
                ReportName = fileTemplate,
                Type = ReportType.CompaniesWithoutSubmissions
            }))
            {
                return;
            }

            var reportOutput = await _companiesStrSubmissionStatusReportGenerator.GenerateReportAsync();

            var documentId = await _documentManager.CreateDocumentAsync(
                documentType: (int)ReportType.CompaniesWithoutSubmissions,
                description: fileTemplate,
                fileName: fileName,
                data: reportOutput.FileContent,
                saveChanges: false);

            await _reportDataManager.AddReport(new Report
            {
                DocumentId = documentId,
                ReportName = fileTemplate,
                Type = ReportType.CompaniesWithoutSubmissions,
            });
        }
    }
}
