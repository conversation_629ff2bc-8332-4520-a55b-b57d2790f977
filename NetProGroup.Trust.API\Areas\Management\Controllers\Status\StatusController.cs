// <copyright file="StatusController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Status;
using NetProGroup.Trust.Application.Contracts.Status.Models;
using Swashbuckle.AspNetCore.Annotations;
using System.Reflection;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Status
{
    /// <summary>
    /// Controller for retrieving various system status information.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    [ApiController]
    public class StatusController : TrustAPIControllerBase
    {
        private readonly IStatusAppService _statusAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="StatusController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="statusAppService">The service for status operations.</param>
        public StatusController(
            ILogger<StatusController> logger,
            IStatusAppService statusAppService)
            : base(logger)
        {
            _statusAppService = statusAppService;
        }

        /// <summary>
        /// Gets the ViewPoint sync status including details of the last successful sync.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     GET /api/v1/management/status/viewpoint-sync.
        /// </remarks>
        /// <returns>The ViewPoint sync status with details for each entity type.</returns>
        [HttpGet("viewpoint-sync")]
        [SwaggerOperation(OperationId = "Management_GetViewPointSyncStatus")]
        [ProducesResponseType(typeof(ViewPointSyncStatusDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetViewPointSyncStatus()
        {
            ViewPointSyncStatusDTO item = null;

            var result = await ProcessRequestAsync(
                executeAsync: async () => item = await _statusAppService.GetViewPointSyncStatusAsync(),
                createResponseModel: () => item);

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the application version.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     GET /api/v1/management/status/version.
        /// </remarks>
        /// <returns>The application version.</returns>
        [HttpGet("version")]
        [SwaggerOperation(OperationId = "Management_GetAppVersion")]
        [ProducesResponseType(typeof(AppVersionDTO), StatusCodes.Status200OK)]
        [AllowAnonymous]
        public IActionResult GetAppVersion()
        {
            var version = GetType().Assembly
                .GetCustomAttribute<AssemblyFileVersionAttribute>()
                ?.Version;

            var versionDto = new AppVersionDTO
            {
                Version = version
            };

            return Ok(versionDto);
        }
    }
}
