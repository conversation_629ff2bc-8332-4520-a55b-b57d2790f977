﻿// <copyright file="TrustAPIControllerBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Models.Exceptions;
using NetProGroup.Framework.Mvc;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Errors;

namespace NetProGroup.Trust.API.Controllers
{
    /// <summary>
    /// Base for controllers for the project.
    /// </summary>
    [Authorize]
    [ApiController]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(Swagger.ResponseTypes.BadRequestResponseTypes))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(APIExceptionModel))]
    [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(APIExceptionModel))]
    public abstract class TrustAPIControllerBase : APIControllerBase
    {
        private IServiceProvider _serviceProvider;
        private IWorkContext _workContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="TrustAPIControllerBase"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        protected TrustAPIControllerBase(ILogger logger)
            : base(logger)
        {
        }

        /// <summary>
        /// Gets the WorkContext for the current request.
        /// </summary>
        protected IWorkContext WorkContext => _workContext;

        /// <summary>
        /// Intercepts the flow just before executing the action.
        /// </summary>
        /// <param name="context">The executign context.</param>
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            ArgumentNullException.ThrowIfNull(context);

            _serviceProvider = context.HttpContext.RequestServices;
            _workContext = context.HttpContext.RequestServices.GetService<IWorkContext>();
        }

        /// <summary>
        /// Validates whether IdentityUserId is set in the !WorkContext.
        /// </summary>
        /// <exception cref="BadRequestException">The exception thrown if no userId in WorkContext.</exception>
        protected void ValidateWorkContextUserId()
        {
            if (!WorkContext.IdentityUserId.HasValue)
            {
                throw new BadRequestException(ApplicationErrors.USER_ID_NOT_IN_HEADER.ToErrorCode(), "No user passed in header (x-userid)");
            }
        }

        /// <summary>
        /// Gets the AppId from the claims. This is the application id of the calling client.
        /// </summary>
        /// <returns>The applicationid from the appid claim.</returns>
        protected string GetApplicationId()
        {
            string result = null;

            var appClaims = HttpContext.User.Claims.ToList();
            if (appClaims.Count > 0 && appClaims.Exists(x => x.Type.Equals("appid", StringComparison.OrdinalIgnoreCase)))
            {
                result = appClaims.FirstOrDefault(x => x.Type.Equals("appid", StringComparison.OrdinalIgnoreCase))?.Value;
            }

            return result;
        }
    }
}
