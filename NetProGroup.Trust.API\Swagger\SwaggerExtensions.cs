﻿// <copyright file="SwaggerExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Reflection;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using NetProGroup.Framework.Swagger;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Diagnostics.CodeAnalysis;

namespace NetProGroup.Trust.API.Swagger
{
    /// <summary>
    /// https://medium.com/geekculture/customizing-swagger-in-asp-net-core-5-2c98d03cbe52.
    /// https://www.dotnetnakama.com/blog/enriched-web-api-documentation-using-swagger-openapi-in-asp-dotnet-core/.
    /// https://medium.com/@niteshsinghal85/enhance-swagger-documentation-with-annotations-in-asp-net-core-d2981803e299.
    /// </summary>
    public static class SwaggerExtensions
    {
        /// <summary>
        /// Configures swagger UI.
        /// </summary>
        /// <param name="app">Instance of the WebApplication.</param>
        public static void ConfigureSwaggerUI([NotNull] this WebApplication app)
        {
            var clientId = String.IsNullOrEmpty(app.Configuration.GetValue<string>("SwaggerConfig:ClientId")) ?
                app.Configuration.GetValue<string>("AppRegistration:ClientId") :
                app.Configuration.GetValue<string>("SwaggerConfig:ClientId");

            var clientSecret = String.IsNullOrEmpty(app.Configuration.GetValue<string>("SwaggerConfig:ClientSecret")) ?
                app.Configuration.GetValue<string>("AppRegistration:ClientSecret") :
                app.Configuration.GetValue<string>("SwaggerConfig:ClientSecret");

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.DefaultModelsExpandDepth(-1); // Disable swagger schemas at bottom

                // c.SwaggerEndpoint("/swagger/v1/swagger.json", "NetProGroup Trust API V1");
                var descriptions = app.DescribeApiVersions();
                foreach (var description in descriptions)
                {
                    c.SwaggerEndpoint($"/swagger/{description.GroupName}/swagger.json", "NetProGroup Trust API " + description.GroupName.ToUpperInvariant());
                }

                c.DocExpansion(DocExpansion.None);
                c.OAuthClientId(clientId);
                c.OAuthClientSecret(clientSecret);
            });
        }

        /// <summary>
        /// Configures SwaggerGen.
        /// </summary>
        /// <param name="builder">Instance of the WebApplicationBuilder.</param>
        public static void ConfigureSwaggerGen(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(config =>
            {
                config.EnableAnnotations();

                // config.SwaggerDoc("v1", new OpenApiInfo { Title = "NetProGroup Trust API", Version = "v1" });
                config.AddSecurityDefinition("oAuth2", new OpenApiSecurityScheme
                {
                    Flows = new OpenApiOAuthFlows
                    {
                        ClientCredentials = new OpenApiOAuthFlow
                        {
                            TokenUrl = new Uri($"{builder.Configuration.GetValue<string>("SwaggerConfig:ApplicationUrl")}/api/v1.0/tools/access/client-credentials-token"),
                            Scopes = new Dictionary<string, string>
                                {
                                    {
                                        $"{builder.Configuration.GetValue<string>("AzureAd:Audience")}/.default", ".default"
                                    }
                                }
                        }
                    },
                    In = ParameterLocation.Header,
                    Name = HeaderNames.Authorization,
                    Type = SecuritySchemeType.OAuth2
                });
                config.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme, Id = "oAuth2"
                            }
                        },
                        ["Api"]
                    }
                });

                var filePath = Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
                config.IncludeXmlComments(filePath);

                config.DocumentFilter<PathLowercaseDocumentFilter>();

                config.OperationFilter<SwaggerFilter>();
                config.OperationFilter<OneOfOperationFilter>();
                config.OperationFilter<SortableColumnsOperationFilter>();
                config.SchemaFilter<PermissionNamesSchemaFilter>();
            });
        }
    }
}
