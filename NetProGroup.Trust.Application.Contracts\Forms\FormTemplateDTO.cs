﻿// <copyright file="FormTemplateDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a form template.
    /// </summary>
    public class FormTemplateDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplateDTO"/> class.
        /// </summary>
        public FormTemplateDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the template.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the id of the jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the name of the jurisdiction.
        /// </summary>
        public string JurisdictionName { get; set; }

        /// <summary>
        /// Gets or sets the id of the module.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the name of the module.
        /// </summary>
        public string ModuleName { get; set; }
    }
}
