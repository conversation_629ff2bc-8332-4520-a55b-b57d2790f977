// <copyright file="MarkSubmissionsAsPaidByCompanyYearsRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Request DTO for marking submissions as paid by company code and filing year.
    /// </summary>
    public class MarkSubmissionsAsPaidByCompanyYearsRequestDTO
    {
        /// <summary>
        /// Gets or sets the list of company code and filing year combinations.
        /// </summary>
        public IEnumerable<CompanyFinancialYearDto> CompanyFilingYears { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to mark as paid (true) or unpaid (false).
        /// </summary>
        public bool IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the id of the module that this request is related to.
        /// </summary>
        public Guid ModuleId { get; set; }
    }
}
