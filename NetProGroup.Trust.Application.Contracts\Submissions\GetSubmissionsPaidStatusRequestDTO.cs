// <copyright file="GetSubmissionsPaidStatusRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Request DTO for getting submission paid status by company and year.
    /// </summary>
    public class GetSubmissionsPaidStatusRequestDTO
    {
        /// <summary>
        /// Gets or sets the list of company code and filing year pairs.
        /// </summary>
        public IEnumerable<CompanyFinancialYearDto> CompanyFilingYears { get; set; }

        /// <summary>
        /// Gets or sets the id of the module that this request is for.
        /// </summary>
        public Guid ModuleId { get; set; }
    }
}
