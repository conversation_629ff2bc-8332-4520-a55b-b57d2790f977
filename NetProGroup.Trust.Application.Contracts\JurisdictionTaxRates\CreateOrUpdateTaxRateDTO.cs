﻿// <copyright file="CreateOrUpdateTaxRateDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates
{
    /// <summary>
    /// Data Transfer Object (DTO) for creating or updating a tax rate.
    /// </summary>
    public class CreateOrUpdateTaxRateDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier of the jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the tax rate.
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// Gets or sets the start date for the tax rate.
        /// </summary>
        public DateTime StartDate { get; set; }
    }
}
