// <copyright file="IPaymentsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Provides methods for handling payment-related operations in the application.
    /// </summary>
    public interface IPaymentsAppService : IScopedService
    {
        /// <summary>
        /// Retrieves a paginated list of payments based on the specified request criteria.
        /// </summary>
        /// <param name="requestDTO">The request object containing filter and pagination criteria.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paginated list of <see cref="PaymentDTO"/> objects.</returns>
        Task<IPagedList<PaymentDTO>> ListPaymentsAsync(PaymentsRequestDTO requestDTO);

        /// <summary>
        /// Adds a new payment transaction to the system.
        /// </summary>
        /// <param name="createTransactionRequestDto">The payment information to be added.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created <see cref="CreatePaymentRequestDTO"/>.</returns>
        Task<CreatePaymentResponseDTO> AddPaymentAsync(CreatePaymentRequestDTO createTransactionRequestDto);

        /// <summary>
        /// Gets a payment by its unique identifier.
        /// </summary>
        /// <param name="id">the unique identifier of the payment.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the payment details.</returns>
        Task<PaymentDetailsResponseDTO> GetPaymentAsync(Guid id);

        /// <summary>
        /// Cancels a payment.
        /// </summary>
        /// <param name="paymentId">Id of the payment to cancel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CancelPaymentAsync(Guid paymentId);
    }
}