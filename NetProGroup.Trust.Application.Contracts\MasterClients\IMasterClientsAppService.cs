﻿// <copyright file="IMasterClientsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Domain.Shared.Enums;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Interface for the MasterClient AppService.
    /// </summary>
    public interface IMasterClientsAppService : IScopedService
    {
        /// <summary>
        /// Checks if the email/masterclientcode relation exists.
        /// </summary>
        /// <param name="masterClientCode">The MasterClientCode to check for.</param>
        /// <param name="email">The emailaddress to check for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> EmailExistsForMasterClientCodeAsync(string masterClientCode, string email);

        /// <summary>
        /// Gets the paged list of master clients.
        /// </summary>
        /// <param name="searchTerm">Optional term to search for.</param>
        /// <param name="jurisdictionId">Optional id of the jurisdiction.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the master clients as a paged list.</returns>
        Task<IPagedList<MasterClientDTO>> GetMasterClientsAsync(string searchTerm, Guid? jurisdictionId, int pageNumber,
            int pageSize);

        /// <summary>
        /// Gets a single masterclient with users and jurisdictions.
        /// </summary>
        /// <param name="masterClientId">The id of the masterclient to get.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the master client.</returns>
        Task<MasterClientDTO> GetMasterClientAsync(Guid masterClientId);

        /// <summary>
        /// Searches for masterclient using the search term. Search is performed on the master client code and the jurisdiction name.
        /// </summary>
        /// <param name="searchTerm">Term to use for the search.</param>
        /// <returns>A <see cref="Task{MasterClientsSearchResultDTO}"/> representing the asynchronous operation. The task result contains the model with the found masterclients.</returns>
        Task<MasterClientsSearchResultsDTO> SearchMasterClientsAsync(string searchTerm);

        /// <summary>
        /// Searches for masterclient companies using the search term. Search is performed on the name andd the incorporationcode.
        /// </summary>
        /// <param name="masterClientId">Id of the masterclient to get the companies for.</param>
        /// <param name="searchTerm">Term to use for the search.</param>
        /// <param name="active">Optional filter to get only active or inactive companies.</param>
        /// <param name="onboardingStatus">Optional filter for the onboarding status.</param>
        /// <returns>A <see cref="Task{CompaniesSearchResultsDTO}"/> representing the asynchronous operation. The task result contains the model with the found companies.</returns>
        Task<CompaniesSearchResultsDTO> SearchCompaniesAsync(Guid masterClientId, string searchTerm, bool? active, OnboardingStatus? onboardingStatus);

        /// <summary>
        /// Lists the users of a MasterClient.
        /// </summary>
        /// <param name="masterClientId">Id of the masterclient to get the users for.</param>
        /// <param name="request">The request with parameters.</param>
        /// <returns>A (paged) list of the found masterclient users.</returns>
        Task<IPagedList<ListMasterClientUserDTO>> ListUsersAsync(Guid masterClientId, ListUsersRequestDTO request);

        /// <summary>
        /// Adds a user to a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<MasterClientUserDTO> CreateUserToMasterClientAsync(CreateMasterClientUserDTO model);

        /// <summary>
        /// Removes a user from a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RemoveUserFromMasterClientAsync(RemoveMasterClientUserDTO model);
    }
}
