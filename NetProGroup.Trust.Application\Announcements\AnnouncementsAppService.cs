// <copyright file="AnnouncementsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.Announcements
{
    /// <summary>
    /// ApplicationService for announcements.
    /// </summary>
    public class AnnouncementsAppService : IAnnouncementsAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IAnnouncementDataManager _announcementDataManager;
        private readonly ISecurityManager _securityManager;
        private readonly IWorkContext _workContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="AnnouncementsAppService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="mapper">The mapper instance.</param>
        /// <param name="announcementDataManager">The data manager for announcements.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="workContext">The current work context.</param>
        public AnnouncementsAppService(
            ILogger<AnnouncementsAppService> logger,
            IMapper mapper,
            IAnnouncementDataManager announcementDataManager,
            ISecurityManager securityManager,
            IWorkContext workContext)
        {
            _logger = logger;
            _mapper = mapper;
            _announcementDataManager = announcementDataManager;
            _securityManager = securityManager;
            _workContext = workContext;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListAnnouncementDTO>> FilterAnnouncementsAsync(FilterAnnouncementsDTO data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Search);

            return await _announcementDataManager.FilterAnnouncementsAsync(data);
        }

        /// <inheritdoc/>
        public async Task<Guid> CreateUpdateAnnouncementAsync(CreateUpdateAnnouncementDTO data)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Authorization
            if (data.SendToAllJurisdictions)
            {
                await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Create);
            }
            else
            {
                await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Create_Limited);
            }

            return await _announcementDataManager.CreateUpdateAnnouncementAsync(data);
        }

        /// <inheritdoc/>
        public async Task<AnnouncementDTO> GetAnnouncementByIdAsync(Guid announcementId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_View);

            return await _announcementDataManager.GetAnnouncementByIdAsync(announcementId);
        }

        /// <inheritdoc/>
        public async Task CreateAnnouncementDocumentAsync(Guid announcementId, CreateAnnouncementDocumentDTO data)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Create_Limited);

            await _announcementDataManager.CreateAnnouncementDocumentAsync(announcementId, data);
        }

        /// <inheritdoc/>
        public async Task DeleteAnnouncementDocumentAsync(Guid announcementDocumentId, bool uploadComplete)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Delete);

            await _announcementDataManager.DeleteAnnouncementDocumentAsync(announcementDocumentId, uploadComplete);
        }

        /// <inheritdoc/>
        public async Task DeleteAnnouncementAsync(Guid announcementId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.AnnouncementModule_Delete);

            // Delete the announcement entity
            await _announcementDataManager.DeleteAnnouncementAsync(announcementId);
        }
    }
}