﻿// <copyright file="SystemAuditManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Framework.Services.ActivityLogs.Services;
using NetProGroup.Framework.Services.Communication.EFModels;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Framework.Services.EFAuditing.EFRepository;
using NetProGroup.Framework.Services.Extensions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Constants;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.FormDocuments;
using System.Diagnostics.CodeAnalysis;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Manager for system audits like ActivityLog.
    /// ActivityLogs have a ContextId that can be used to correlate the database audits (through the UnitOfWork).
    /// </summary>
    public class SystemAuditManager : ISystemAuditManager
    {
        private readonly ILogger _logger;
        private readonly IWorkContext _workContext;
        private readonly IUserRepository _userRepository;

        private readonly IActivityLogRepository _activityLogRepository;
        private readonly IActivityLogManager _activityLogManager;
        private readonly IAuditService _auditService;
        private readonly ISystemAuditItemBuilder _systemAuditItemBuilder;
        private readonly IMapper _mapper;
        private readonly IAuditRepository _auditRepository;

        private ApplicationUser _contextUser;

        /// <summary>
        /// Initializes a new instance of the <see cref="SystemAuditManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="workContext">Context for the current request.</param>
        /// <param name="userRepository">Repository for users.</param>
        /// <param name="activityLogManager">ActivityLogManager from framework.</param>
        /// <param name="activityLogRepository">Repository for reading ActivityLog.</param>
        /// <param name="auditService">Service for getting database audit entries.</param>
        /// <param name="systemAuditItemBuilder">Builder for the ActivityLog output.</param>
        /// <param name="mapper">Mapper for mapping objects.</param>
        /// <param name="auditRepository">Repository for reading database audits.</param>
        public SystemAuditManager(ILogger<SystemAuditManager> logger,
                                  IWorkContext workContext,
                                  IUserRepository userRepository,
                                  IActivityLogManager activityLogManager,
                                  IActivityLogRepository activityLogRepository,
                                  IAuditService auditService,
                                  ISystemAuditItemBuilder systemAuditItemBuilder,
                                  IMapper mapper,
                                  IAuditRepository auditRepository)
        {
            _logger = logger;
            _workContext = workContext;

            _userRepository = userRepository;
            _activityLogManager = activityLogManager;
            _activityLogRepository = activityLogRepository;
            _auditService = auditService;
            _systemAuditItemBuilder = systemAuditItemBuilder;
            _mapper = mapper;
            _auditRepository = auditRepository;
        }

        /// <inheritdoc/>
        public async Task AddUserAuthenticatedActivityLogAsync(ApplicationUser authenticatedUser, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(authenticatedUser, nameof(authenticatedUser));

            await _activityLogManager.AddActivityLogAsync(
                authenticatedUser.Id,
                authenticatedUser.GetType().Name,
                authenticatedUser.Id,
                $"User '{authenticatedUser.GetDisplayName()}' authenticated.",
                $"The user with name '{authenticatedUser.GetDisplayName()}' and email '{authenticatedUser.Email}' authenticated at {FormatDateTimeStamp(DateTime.UtcNow)}.",
                saveChanges,
                activityType: ActivityLogActivityTypes.UserAuthenticate);
        }

        /// <inheritdoc/>
        public async Task AddUserAuthenticatedActivityLogAsync(ApplicationUserDTO authenticatedUser, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(authenticatedUser, nameof(authenticatedUser));

            await _activityLogManager.AddActivityLogAsync(
                authenticatedUser.Id,
                authenticatedUser,
                $"User '{authenticatedUser.GetDisplayName()}' authenticated.",
                $"The user with name '{authenticatedUser.GetDisplayName()}' and email '{authenticatedUser.Email}' authenticated at {FormatDateTimeStamp(DateTime.UtcNow)}.",
                saveChanges,
                activityType: ActivityLogActivityTypes.UserAuthenticate);
        }

        /// <inheritdoc/>
        public async Task AddUserRegisteredActivityLogAsync(ApplicationUser registeredUser, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(registeredUser, nameof(registeredUser));

            await _activityLogManager.AddActivityLogAsync(
                registeredUser.Id,
                registeredUser.GetType().Name,
                registeredUser.Id,
                $"User '{registeredUser.GetDisplayName()}' registered.",
                $"The user with name '{registeredUser.GetDisplayName()}' and email '{registeredUser.Email}' registered at {FormatDateTimeStamp(DateTime.UtcNow)}.",
                saveChanges,
                activityType: ActivityLogActivityTypes.UserRegister);
        }

        /// <inheritdoc/>
        public async Task AddUserLogoutActivityLogAsync(ApplicationUser loggedoutUser, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(loggedoutUser, nameof(loggedoutUser));

            await _activityLogManager.AddActivityLogAsync(
                loggedoutUser.Id,
                loggedoutUser.GetType().Name,
                loggedoutUser.Id,
                $"User '{loggedoutUser.GetDisplayName()}' logged out.",
                $"The user with name '{loggedoutUser.GetDisplayName()}' and email '{loggedoutUser.Email}' logged out at {FormatDateTimeStamp(DateTime.UtcNow)}.",
                saveChanges,
                activityType: ActivityLogActivityTypes.UserLogout);
        }

        /// <inheritdoc/>
        public async Task AddUserLogoutActivityLogAsync(ApplicationUserDTO loggedoutUser, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(loggedoutUser, nameof(loggedoutUser));

            await _activityLogManager.AddActivityLogAsync(
                loggedoutUser.Id,
                loggedoutUser,
                $"User '{loggedoutUser.GetDisplayName()}' logged out.",
                $"The user with name '{loggedoutUser.GetDisplayName()}' and email '{loggedoutUser.Email}' logged out at {FormatDateTimeStamp(DateTime.UtcNow)}.",
                saveChanges,
                activityType: ActivityLogActivityTypes.UserLogout);
        }

        /// <inheritdoc/>
        public async Task AddMasterClientAddedActivityLogAsync(MasterClient addedMasterClient, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(addedMasterClient, nameof(addedMasterClient));

            await LogActivityAsync(
                addedMasterClient,
                $"MasterClient '{addedMasterClient.Code}' is added.",
                $"The master client with code '{addedMasterClient.Code} is added",
                saveChanges,
                ActivityLogActivityTypes.MasterClientAdded);
        }

        /// <inheritdoc />
        public async Task AddLegalEntityAddedToMasterClientActivityLogAsync(LegalEntity addedLegalEntity,
            MasterClient addedToMasterClient, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(addedLegalEntity, nameof(addedLegalEntity));
            ArgumentNullException.ThrowIfNull(addedToMasterClient, nameof(addedToMasterClient));

            await LogActivityAsync(
                addedToMasterClient,
                $"LegalEntity '{addedLegalEntity.Code}' is added to MasterClient '{addedToMasterClient.Code}'.",
                $"The legal entity with code '{addedLegalEntity.Code} is added to master client '{addedToMasterClient.Code}'",
                saveChanges,
                ActivityLogActivityTypes.MasterClientLegalEntityAdded);
        }

        /// <inheritdoc />
        public async Task<ActivityLog> CreateLegalEntityAddedToMasterClientActivityLogAsync(LegalEntity addedLegalEntity, MasterClient addedToMasterClient)
        {
            ArgumentNullException.ThrowIfNull(addedLegalEntity, nameof(addedLegalEntity));
            ArgumentNullException.ThrowIfNull(addedToMasterClient, nameof(addedToMasterClient));

            var user = await GetContextUser();

            return await CreateActivityLogAsync(
                addedToMasterClient,
                ActivityLogActivityTypes.MasterClientLegalEntityAdded,
                $"LegalEntity '{addedLegalEntity.Code}' is added to MasterClient '{addedToMasterClient.Code}'.",
                $"The legal entity with code '{addedLegalEntity.Code} is added to master client '{addedToMasterClient.Code}' {GetCommonUserTextSuffix(user)}.");
        }

        /// <inheritdoc />
        public async Task AddUserAddedToMasterClientActivityLogAsync(ApplicationUser addedUser,
            MasterClient addedToMasterClient, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(addedUser, nameof(addedUser));
            ArgumentNullException.ThrowIfNull(addedToMasterClient, nameof(addedToMasterClient));

            var shortDescription = $"User added to MasterClient.";
            var description = $"User '{addedUser.GetDisplayName()}' is added to MasterClient '{addedToMasterClient.Code}'.";

            await AddActivityLogAsync(
                addedUser,
                ActivityLogActivityTypes.UserAddedToMasterClient,
                shortDescription,
                description,
                saveChanges);

            await AddActivityLogAsync(
                addedToMasterClient,
                ActivityLogActivityTypes.MasterClientUserAdded,
                shortDescription,
                description,
                saveChanges);
        }

        /// <inheritdoc />
        public async Task<List<ActivityLog>> CreateUserAddedToMasterClientActivityLogAsync(ApplicationUser addedUser, MasterClient addedToMasterClient)
        {
            ArgumentNullException.ThrowIfNull(addedUser, nameof(addedUser));
            ArgumentNullException.ThrowIfNull(addedToMasterClient, nameof(addedToMasterClient));

            var shortDescription = $"User added to MasterClient.";
            var description = $"User '{addedUser.GetDisplayName()}' is added to MasterClient '{addedToMasterClient.Code}'.";

            var result = new List<ActivityLog>();

            result.Add(await CreateActivityLogAsync(
                addedUser,
                ActivityLogActivityTypes.UserAddedToMasterClient,
                shortDescription,
                description));

            result.Add(await CreateActivityLogAsync(
                addedToMasterClient,
                ActivityLogActivityTypes.MasterClientUserAdded,
                shortDescription,
                description));

            return result;
        }

        /// <inheritdoc />
        public async Task AddUserRemovedFromMasterClientActivityLogAsync(ApplicationUser removedUser, MasterClient removedFromMasterClient,
            bool saveChanges)
        {
            ArgumentNullException.ThrowIfNull(removedUser, nameof(removedUser));
            ArgumentNullException.ThrowIfNull(removedFromMasterClient, nameof(removedFromMasterClient));

            var longDescription = $"User '{removedUser.GetDisplayName()}' is removed from MasterClient '{removedFromMasterClient.Code}'.";
            var shortDescription = $"User removed from MasterClient '{removedFromMasterClient.Code}'.";

            await AddActivityLogAsync(
                removedUser,
                ActivityLogActivityTypes.UserRemovedFromMasterClient,
                shortDescription,
                longDescription,
                saveChanges);

            await AddActivityLogAsync(
                removedFromMasterClient,
                ActivityLogActivityTypes.MasterClientUserRemoved,
                shortDescription,
                longDescription,
                saveChanges);
        }

        /// <inheritdoc />
        public async Task<List<ActivityLog>> CreateUserRemovedFromMasterClientActivityLogAsync(ApplicationUser removedUser, MasterClient removedFromMasterClient)
        {
            ArgumentNullException.ThrowIfNull(removedUser, nameof(removedUser));
            ArgumentNullException.ThrowIfNull(removedFromMasterClient, nameof(removedFromMasterClient));

            var longDescription = $"User '{removedUser.GetDisplayName()}' is removed from MasterClient '{removedFromMasterClient.Code}'.";
            var shortDescription = $"User removed from MasterClient '{removedFromMasterClient.Code}'.";

            var result = new List<ActivityLog>();

            result.Add(await CreateActivityLogAsync(
                removedUser,
                ActivityLogActivityTypes.UserRemovedFromMasterClient,
                shortDescription,
                longDescription));

            result.Add(await CreateActivityLogAsync(
                removedFromMasterClient,
                ActivityLogActivityTypes.MasterClientUserRemoved,
                shortDescription,
                longDescription));

            return result;
        }

        /// <inheritdoc />
        public async Task<ActivityLog> CreateModuleDeactivatedActivityLogAsync(LegalEntity legalEntity, Module deactivatedModule)
        {
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));
            ArgumentNullException.ThrowIfNull(deactivatedModule, nameof(deactivatedModule));

            var longDescription = $"Module '{deactivatedModule.Name}' is deactivated for company '{legalEntity.Code}'.";
            var shortDescription = $"Module '{deactivatedModule.Name}' deactivated.";

            var result = await CreateActivityLogAsync(
                legalEntity,
                ActivityLogActivityTypes.ModuleForCompanyDeactivated,
                shortDescription,
                longDescription);

            return result;
        }

        /// <inheritdoc/>
        public async Task AddMessageSentToEmployeeForCompanyActivityLogAsync(LegalEntity legalEntity, string recipient, bool saveChanges)
        {
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));

            await LogActivityAsync(
                legalEntity,
                $"An email message is sent to '{recipient}'.",
                $"An email message is sent to '{recipient}'",
                saveChanges,
                ActivityLogActivityTypes.MessageSentToEmployeeForCompany);
        }

        /// <inheritdoc/>
        public async Task AddSubmissionSubmittedActivityLogAsync(Submission submission, SubmissionStatus previousStatus, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            // Check the submission event.
            if (previousStatus == SubmissionStatus.Revision)
            {
                await LogActivityAsync(
                    submission,
                    $"Submission '{submission.Name}' resubmitted.",
                    $"Submission '{submission.Name}' resubmitted",
                    saveChanges,
                    ActivityLogActivityTypes.SubmissionResubmitted);
            }
            else if (previousStatus == SubmissionStatus.InformationRequested)
            {
                await LogActivityAsync(
                    submission,
                    $"Information request completed.",
                    $"Information request completed for Submission with name '{submission.Name}'.",
                    saveChanges,
                    ActivityLogActivityTypes.SubmissionInformationRequestCompleted);
            }
            else
            {
                await LogActivityAsync(
                    submission,
                    $"Submission '{submission.Name}' submitted.",
                    $"Submission '{submission.Name}' submitted",
                    saveChanges,
                    ActivityLogActivityTypes.SubmissionSubmitted);
            }
        }

        /// <inheritdoc/>
        public async Task AddSubmissionScheduledActivityLogAsync(Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Submission '{submission.Name}' scheduled for submit.",
                $"Submission '{submission.Name}' scheduled for submit",
                saveChanges,
                ActivityLogActivityTypes.SubmissionSubmitScheduled);
        }

        /// <inheritdoc/>
        public async Task AddActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, string text, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            await AddActivityLogAsync(entity.GetType().Name, entity.Id, activityType, shortDescription, text, saveChanges);
        }

        /// <inheritdoc/>
        public async Task AddActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            var text = shortDescription;

            await AddActivityLogAsync(entity, activityType, shortDescription, text, saveChanges);
        }

        /// <inheritdoc />
        public async Task<ActivityLog> CreateActivityLogAsync(Framework.EF.Repository.Interfaces.IEntity<Guid> entity, string activityType, string shortDescription, string text)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            return await CreateActivityLogAsync(entity.GetType().Name, entity.Id, activityType, shortDescription, text);
        }

        /// <inheritdoc/>
        public async Task AddInboxIsReadActivityLogAsync([NotNull] Inbox inboxMessage, bool isRead, bool saveChanges = false)
        {
            Check.NotNull(inboxMessage, nameof(inboxMessage));

            // Log the event
            await LogActivityAsync(
                inboxMessage,
                $"The 'IsRead' status of inbox message '{inboxMessage.Subject}' changed.",
                $"The 'IsRead' status of inbox message with subject '{inboxMessage.Subject}' was changed to '{isRead}'",
                saveChanges);
        }

        /// <inheritdoc/>
        public async Task AddSubmissionInformationRequestedActivityLogAsync(Submission submission, RequestForInformation requestForInformation, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));

            await LogActivityAsync(
                    submission,
                    $"Information requested.",
                    $"Information requested: \"{requestForInformation.Comments}\"",
                    saveChanges,
                    ActivityLogActivityTypes.SubmissionInformationRequested);
        }

        /// <inheritdoc/>
        public async Task AddRequestForInformationCancelledActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Information request cancelled.",
                $"Information request with id '{requestForInformation.Id}' cancelled, because: " + requestForInformation.CancellationReason,
                saveChanges,
                ActivityLogActivityTypes.SubmissionInformationRequestCancelled);
        }

        /// <inheritdoc/>
        public async Task AddRequestForInformationCompletedActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Information request with id '{requestForInformation.Id}' completed.",
                $"Information request with id '{requestForInformation.Id}' completed",
                saveChanges,
                ActivityLogActivityTypes.SubmissionInformationRequestCompleted);
        }

        /// <inheritdoc/>
        public async Task AddRfiCompletedNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Notification of completion of RFI with id '{requestForInformation.Id}'.",
                $"Notification of completion of RFI with id '{requestForInformation.Id}'",
                saveChanges,
                ActivityLogActivityTypes.RfiCompletedNotificationCreated);
        }

        /// <inheritdoc/>
        public async Task AddRfiCreatedNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Notification of RFI creation with id '{requestForInformation.Id}'.",
                $"Notification of RFI creation with id '{requestForInformation.Id}'",
                saveChanges,
                ActivityLogActivityTypes.RfiCreatedNotificationCreated);
        }

        /// <inheritdoc/>
        public async Task AddRfiDueInOneWeekNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Notification of RFI due in one week with id '{requestForInformation.Id}'.",
                $"Notification of RFI due in one week with id '{requestForInformation.Id}'",
                saveChanges,
                ActivityLogActivityTypes.RfiDueInOneWeekNotificationCreated);
        }

        /// <inheritdoc/>
        public async Task AddRfiDueInOneDayNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Notification of RFI due in one day with id '{requestForInformation.Id}'.",
                $"Notification of RFI due in one day with id '{requestForInformation.Id}'",
                saveChanges,
                ActivityLogActivityTypes.RfiDueInOneDayNotificationCreated);
        }

        /// <inheritdoc/>
        public async Task AddRfiThreeDaysOverdueNotificationActivityLogAsync(RequestForInformation requestForInformation, Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(requestForInformation, nameof(requestForInformation));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Notification of RFI three days overdue with id '{requestForInformation.Id}'.",
                $"Notification of RFI three days overdue with id '{requestForInformation.Id}'",
                saveChanges,
                ActivityLogActivityTypes.Rfi3DaysOverDueNotificationCreated);
        }

        /// <inheritdoc />
        public async Task AddAnnualFeeStatusUpdatedActivityLogAsync(LegalEntityAnnualFee annualFee, LegalEntity legalEntity, bool saveChanges)
        {
            ArgumentNullException.ThrowIfNull(annualFee, nameof(annualFee));
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));

            await LogActivityAsync(
                legalEntity,
                $"Annual Fee status for {annualFee.FinancialYear} updated.",
                $"Annual Fee status for {annualFee.FinancialYear} updated to {(annualFee.IsPaid ? "paid" : "unpaid")} for Legal Entity '{legalEntity.Code}'",
                saveChanges,
                ActivityLogActivityTypes.CompanyAnnualFeeStatusUpdated);
        }

        /// <inheritdoc/>
        public async Task AddSubmissionFinancialPeriodChangedActivityLogAsync(Submission submission, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            await LogActivityAsync(
                submission,
                $"Financial Period changed for submission '{submission.Name}'.",
                $"Financial Period changed for submission '{submission.Name}' '{submission.StartsAt?.ToString(WellKnownDataConstants.DateFormat)}' to '{submission.EndsAt?.ToString(WellKnownDataConstants.DateFormat)}'",
                saveChanges,
                ActivityLogActivityTypes.SubmissionFinancialPeriodChanged);
        }

        /// <inheritdoc/>
        public async Task<ListActivityLogResponse> ListActivityLogsAsync(ListActivityLogRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var result = new ListActivityLogResponse();

            // Get the activity logs
            var startDate = request.Period.StartDate.Date;
            var endDate = request.Period.EndDate.Date;

            var pagedActivityLogs = await _activityLogRepository.FindByConditionAsPagedListAsync(x =>
                    (request.ActivityLogId == null || x.Id == request.ActivityLogId.Value) &&
                    (x.CreatedAt >= startDate && x.CreatedAt.Date <= endDate) &&
                    (request.EntityId == null || x.EntityId == request.EntityId.Value) &&
                    (request.ActivityType == null || x.ActivityType == request.ActivityType),
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                options: q => q.OrderByDescending(x => x.CreatedAt));

            // Get the audits based on the ContextIds of the ActivityLogs
            var contextIds = pagedActivityLogs.Where(x => x.ContextId.HasValue).Select(x => x.ContextId.Value).Distinct().ToList();

            // For now we get the EFAudits ContextId by ContextId.
            // New method must be added in Framework to get by list.
            var cachedUnitsOfWork = new Dictionary<Guid, List<AuditUnitOfWorkDTO>>();
            foreach (var contextId in contextIds)
            {
                if (!cachedUnitsOfWork.TryGetValue(contextId, out List<AuditUnitOfWorkDTO> value))
                {
                    value = new List<AuditUnitOfWorkDTO>();
                    cachedUnitsOfWork.Add(contextId, value);
                }

                value.AddRange(await GetUnitOfWorkByContextIdAsync(contextId));
            }

            var items = await _systemAuditItemBuilder.BuildActivityLogItemsAsync(pagedActivityLogs, cachedUnitsOfWork);

            result.ActivityLogItems = new StaticPagedList<ActivityLogItemDTO>(items, pagedActivityLogs.GetMetaData());

            return result;
        }

        /// <inheritdoc/>
        public async Task<List<ActivityLog>> ListActivityLogsAsync(IEnumerable<Guid> entityIds, IEnumerable<string> activityTypes)
        {
            return (await _activityLogRepository.FindByConditionAsync(al => entityIds.Contains(al.EntityId) && activityTypes.Contains(al.ActivityType))).ToList();
        }

        /// <inheritdoc/>
        public async Task<AuditUnitOfWorkDTO> GetAuditAsync(Guid auditId)
        {
            var audits = await _auditService.GetUnitOfWorkAsync(auditId);
            return audits;
        }

        /// <inheritdoc/>
        public async Task<ListAuditResponse> ListAuditsAsync(ListAuditRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var result = new ListAuditResponse();
            var entityId = request.EntityId.ToString().ToLower();

            var pagedData = await _auditRepository.FindByConditionAsPagedListAsync(work => work.Entities.Any(entity => entity.EntityId == entityId),
                request.PageNumber,
                request.PageSize,
                q => q.Include(work => work.Entities)
                      .ThenInclude(entity => entity.Details)
                      .OrderByDescending(work => work.ChangedAt));

            var subset = _mapper.Map<List<AuditUnitOfWorkDTO>>(pagedData);

            result.AuditItems = new StaticPagedList<AuditUnitOfWorkDTO>(subset, pagedData.GetMetaData());

            return result;
        }

        /// <summary>
        /// Formats the given (optional) datetime.
        /// </summary>
        /// <param name="dateTime">Optional datetime. Defaults to DateTime UtcNow.</param>
        /// <returns>Formatted datetime as string.</returns>
        private static string FormatDateTimeStamp(DateTime? dateTime = null)
        {
            if (!dateTime.HasValue)
            {
                dateTime = DateTime.UtcNow;
            }

            return $"{dateTime.Value.ToString(GeneralConsts.DateTimestampFormat)} (UTC)";
        }

        /// <summary>
        /// Gets a commonly used suffix for the ActivityLog texts.
        /// </summary>
        /// <param name="user">User for adding name and email.</param>
        /// <returns>Text as string.</returns>
        private static string GetCommonUserTextSuffix(ApplicationUser user)
        {
            if (user == null)
            {
                return $"by unknown user at {FormatDateTimeStamp(DateTime.UtcNow)}";
            }

            return $"by user with name '{user.GetDisplayName()}' and email '{user.Email}' at {FormatDateTimeStamp(DateTime.UtcNow)}";
        }

        private async Task<ActivityLog> CreateActivityLogAsync(string entityTypeName, Guid entityId, string activityType, string shortDescription, string text)
        {
            Check.Length(shortDescription, nameof(shortDescription), 100); // nvarchar(100) in database

            var user = await GetContextUser();

            return new ActivityLog
            {
                CreatedByIdentityUserId = user == null ? Guid.Empty : user.Id,
                EntityName = entityTypeName,
                EntityId = entityId,
                ActivityType = activityType,
                CreatedAt = DateTime.UtcNow,
                ShortDescription = shortDescription,
                Text = $"{text} {GetCommonUserTextSuffix(user)}"
            };
        }

        /// <summary>Gets a list of UnitOfWork with the given contextId.</summary>
        /// <param name="contextId">The ContextId to return the UnitOfWork for.</param>
        /// <returns>A list of AuditUnitOfWorkDTO.</returns>
        private async Task<IEnumerable<AuditUnitOfWorkDTO>> GetUnitOfWorkByContextIdAsync(Guid contextId)
        {
            var auditUnitOfWorks = await _auditRepository.FindByConditionAsync(x => x.ContextId == contextId,
                options: q => q.Include(work => work.Entities)
                               .ThenInclude(entity => entity.Details)
                               .OrderBy(work => work.ChangedAt));

            return _mapper.Map<List<AuditUnitOfWorkDTO>>(auditUnitOfWorks);
        }

        private async Task AddActivityLogAsync(string entityTypeName, Guid entityId, string activityType, string shortDescription, string text, bool saveChanges = false)
        {
            await LogActivityAsync(entityTypeName, entityId, shortDescription, text, saveChanges, activityType);
        }

        /// <summary>
        /// Logs an activity with proper user ID handling and common text suffix.
        /// </summary>
        /// <param name="entity">The entity associated with the activity.</param>
        /// <param name="shortDescription">The short description of the activity.</param>
        /// <param name="text">The text description of the activity (without suffix).</param>
        /// <param name="saveChanges">Whether to save changes immediately.</param>
        /// <param name="activityType">The type of activity.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task LogActivityAsync(
            Entity<Guid> entity,
            string shortDescription,
            string text,
            bool saveChanges = false,
            string activityType = null)
        {
            var user = await GetContextUser();

            await _activityLogManager.AddActivityLogAsync(
                userId: user?.Id ?? UserConsts.SystemUserId,
                entity: entity,
                shortDescription.LimitLength(100),
                $"{text} {GetCommonUserTextSuffix(user)}",
                saveChanges,
                activityType: activityType);
        }

        /// <summary>
        /// Logs an activity with proper user ID handling and common text suffix.
        /// </summary>
        /// <param name="entityTypeName">The entity type name associated with the activity.</param>
        /// <param name="entityId">The entity ID.</param>
        /// <param name="shortDescription">The short description of the activity.</param>
        /// <param name="text">The text description of the activity (without suffix).</param>
        /// <param name="saveChanges">Whether to save changes immediately.</param>
        /// <param name="activityType">The type of activity.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task LogActivityAsync(
            string entityTypeName,
            Guid entityId,
            string shortDescription,
            string text,
            bool saveChanges = false,
            string activityType = null)
        {
            var user = await GetContextUser();

            await _activityLogManager.AddActivityLogAsync(
                user?.Id ?? UserConsts.SystemUserId,
                entityTypeName,
                entityId,
                shortDescription.LimitLength(100),
                $"{text} {GetCommonUserTextSuffix(user)}",
                saveChanges,
                activityType: activityType);
        }

        /// <summary>
        /// Gets the (cached) user for the IdentityUserId in IWorkContext.
        /// </summary>
        /// <returns>ApplicationUser.</returns>
        private async Task<ApplicationUser> GetContextUser()
        {
            if (_contextUser == null)
            {
                if (_workContext.IdentityUserId.HasValue)
                {
                    _contextUser = await _userRepository.FindByUserByPredicateAsync(x => x.Id == _workContext.IdentityUserId);
                }
            }

            return _contextUser;
        }
    }
}
