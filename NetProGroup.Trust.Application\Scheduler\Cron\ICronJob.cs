﻿// <copyright file="ICronJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Scheduler.Cron;

namespace NetProGroup.Trust.Application.Scheduler
{
    /// <summary>
    /// Interface for cron jobs.
    /// </summary>
    public interface ICronJob
    {
        /// <summary>
        /// Gets the id of the scheduled task.
        /// </summary>
        /// <returns>The id as Guid.</returns>
        Guid ScheduledJobId { get; }

        /// <summary>
        /// Gets the name of the scheduled task.
        /// </summary>
        /// <returns>The name of the scheduled job.</returns>
        string ScheduledJobName { get; }

        /// <summary>
        /// Gets the key of the scheduled task.
        /// </summary>
        /// <returns>The key of the scheduled job.</returns>
        string ScheduledJobKey { get; }

        /// <summary>
        /// The entry for executing the job.
        /// </summary>
        /// <param name="jobDetails">The data for the executing job.</param>
        /// <param name="token">The cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RunAsync(JobRunDetails jobDetails, CancellationToken token = default);
    }
}
