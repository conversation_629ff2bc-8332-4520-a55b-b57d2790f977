// <copyright file="IBoDirExportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.BoDir;

namespace NetProGroup.Trust.Application.Contracts.Export
{
    /// <summary>
    /// Interface for exporting BO/Dir reports.
    /// </summary>
    public interface IBoDirExportGenerator : IScopedService
    {
        /// <summary>
        /// Generates the BO/Dir report.
        /// </summary>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <param name="jurisdictionIDs">List of jurisdictionids to create the report for.</param>
        /// <returns>The file content and metadata for the report.</returns>
        Task<BoDirDownloadResponseDTO> GenerateAsync(SearchBoDirRequestDTO request, List<Guid> jurisdictionIDs);
    }
}