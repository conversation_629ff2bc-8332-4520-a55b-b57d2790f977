﻿// <copyright file="OnAttributeCollectionStartController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Areas.ExternalId.Models;
using NetProGroup.Trust.API.Security;
using NetProGroup.Trust.DataManager.Users;
using Newtonsoft.Json;

namespace NetProGroup.Trust.API.Areas.ExternalId.Controllers
{
    /// <summary>
    /// Controller to be called by External Id to collect attributes when starting a signup.
    /// </summary>
    [ApiController]
    [Area("ExternalId")]
    [Route("api/v{version:apiVersion}/[Area]/On-Attribute-Collection-Start")]
    [Route("OnAttributeCollectionStart")]
    public class OnAttributeCollectionStartController : ControllerBase
    {
        private readonly ILogger<OnAttributeCollectionStartController> _logger;
        private readonly IUsersDataManager _usersDataManager;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="OnAttributeCollectionStartController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="configuration">The configuration instance.</param>
        /// <param name="usersDataManager">The users datamanager instance.</param>
        public OnAttributeCollectionStartController(ILogger<OnAttributeCollectionStartController> logger,
                                                    IConfiguration configuration,
                                                    IUsersDataManager usersDataManager)
        {
            _logger = logger;
            _configuration = configuration;
            _usersDataManager = usersDataManager;
        }

        /// <summary>
        /// The endpoint for a submit in the Signup flow (custom extension).
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch all")]
        public async Task<ActionResult<AttributeCollectionStartResponse>> PostAsync()
        {
            try
            {
                if (!await CustomExtensionsValidator.AuthenticateCallerAsync(Request, _configuration, _logger))
                {
                    return Unauthorized();
                }

                string body = string.Empty;
                AttributeCollectionRequest requestPayload = null;

                try
                {
                    var request = this.Request;
                    request.EnableBuffering();
                    request.Body.Position = 0;

                    using (var reader = new StreamReader(request.Body, Encoding.UTF8))
                    {
                        body = await reader.ReadToEndAsync().ConfigureAwait(false);
                    }

                    requestPayload = JsonConvert.DeserializeObject<AttributeCollectionRequest>(body);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Getting the body failed in OnAttributeCollectionSubmit()");
                }

                ArgumentNullException.ThrowIfNull(requestPayload, nameof(requestPayload));

                // Message object to return to Microsoft Entra ID
                var r = new AttributeCollectionStartResponse();
                r.data.actions[0].odatatype = AttributeCollectionStartResponse_ActionTypes.SetPrefillValues;
                r.data.actions[0].inputs = new AttributeCollectionStartResponse_Inputs();

                // Use an AppService to check for a correct match
                var email = requestPayload.data.userSignUpInfo.identities.First().issuerAssignedId;

                var applicationUser = await _usersDataManager.GetClientUserByEmailAsync(email);
                var displayName = string.Empty;

                if (applicationUser == null)
                {
                    _logger.LogWarning("Attempt to get client user {Email} for AttributeCollectionStart but user not found", email);
                }
                else
                {
                    // Do NOT use the GetDisplayName extension, just the known displayname
                    displayName = applicationUser.DisplayName == null ? string.Empty : applicationUser.DisplayName;
                }

                // Return the displayname that was found on the user
                r.data.actions[0].inputs.DisplayName = displayName;

                return r;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Signup extension failed");
                throw;
            }
        }
    }
}
#pragma warning restore SA1005 // Single line comments should begin with single space
