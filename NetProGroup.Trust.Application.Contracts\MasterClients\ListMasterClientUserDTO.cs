﻿// <copyright file="ListMasterClientUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Users;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClientUser.
    /// </summary>
    public class ListMasterClientUserDTO : ListUserDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the user was manually added (means 'manager' instead of 'owner').
        /// </summary>
        public bool IsManuallyAdded { get; set; }
    }
}
