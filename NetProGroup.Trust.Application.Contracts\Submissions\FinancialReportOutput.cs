// <copyright file="FinancialReportOutput.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Output for a financial report.
    /// </summary>
    public class FinancialReportOutput
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FinancialReportOutput"/> class.
        /// </summary>
        /// <param name="fileContent">The file content.</param>
        /// <param name="submissions">The submissions.</param>
        public FinancialReportOutput(byte[] fileContent, IEnumerable<Submission> submissions)
        {
            FileContent = fileContent;
            Submissions = submissions;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FinancialReportOutput"/> class.
        /// </summary>
        /// <param name="submissions">The submissions.</param>
        public FinancialReportOutput(IEnumerable<Submission> submissions)
        {
            Submissions = submissions;
        }

        /// <summary>
        /// Gets or sets the file content.
        /// </summary>
#pragma warning disable CA1819 // Properties should not return arrays
        public byte[] FileContent { get; set; }
#pragma warning restore CA1819 // Properties should not return arrays

        /// <summary>
        /// Gets or sets the submissions.
        /// </summary>
        public IEnumerable<Submission> Submissions { get; set; }
    }
}