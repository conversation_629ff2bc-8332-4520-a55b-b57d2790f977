﻿// <copyright file="MFAInfoDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// A DTO holding the info for MFA.
    /// </summary>
    public class MFAInfoDTO
    {
        /// <summary>
        /// Gets or sets the id of the user that the MFA infomation is for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the method to use for MFA.
        /// </summary>
        public string MFAMethod { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether MFA is enabled.
        /// </summary>
        public bool MFAIsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the Url for the QR code to pair with an authenticator.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1056:URI-like properties should not be strings", Justification = "Want it as a string")]
        public string MFAAuthenticatorQRUrl { get; set; }

        /// <summary>
        /// Gets or sets the the secret to manually pair with an authenticator.
        /// </summary>
        public string MFAAuthenticatorSecret { get; set; }

        /// <summary>
        /// Gets or sets the time that the code expires in Utc.
        /// </summary>
        public DateTime? MFAEmailCodeExpiresAt { get; set; }

        /// <summary>
        /// Gets or sets the number of seconds that the code is valid.
        /// </summary>
        public int MFAEmailCodeExpiresIn { get; set; }
    }
}
