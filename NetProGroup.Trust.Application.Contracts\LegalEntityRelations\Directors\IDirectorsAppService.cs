﻿// <copyright file="IDirectorsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors
{
    /// <summary>
    /// Interface for the Directors AppService.
    /// </summary>
    public interface IDirectorsAppService : IScopedService
    {
        /// <summary>
        /// Gets a paged list with Directors.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the directors as a paged list.</returns>
        Task<IPagedList<DirectorDTO>> ListDirectorsAsync(Guid legalEntityId, int pageNumber, int pageSize);

        /// <summary>
        /// Gets the current version of a particular Director using the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique id.</param>
        /// <returns>The found director DTO.</returns>
        Task<DirectorDTO> GetDirectorAsync(string uniqueRelationId);

        /// <summary>
        /// Confirmation of the data for the Director with the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique id.</param>
        /// <returns>The confirmed director DTO.</returns>
        Task<DirectorDTO> SetConfirmationAsync(string uniqueRelationId);

        /// <summary>
        /// Request for updating the data of the Director with the unique relation id.
        /// </summary>
        /// <param name="requestUpdate">The model holding the parameters for the update request.</param>
        /// <returns>The updated director DTO.</returns>
        Task<DirectorDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate);

        /// <summary>
        /// Request for assistance related to Directors.
        /// </summary>
        /// <param name="requestAssistance">The model holding the parameters for the assistance request.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance);

        /// <summary>
        /// Gets both current and prior version of a particular Director using the unique relation id so they can be compared.
        /// </summary>
        /// <param name="uniqueRelationId">The unique id.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(string uniqueRelationId);

        /// <summary>
        /// Simulates the name change for the specified relation.
        /// </summary>
        /// <param name="uniqueRelationId">The unique id.</param>
        /// <param name="newName">The new name.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SimulateUpdateSync(string uniqueRelationId, string newName);
    }
}
