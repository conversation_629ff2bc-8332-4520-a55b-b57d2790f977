﻿// <copyright file="MasterClientsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;

namespace NetProGroup.Trust.API.Areas.Imports.Controllers
{
    /// <summary>
    /// Use this controller to execute imports of MasterClients.
    /// </summary>
    [Area("Imports")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class MasterClientsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        public MasterClientsController(
            ILogger<MasterClientsController> logger,
            IConfiguration configuration)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Imports an excel sheet with master client data and email addresses.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to assign the master clients to.</param>
        /// <param name="excelFile">The uploaded excel file.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> ImportMasterClients(Guid jurisdictionId, IFormFile excelFile)
        {
            await Task.CompletedTask;

            return Ok();
        }
    }
}
