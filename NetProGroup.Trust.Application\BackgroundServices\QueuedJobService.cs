﻿// <copyright file="QueuedJobService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Trust.DataManager.Invitations;
using NetProGroup.Trust.Domain.Scheduling;
using Newtonsoft.Json;
using System.Data.Entity;
using NetProGroup.Trust.Application.Scheduler;
using NetProGroup.Trust.DataManager.Exceptions;
using NetProGroup.Trust.Domain.Shared.Defines;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.DataManager.Configurations;

namespace NetProGroup.Trust.Application.BackgroundServices
{
    /// <summary>
    /// Background service for queued jobs.
    /// </summary>
    public sealed class QueuedJobService : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly GoLiveOptions _goLiveOptions;
        private readonly TrustOfficeOptions _trustOfficeOptions;

        private IServiceProvider _scopedServiceProvider;
        private IServiceScope _servicescope;

        /// <summary>
        /// Initializes a new instance of the <see cref="QueuedJobService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The serviceprovider to get new scoped services on execution.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="goLiveOptions">Options for GoLive settings.</param>
        /// <param name="trustOfficeOptions">Options for TrustOffice settings.</param>
        public QueuedJobService(IServiceProvider serviceProvider,
                                ILogger<QueuedJobService> logger,
                                IOptions<GoLiveOptions> goLiveOptions,
                                IOptions<TrustOfficeOptions> trustOfficeOptions)
        {
            ArgumentNullException.ThrowIfNull(goLiveOptions, nameof(goLiveOptions));
            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));

            // Use the container
            _serviceProvider = serviceProvider;

            _logger = logger;
            _goLiveOptions = goLiveOptions.Value;
            _trustOfficeOptions = trustOfficeOptions.Value;
        }

        /// <inheritdoc/>
        public override void Dispose()
        {
            if (_servicescope != null)
            {
                _servicescope.Dispose();
                _servicescope = null;
            }

            base.Dispose();
        }

        /// <inheritdoc/>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Delay before actually starting the service
            await Task.Delay(5000, stoppingToken);

            using var tickTimer = new PeriodicTimer(TimeSpan.FromSeconds(10));

            while (await tickTimer.WaitForNextTickAsync(stoppingToken))
            {
                _servicescope = _serviceProvider.CreateScope();
                _scopedServiceProvider = _servicescope.ServiceProvider;

                LockDTO jobLock = null;

                try
                {
                    // Small random delay to get out of a possible race condition
#pragma warning disable CA5394 // Do not use insecure randomness
                    await Task.Delay(new Random().Next(100, 1000), stoppingToken);
#pragma warning restore CA5394 // Do not use insecure randomness

                    jobLock = await AcquireLockAsync();

                    if (!jobLock.Id.HasValue)
                    {
                        throw new NoLockException();
                    }

                    var repository = _scopedServiceProvider.GetRequiredService<IQueuedJobsRepository>();

                    bool processedJobs = false;

                    if (_goLiveOptions.SendQueuedInvitations)
                    {
                        var queuedJobsForInvitation = (await repository.FindByConditionAsync(j => j.ProcessedAt == null && j.Job == QueuedJobKeys.Invitation,
                                                                                             q => q.OrderBy(j => j.Prio).ThenBy(j => j.CreatedAt).Take(50))).ToList();

                        foreach (var queuedJob in queuedJobsForInvitation)
                        {
                            processedJobs = true;

#pragma warning disable CA1031 // Do not catch general exception types
                            try
                            {
                                await SendInvitation(queuedJob);
                            }
                            catch (Exception ex)
                            {
                                queuedJob.ErrorMessage = ex.Message;
                            }
                            finally
                            {
                                queuedJob.ProcessedAt = DateTime.UtcNow;
                                await repository.SaveChangesAsync();
                            }
#pragma warning restore CA1031 // Do not catch general exception types
                        }
                    }

                    if (processedJobs)
                    {
                        tickTimer.Period = TimeSpan.FromSeconds(1);
                    }
                    else
                    {
                        tickTimer.Period = TimeSpan.FromSeconds(10);
                    }
                }
                catch (NoLockException ex)
                {
                    _logger.LogError(ex, "Job failed");
                }
                catch (LockNotFoundException ex)
                {
                    _logger.LogError(ex, "The lock for QueuedJob could no longer be found");
                    jobLock = null;
                }
#pragma warning disable CA1031 // Do not catch general exception types
                catch (Exception ex)
#pragma warning restore CA1031
                {
                    _logger.LogError(ex, "Job failed");
                }
                finally
                {
                    await ReleaseLockAsync(jobLock);
                }
            }
        }

        private async Task SendInvitation(QueuedJob job)
        {
            var manager = _scopedServiceProvider.GetRequiredService<IUserInvitationsDataManager>();

            var jobData = JsonConvert.DeserializeObject<QueuedJobData>(job.Data);

            if (_trustOfficeOptions.SendInvitationToUserEnabled)
            {
                await manager.SendInvitationToUserAsync(jobData.UserId, force: jobData.Force, reregistration: jobData.Reregistration, saveChanges: false);
            }
        }

        /// <summary>
        /// Acquires a lock to execute a scheduled job.
        /// </summary>
        /// <returns>LockDTO.</returns>
        private async Task<LockDTO> AcquireLockAsync()
        {
            var lockManager = _scopedServiceProvider.GetRequiredService<ILockManager>();

            var request = new Framework.Services.Locks.Models.AcquireLockRequestDTO
            {
                IdentityUserId = Guid.NewGuid(),
                EntityName = "QueuedJob",
                EntityId = Guid.Empty,
                Session = string.Empty
            };

            return await lockManager.AcquireLockAsync(request);
        }

        /// <summary>
        /// Releases a lock.
        /// </summary>
        /// <param name="theLock">The lock to release.</param>
        private async Task ReleaseLockAsync(LockDTO theLock)
        {
            if (theLock == null || !theLock.Id.HasValue)
            {
                return;
            }

#pragma warning disable CA1031 // Do not catch general exception types
            try
            {
                var lockManager = _scopedServiceProvider.GetRequiredService<ILockManager>();
                await lockManager.ReleaseLockAsync(theLock);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Releasing the lock failed");
            }
#pragma warning restore CA1031 // Do not catch general exception types
        }
    }
}
