﻿// <copyright file="UserPermissionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    ///     Data transfer object (DTO) representing a user permission.
    /// </summary>
    public class UserPermissionDTO
    {
        /// <summary>
        ///     Gets or sets the name of the permission.
        /// </summary>
        /// <remarks>
        ///     This property represents the name of the permission assigned to a user.
        /// </remarks>
        [PermissionName]
        public string PermissionName { get; set; } = null!;
    }
}