// <copyright file="ExportSubmissionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Request for exporting a submission.
    /// </summary>
    public class ExportSubmissionDTO
    {
        /// <summary>
        /// Gets or sets the list of submission ids to export.
        /// </summary>
        [Required]
        public IEnumerable<Guid> SubmissionIds { get; set; }

        /// <summary>
        /// Gets or sets the financial year to export.
        /// </summary>
        [Required]
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction to export.
        /// </summary>
        [Required]
        public string Jurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the module to export.
        /// </summary>
        [Required]
        public string Module { get; set; }
    }
}