﻿// <copyright file="MasterClientsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.MasterClients
{
    /// <summary>
    /// Use this controller for master client related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/master-clients")]
    public class MasterClientsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IMasterClientsAppService _masterClientsAppService;
        private readonly ISubmissionsAppService _submissionsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="masterClientsAppService">The service for master clients.</param>
        /// <param name="submissionsAppService">The service for submissions.</param>
        public MasterClientsController(
            ILogger<MasterClientsController> logger,
            IConfiguration configuration,
            ISubmissionsAppService submissionsAppService,
            IMasterClientsAppService masterClientsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;
            _submissionsAppService = submissionsAppService;
            _masterClientsAppService = masterClientsAppService;
        }

        /// <summary>
        /// Gets a list of masterclients with the jurisdiction info as a result of a search on both MasterClient Code and Jurisdiction name.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/master-clients?search={search}.
        /// </remarks>
        /// <param name="search">Term to use for searching the master clients.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the master clients.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Client_GetMasterClients")]
        [ProducesResponseType(typeof(MasterClientsSearchResultsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClients(string search)
        {
            MasterClientsSearchResultsDTO item = null;

            var result = await ProcessRequestAsync<MasterClientsSearchResultsDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _masterClientsAppService.SearchMasterClientsAsync(search);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets a list of companies with the 'Approved' onboardingstatus for the given masterclient.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/master-clients/{masterclientid}/companies?search={search}&amp;active=true.
        ///
        /// </remarks>
        /// <param name="masterClientId">The id of the masterclient to search the companies for.</param>
        /// <param name="search">Term to use for searching the companies.</param>
        /// <param name="active">Optional filter to select only active or inactive companies.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the companies.</returns>
        [HttpGet("{masterClientId}/companies")]
        [SwaggerOperation(OperationId = "Client_GetMasterClientCompanies")]
        [ProducesResponseType(typeof(CompaniesSearchResultsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClientCompanies(
            Guid masterClientId,
            string search,
            bool? active = null)
        {
            CompaniesSearchResultsDTO item = null;

            var result = await ProcessRequestAsync<CompaniesSearchResultsDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _masterClientsAppService.SearchCompaniesAsync(masterClientId, search, active, Domain.Shared.Enums.OnboardingStatus.Approved);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of submissions for the given master client for a specific module.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/master-clients/{masterClientId}/submissions
        /// .
        /// </remarks>
        /// <param name="masterClientId">The id of the master client to get the submissions for.</param>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet("{masterClientId}/submissions")]
        [SwaggerOperation(OperationId = "Client_GetMasterClientSubmissions")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubmissionsByMasterClient(
            Guid masterClientId,
            [FromQuery] ListSubmissionsByMasterClientRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                 validate: () => { },
            executeAsync: async (_) =>
            {
                return await _submissionsAppService.ListSubmissionsAsync(masterClientId, request);
            });

            return result.AsResponse();
        }
    }
}
