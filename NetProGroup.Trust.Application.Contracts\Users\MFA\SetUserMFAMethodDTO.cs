﻿// <copyright file="SetUserMFAMethodDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// A DTO holding the info for setting the MFA method.
    /// </summary>
    public class SetUserMFAMethodDTO
    {
        /// <summary>
        /// Gets or sets the id of the user that the MFA infomation is for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the method to use for MFA.
        /// </summary>
        public string MFAMethod { get; set; }
    }
}
