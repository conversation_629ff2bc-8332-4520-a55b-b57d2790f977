// <copyright file="IReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <summary>
    /// Interface for report jobs.
    /// </summary>
    public interface IReportJob : ICronJob, ITransientService;

    /// <summary>
    /// Interface for financial report job implementation.
    /// </summary>
    public interface IFinancialReportJob : IReportJob;

    /// <summary>
    /// Interface for financial report job implementation.
    /// </summary>
    public interface IBasicFinancialReportJob : IReportJob;

    /// <summary>
    /// Interface for companies without submissions report job implementation.
    /// </summary>
    public interface ICompaniesWithoutSubmissionsReportJob : IReportJob;

    /// <summary>
    /// Interface for contact information report job implementation.
    /// </summary>
    public interface IContactsInfoReportJob : IReportJob;

    /// <summary>
    /// Interface for payment report job implementation.
    /// </summary>
    public interface ISubmissionsNotPaidReportJob : IReportJob;
}
