// <copyright file="InvoicePaymentStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Payments.Invoices
{
    /// <summary>
    /// Represents the payment status of an invoice.
    /// </summary>
    public enum InvoicePaymentStatus
    {
        /// <summary>
        /// Indicates that the invoice has not been paid.
        /// </summary>
        UnPaid = 0,

        /// <summary>
        /// Indicates that the invoice has been paid.
        /// </summary>
        Paid = 1,
    }
}