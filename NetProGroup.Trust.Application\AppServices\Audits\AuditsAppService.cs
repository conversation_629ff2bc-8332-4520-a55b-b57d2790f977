// <copyright file="AuditsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Audits
{
    /// <summary>
    /// Implementation for Audits.
    /// </summary>
    public class AuditsAppService : IAuditsAppService
    {
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="AuditsAppService"/> class.
        /// </summary>
        /// <param name="workContext">The context for the current request.</param>
        /// <param name="systemAuditManager">Manager for audits.</param>
        /// <param name="securityManager">Manager for security.</param>
        public AuditsAppService(
            IWorkContext workContext,
            ISystemAuditManager systemAuditManager,
            ISecurityManager securityManager)
        {
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<AuditUnitOfWorkDTO>> GetAuditsAsync(Guid entityId, int pageNumber = 1, int pageSize = 20)
        {
            Check.NotDefaultOrNull<Guid>(entityId, nameof(entityId));

            await _securityManager.RequireManagementUserAsync();

            var request = new ListAuditRequest
            {
                EntityId = entityId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var response = await _systemAuditManager.ListAuditsAsync(request);

            var requestedForEntity = response.AuditItems.FirstOrDefault()?.Entities.SingleOrDefault(dto => new Guid(dto.EntityId) == entityId);
            await _securityManager.RequireManagementPermissionForAuditEntityAsync(requestedForEntity.EntityName);

            return new StaticPagedList<AuditUnitOfWorkDTO>(
                response.AuditItems,
                response.AuditItems.GetMetaData());
        }

        /// <inheritdoc/>
        public async Task<AuditUnitOfWorkDTO> GetAuditAsync(Guid auditId)
        {
            Check.NotDefaultOrNull<Guid>(auditId, nameof(auditId));

            await _securityManager.RequireManagementUserAsync();

            var audit = await _systemAuditManager.GetAuditAsync(auditId);
            var requestedForEntity = audit.Entities.FirstOrDefault();

            await _securityManager.RequireManagementPermissionForAuditEntityAsync(requestedForEntity.EntityName);

            return audit;
        }
    }
}
