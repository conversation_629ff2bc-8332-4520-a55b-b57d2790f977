﻿// <copyright file="ConfirmMFAResetResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// DTO for the result of confirming the MFA reset.
    /// </summary>
    public class ConfirmMFAResetResultDTO
    {
        /// <summary>
        /// Gets or sets the result of the confirmation.
        /// </summary>
        public string ConfirmationCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the confirmation succeeded and the MFA is reset.
        /// </summary>
        public bool Success { get; set; }
    }
}
