﻿// <copyright file="MessageTokenProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Messaging.Tokens;
using NetProGroup.Framework.Services.Extensions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Tools;

namespace NetProGroup.Trust.Application.Communication
{
    /// <summary>
    /// Use the TokenProvider to fill the tokens list with commonly used tokens.
    /// </summary>
    public class MessageTokenProvider : TokenProvider
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MessageTokenProvider"/> class.
        /// </summary>
        public MessageTokenProvider()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageTokenProvider"/> class.
        /// </summary>
        /// <param name="tokens">Existing list of tokens to append to.</param>
        public MessageTokenProvider(TokenList tokens) : base(tokens)
        {
        }

        /// <summary>
        /// Adds common tokens to the list.
        /// </summary>
        /// <param name="tokens">List of tokens to add to (optional).</param>
        /// <returns>MessageTokenProvider for chaining.</returns>
        public virtual MessageTokenProvider AddCommonTokens(TokenList tokens = null)
        {
            if (tokens == null)
            {
                tokens = base.Tokens;
            }

            Check.NotNull(tokens, nameof(tokens));

            tokens.Add(new Token("currentDate", FormatDate(DateTime.Today)));
            tokens.Add(new Token("currentTime", FormatTime(DateTime.UtcNow.ToLocalTime())));
            tokens.Add(new Token("currentDateTime", FormatDateTime(DateTime.UtcNow.ToLocalTime())));

            tokens.Add(new Token("currentTimeUtc", FormatTime(DateTime.UtcNow.AsUtcTime())));
            tokens.Add(new Token("currentDateTimeUtc", FormatDateTime(DateTime.UtcNow.AsUtcTime())));

            return this;
        }

        /// <summary>
        /// Adds user tokens to the list.
        /// </summary>
        /// <param name="user">User to create tokens from.</param>
        /// <param name="tokens">List of tokens to add to (optional).</param>
        /// <returns>MessageTokenProvider for chaining.</returns>
        public virtual MessageTokenProvider AddUserTokens([NotNull] ApplicationUser user, TokenList tokens = null)
        {
            if (tokens == null)
            {
                tokens = base.Tokens;
            }

            Check.NotNull(tokens, nameof(tokens));
            Check.NotNull(user, nameof(user));

            tokens.Add(new Token("fullName", GetDisplayName(user)));
            tokens.Add(new Token("userName", user.UserName));
            tokens.Add(new Token("userGivenName", user.Name));
            tokens.Add(new Token("userSurname", user.Surname));

            return this;
        }

        /// <summary>
        /// Adds user tokens to the list.
        /// </summary>
        /// <param name="user">User to create tokens from.</param>
        /// <param name="tokens">List of tokens to add to (optional).</param>
        /// <returns>MessageTokenProvider for chaining.</returns>
        public virtual MessageTokenProvider AddUserTokens([NotNull] ApplicationUserDTO user, TokenList tokens = null)
        {
            if (tokens == null)
            {
                tokens = base.Tokens;
            }

            Check.NotNull(tokens, nameof(tokens));
            Check.NotNull(user, nameof(user));

            tokens.Add(new Token("fullName", user.GetDisplayName()));
            tokens.Add(new Token("userName", user.Username));
            tokens.Add(new Token("userGivenName", user.Name));
            tokens.Add(new Token("userSurname", user.Surname));
            tokens.Add(new Token("email", user.Email));

            return this;
        }

        /// <summary>
        /// Adds custom date token to the list.
        /// </summary>
        /// <param name="customKey">A custom date key to add.</param>
        /// <param name="value">The DateTime value to set.</param>
        /// <param name="tokens">List of tokens to add to (optional).</param>
        /// <returns>MessageTokenProvider for chaining.</returns>
        public virtual MessageTokenProvider AddCustomDateToken(string customKey, DateTime value, TokenList tokens = null)
        {
            if (tokens == null)
            {
                tokens = base.Tokens;
            }

            Check.NotNull(tokens, nameof(tokens));
            Check.NotNull(customKey, nameof(customKey));
            Check.NotDefaultOrNull<DateTime>(value, nameof(value));

            tokens.Add(new Token(customKey, FormatDate(value)));

            return this;
        }

        private static string GetDisplayName(ApplicationUser user)
        {
            ArgumentNullException.ThrowIfNull(user, nameof(user));
            if (string.IsNullOrWhiteSpace(user.GetDisplayName()))
            {
                return user.UserName;
            }
            else
            {
                return user.GetDisplayName();
            }
        }
    }
}
