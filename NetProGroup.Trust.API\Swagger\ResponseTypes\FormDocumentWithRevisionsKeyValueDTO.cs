﻿// <copyright file="FormDocumentWithRevisionsKeyValueDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Forms;

namespace NetProGroup.Trust.API.Swagger.ResponseTypes
{
    /// <summary>
    /// Version of <see cref="FormDocumentWithRevisionsDTO"/> that includes a <see cref="FormDocumentRevisionKeyValueDTO"/> form document.
    /// Intended to generate correct OpenAPI documentation.
    /// </summary>
    public class FormDocumentWithRevisionsKeyValueDTO : FormDocumentWithRevisionsDTO
    {
        /// <summary>
        /// Gets or sets the collection of revisions for the document.
        /// Override of the type of the revisions, to display the correct type in OpenAPI documentation.
        /// </summary>
        public new IReadOnlyCollection<FormDocumentRevisionKeyValueDTO> Revisions { get; set; }
    }
}