﻿// <copyright file="PagedRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Base class for a paged request.
    /// </summary>
    public abstract class PagedRequest
    {
        /// <summary>
        /// Gets or sets the number of the page to get.
        /// </summary>
        public int PageNumber { get; set; } = PagingSettings.DefaultPageNumber;

        /// <summary>
        /// Gets or sets the size of the page to get.
        /// </summary>
        public int PageSize { get; set; } = PagingSettings.DefaultPageSize;
    }
}
