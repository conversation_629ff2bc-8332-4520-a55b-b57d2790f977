﻿// <copyright file="FormDocumentRevisionKeyValueDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Forms;

namespace NetProGroup.Trust.API.Swagger.ResponseTypes
{
    /// <summary>
    /// Version of <see cref="FormDocumentRevisionDTO"/> that includes a <see cref="KeyValueFormBuilderDTO"/> form document.
    /// Intended to generate correct OpenAPI documentation.
    /// </summary>
    public class FormDocumentRevisionKeyValueDTO : FormDocumentRevisionDTO
    {
        /// <summary>
        /// Gets or sets the FormBuilder.
        /// Override of the type of the form builder, to display the correct type in OpenAPI documentation.
        /// </summary>
        public new KeyValueFormBuilderDTO FormBuilder { get; set; }
    }
}