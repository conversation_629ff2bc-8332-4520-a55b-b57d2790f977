﻿// <copyright file="CustomMiddlewareExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.API.Middleware
{
    /// <summary>
    /// Extensions for the custom middleware.
    /// </summary>
    public static class CustomMiddlewareExtensions
    {
        /// <summary>
        /// Method to add the WorkContext middleware to the pipeline.
        /// </summary>
        /// <param name="app">Instance of ApplicationBuilder.</param>
        /// <returns>IApplicationBuilder for chaining.</returns>
        public static IApplicationBuilder UseWorkContext(this IApplicationBuilder app)
        {
            app.UseMiddleware<WorkContextMiddleware>();

            return app;
        }
    }
}
