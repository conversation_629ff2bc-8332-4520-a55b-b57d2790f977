// <copyright file="PaymentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Data Transfer Object for representing payment details.
    /// </summary>
    public class PaymentDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the payment.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the legal entity associated with the payment.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the name of the company associated with the payment.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the financial period for the payment (e.g., fiscal year).
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number of the company.
        /// </summary>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the payment was made or processed.
        /// </summary>
        public DateTime DateTime { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the currency used in the payment.
        /// </summary>
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the symbol of the currency used in the payment (e.g., USD, EUR).
        /// </summary>
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// Gets or sets the total amount of the payment.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the status of the payment (e.g., Pending, Completed, Failed).
        /// </summary>
        public PaymentStatus Status { get; set; }
    }
}