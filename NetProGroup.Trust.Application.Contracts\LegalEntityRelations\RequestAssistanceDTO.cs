﻿// <copyright file="RequestAssistanceDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations
{
    /// <summary>
    /// Model for requesting a assistance.
    /// </summary>
    public class RequestAssistanceDTO
    {
        /// <summary>
        /// Gets or sets the id of the legal entity to request assistence for.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the type of requested assistance.
        /// </summary>
        public LegalEntityRelationAssistanceRequestType AssistanceRequestType { get; set; }

        /// <summary>
        /// Gets or sets the comments for the assistance request.
        /// </summary>
        public string AssistanceRequestComments { get; set; }
    }
}
