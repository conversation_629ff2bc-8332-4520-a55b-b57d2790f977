// <copyright file="IPaymentProcessorFacade.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Payments.Request;

namespace NetProGroup.Trust.Application.Contracts.Payments.Processor
{
    /// <summary>
    /// Defines a facade for processing payments across different payment providers.
    /// This interface abstracts the payment processing logic, allowing for a unified
    /// approach to handling payments regardless of the underlying payment processor.
    /// </summary>
    public interface IPaymentProcessorFacade : IScopedService
    {
        /// <summary>
        /// Processes a payment asynchronously and updates the associated transaction.
        /// </summary>
        /// <param name="request">The payment request containing all necessary information to process the payment.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the <see cref="NetProGroup.Framework.Services.Payments.Models.PaymentsAPI.V2.StartPaymentResponse"/> which includes the outcome of the payment processing
        /// The response is wrapped in an <see cref="CreateTransactionResponseDTO"/>.
        /// </returns>
        /// <remarks>
        /// This method handles the entire lifecycle of a payment, from initial processing to updating
        /// the transaction status. It is designed to work with various payment providers, abstracting
        /// away the differences in their individual APIs.
        /// </remarks>
        /// <example>
        /// Here's a basic example of how to use this method:
        /// <code>
        /// var paymentRequest = new PaymentRequest { /* populate with payment details */ };
        /// var transactionId = Guid.NewGuid();
        /// var result = await _paymentProcessorFacade.HandlePaymentAsync(paymentRequest, transactionId);
        /// if (result.IsSuccessful)
        /// {
        ///     // Handle successful payment
        /// }
        /// else
        /// {
        ///     // Handle failed payment
        /// }
        /// </code>
        /// </example>
        /// <exception cref="ArgumentNullException">Thrown if <paramref name="request"/> is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the payment processing fails due to a system error.</exception>
        Task<CreateTransactionResponseDTO> HandleStartPaymentAsync(CreateTransactionRequestDTO request);

        /// <summary>
        /// Processes a payment submission asynchronously and returns the result of the operation.
        /// </summary>
        /// <param name="request">The payment request containing all necessary information to submit the payment.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the
        /// <see cref="SubmitPaymentResponseDTO"/> which includes the outcome of the payment submission.
        /// </returns>
        /// <remarks>
        /// This method is responsible for managing the payment submission process, ensuring that the
        /// payment request is validated and processed correctly. It abstracts the interaction with
        /// payment providers and handles any necessary transformations of the request data.
        /// </remarks>
        /// <example>
        /// Here's a basic example of how to use this method:
        /// <code>
        /// var paymentRequest = new SubmitPaymentRequestDTO { /* populate with payment details */ };
        /// var result = await _paymentService.HandleSubmitPaymentAsync(paymentRequest);
        /// if (result.IsSuccessful)
        /// {
        ///     // Handle successful payment submission
        /// }
        /// else
        /// {
        ///     // Handle failed payment submission
        /// }
        /// </code>
        /// </example>
        /// <exception cref="ArgumentNullException">Thrown if <paramref name="request"/> is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the payment submission fails due to a system error.</exception>
        Task<SubmitPaymentResponseDTO> HandleCommitPaymentAsync(SubmitPaymentRequestDTO request);
    }
}