// <copyright file="ApplicationInsightsDependencyTracker.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;

namespace NetProGroup.Trust.Application.Common
{
    /// <summary>
    /// Utility class for tracking dependencies in Application Insights.
    /// </summary>
    public class ApplicationInsightsDependencyTracker : IApplicationInsightsDependencyTracker
    {
        private readonly TelemetryClient _telemetryClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationInsightsDependencyTracker"/> class.
        /// </summary>
        /// <param name="telemetryClient">The telemetry client for app insights tracking.</param>
        public ApplicationInsightsDependencyTracker(TelemetryClient telemetryClient)
        {
            _telemetryClient = Check.NotNull(telemetryClient, nameof(telemetryClient));
        }

        /// <summary>
        /// Tracks a dependency operation and returns its result.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="operation">The operation to track.</param>
        /// <param name="type">The type of dependency (e.g., "Email", "Background", "Database").</param>
        /// <param name="target">The target system (e.g., "SendGrid", "SMTP", "SQL Server").</param>
        /// <param name="data">Additional data about the operation.</param>
        /// <returns>The result of the operation.</returns>
        public async Task<T> TrackDependencyAsync<T>(
            string operationName,
            Func<Task<T>> operation,
            string type = null,
            string target = null,
            string data = null)
        {
            Check.NotNull(operation, nameof(operation));

            var dependencyOperation = _telemetryClient.StartOperation<DependencyTelemetry>(operationName);

            if (!string.IsNullOrEmpty(type))
            {
                dependencyOperation.Telemetry.Type = type;
            }

            if (!string.IsNullOrEmpty(target))
            {
                dependencyOperation.Telemetry.Target = target;
            }

            if (!string.IsNullOrEmpty(data))
            {
                dependencyOperation.Telemetry.Data = data;
            }

            try
            {
                var result = await operation();

                // For boolean results, we can set success directly
                if (result is bool boolResult)
                {
                    dependencyOperation.Telemetry.Success = boolResult;
                }
                else
                {
                    dependencyOperation.Telemetry.Success = true;
                }

                return result;
            }
            catch (Exception ex)
            {
                dependencyOperation.Telemetry.Success = false;
                dependencyOperation.Telemetry.ResultCode = "Failed";
                _telemetryClient.TrackException(ex);
                throw;
            }
            finally
            {
                _telemetryClient.StopOperation(dependencyOperation);
            }
        }

        /// <summary>
        /// Tracks a dependency operation that doesn't return a result.
        /// </summary>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="operation">The operation to track.</param>
        /// <param name="type">The type of dependency (e.g., "Email", "Background", "Database").</param>
        /// <param name="target">The target system (e.g., "SendGrid", "SMTP", "SQL Server").</param>
        /// <param name="data">Additional data about the operation.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task TrackDependencyAsync(
            string operationName,
            Func<Task> operation,
            string type = null,
            string target = null,
            string data = null)
        {
            Check.NotNull(operation, nameof(operation));

            var dependencyOperation = _telemetryClient.StartOperation<DependencyTelemetry>(operationName);

            if (!string.IsNullOrEmpty(type))
            {
                dependencyOperation.Telemetry.Type = type;
            }

            if (!string.IsNullOrEmpty(target))
            {
                dependencyOperation.Telemetry.Target = target;
            }

            if (!string.IsNullOrEmpty(data))
            {
                dependencyOperation.Telemetry.Data = data;
            }

            try
            {
                await operation();
                dependencyOperation.Telemetry.Success = true;
            }
            catch (Exception ex)
            {
                dependencyOperation.Telemetry.Success = false;
                dependencyOperation.Telemetry.ResultCode = "Failed";
                _telemetryClient.TrackException(ex);
                throw;
            }
            finally
            {
                _telemetryClient.StopOperation(dependencyOperation);
            }
        }
    }
}
