// <copyright file="AnnouncementDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Documents.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Represent an announcement document.
    /// </summary>
    public class AnnouncementDocumentDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the document id as Guid.
        /// </summary>
        public Guid DocumentId { get; set; }

        /// <summary>
        /// Gets or sets the document entity.
        /// </summary>
        public virtual DocumentDTO Document { get; set; }

        /// <summary>
        /// Gets or sets the announcement id as Guid.
        /// </summary>
        public Guid AnnouncementId { get; set; }
    }
}