﻿// <copyright file="PermissionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Users;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Permissions
{
    /// <summary>
    /// Use this controller to get the permissiosn for the calling user (x-userid).
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class PermissionsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IUsersAppService _usersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="PermissionsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="usersAppService">The service for users.</param>
        public PermissionsController(
            ILogger<PermissionsController> logger,
            IConfiguration configuration,
            IUsersAppService usersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _usersAppService = usersAppService;
        }

        /// <summary>
        /// Retrieves the permissions for the current user within a specific jurisdiction context.
        /// </summary>
        /// <param name="jurisdictionId">The optional unique identifier jurisdiction.</param>
        /// <returns>An <see cref="IActionResult"/> containing a collection of user permissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Client_GetPermissions")]
        [ProducesResponseType(typeof(IList<UserPermissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPermissions(Guid? jurisdictionId = null)
        {
            IList<UserPermissionDTO> items = new List<UserPermissionDTO>();

            var result = await ProcessRequestAsync(

                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    items = await _usersAppService.GetPermissionsAsync(jurisdictionId);
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }
    }
}
