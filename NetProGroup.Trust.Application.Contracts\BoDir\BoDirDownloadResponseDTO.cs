// <copyright file="BoDirDownloadResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.BoDir
{
    /// <summary>
    /// The response model for downloading a BO/Dir report.
    /// </summary>
    public class BoDirDownloadResponseDTO
    {
        /// <summary>
        /// Gets or sets the file content.
        /// </summary>
        public MemoryStream FileContent { get; set; }

        /// <summary>
        /// Gets or sets the file name.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the content type.
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// Gets or sets the extension.
        /// </summary>
        public string Extension { get; set; }
    }
}