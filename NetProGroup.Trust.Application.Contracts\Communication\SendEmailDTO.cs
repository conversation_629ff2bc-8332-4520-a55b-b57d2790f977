﻿// <copyright file="SendEmailDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Communication
{
    /// <summary>
    /// Data Transfer Object for sending an email.
    /// </summary>
    public class SendEmailDTO
    {
        /// <summary>
        /// Gets or sets the email address for the recipient.
        /// </summary>
        [EmailAddress]
        [Required]
        public string RecipientEmailAddress { get; set; }

        /// <summary>
        /// Gets or sets the subject for the email.
        /// </summary>
        [Required]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets the body for the email.
        /// </summary>
        [Required]
        public string Body { get; set; }

        /// <summary>
        /// Gets or sets the optional id of the legal entity that the email is related to (for activitylog).
        /// </summary>
        public Guid? LegalEntityId { get; set; }
    }
}
