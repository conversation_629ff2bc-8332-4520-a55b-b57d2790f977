﻿// <copyright file="CompanyModuleDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a module.
    /// </summary>
    public class CompanyModuleDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the (display)name of the module.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the key of the module.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is active (global).
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is enabled for the entity.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is enabled for the entity.
        /// </summary>
        public bool IsApproved { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is enabled for the jurisdiction.
        /// </summary>
        public bool JurisdictionIsEnabled { get; set; }
    }
}
