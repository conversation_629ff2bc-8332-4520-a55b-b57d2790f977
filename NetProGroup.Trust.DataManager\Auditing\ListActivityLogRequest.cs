﻿// <copyright file="ListActivityLogRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Defines;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Request model for getting ActivityLog data.
    /// </summary>
    public class ListActivityLogRequest : PagedRequest
    {
        /// <summary>
        /// Gets or sets the period that specifies a date range to get the activities for.
        /// </summary>
        public Period Period { get; set; }

        /// <summary>
        /// Gets or sets the id of the ActivityLog to get.
        /// </summary>
        public Guid? ActivityLogId { get; set; }

        /// <summary>
        /// Gets or sets the id of the entity to filter activity logs for.
        /// </summary>
        public Guid? EntityId { get; set; }

        /// <summary>
        /// Gets or sets the activity type to filter activity logs for.
        /// </summary>
        public string ActivityType { get; set; }
    }
}
