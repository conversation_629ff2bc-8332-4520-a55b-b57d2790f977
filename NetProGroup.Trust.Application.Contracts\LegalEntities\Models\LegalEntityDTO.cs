﻿// <copyright file="LegalEntityDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities
{
    /// <summary>
    /// Represents a LegalEntity.
    /// </summary>
    public abstract class LegalEntityDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the id of the Jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the name of the Jurisdiction.
        /// </summary>
        public string JurisdictionName { get; set; }

        /// <summary>
        /// Gets or sets the code of the LegalEntity.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the name of the LegalEntity.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the status of the entiy as synced from ViewPoint.
        /// </summary>
        public string VPEntityStatus { get; set; }

        /// <summary>
        /// Gets or sets the type of the entity.
        /// </summary>
        public string EntityType { get; set; }
    }
}
