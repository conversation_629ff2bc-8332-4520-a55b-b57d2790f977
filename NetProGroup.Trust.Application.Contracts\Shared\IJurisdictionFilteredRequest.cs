// <copyright file="IJurisdictionFilteredRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Represents a request that is filtered by jurisdiction.
    /// </summary>
    public interface IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the Jurisdiction IDs to filter on.
        /// </summary>
        List<Guid> AuthorizedJurisdictionIDs { get; set; }
    }
}