﻿// <copyright file="UserSignUpInfo.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable SA1402 // File may only contain a single type
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
#pragma warning disable IDE1006 // Naming Styles
    public class UserSignUpInfo
    {
        public UserSignUpInfo_Attributes attributes { get; set; }

        public List<UserSignUpInfo_Identities> identities { get; set; }
    }

    public class UserSignUpInfo_Attributes
    {
        public UserSignUpInfo_Attribute email { get; set; }

        public UserSignUpInfo_Attribute displayName { get; set; }

        [JsonPropertyName("extension_4c89b91d25b24aa3b391f28d9de10e13_MasterClientCode")]
        public UserSignUpInfo_Attribute MasterClientCode { get; set; }
    }

    public class UserSignUpInfo_Attribute
    {
        public string value { get; set; }

        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        public string attributeType { get; set; }
    }

    public class UserSignUpInfo_Identities
    {
        public string signInType { get; set; }

        public string issuer { get; set; }

        public string issuerAssignedId { get; set; }
    }
#pragma warning restore IDE1006 // Naming Styles
#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
#pragma warning restore SA1402 // File may only contain a single type
}
