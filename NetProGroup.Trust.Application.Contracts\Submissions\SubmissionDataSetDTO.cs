﻿// <copyright file="SubmissionDataSetDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents a submission dataset (for KeyValueForm).
    /// </summary>
    public class SubmissionDataSetDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionDataSetDTO"/> class.
        /// </summary>
        public SubmissionDataSetDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the dataset, being a dictionary with KeyValue pairs.
        /// </summary>
        public Dictionary<string, string> DataSet { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets the document ids related to the submission.
        /// </summary>
        public List<Guid> DocumentIds { get; set; }
    }
}
