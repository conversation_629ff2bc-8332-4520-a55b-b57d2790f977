// <copyright file="CreateAnnouncementDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Documents;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Represent the necessary data to create an announcement document.
    /// </summary>
    public class CreateAnnouncementDocumentDTO : CreateDocumentDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the upload of documents is finished or not.
        /// </summary>
        /// <remarks>
        /// If the value is true the announcement status will be set to Scheduled or Sent.
        /// If the value is false the announcement status will remain Draft.
        /// </remarks>
        public bool UploadComplete { get; set; }
    }
}