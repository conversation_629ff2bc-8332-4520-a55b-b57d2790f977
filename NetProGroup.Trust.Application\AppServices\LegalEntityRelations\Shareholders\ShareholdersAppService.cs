﻿// <copyright file="ShareholdersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using X.PagedList;

namespace NetProGroup.Trust.Application.LegalEntityRelations.Shareholders
{
    /// <summary>
    /// Application service for Shareholders.
    /// </summary>
    public class ShareholdersAppService : IShareholdersAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IShareholdersDataManager _dataManager;
        private readonly ISecurityManager _securityManager;
        private readonly IModulesRepository _modulesRepository;
        private readonly ILegalEntityModulesRepository _legalEntitiesModulesRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="ShareholdersAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The current workcontext.</param>
        /// <param name="systemAuditManager">The AuditManager for logging activities.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="modulesRepository">The repository for modules.</param>
        /// <param name="legalEntitiesModulesRepository">The repository for legal entities modules.</param>
        public ShareholdersAppService(ILogger<ShareholdersAppService> logger,
                                      IMapper mapper,
                                      IWorkContext workContext,
                                      ISystemAuditManager systemAuditManager,
                                      IShareholdersDataManager dataManager,
                                      ISecurityManager securityManager,
                                      IModulesRepository modulesRepository,
                                      ILegalEntityModulesRepository legalEntitiesModulesRepository)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;
            _dataManager = dataManager;
            _securityManager = securityManager;
            _modulesRepository = modulesRepository;
            _legalEntitiesModulesRepository = legalEntitiesModulesRepository;
        }

        /// <inheritdoc/>
        public async Task<ShareholderDTO> GetShareholderAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var shareholderDto = await _dataManager.GetShareholderAsync(uniqueRelationId);

            return shareholderDto;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ShareholderDTO>> ListShareholdersAsync(Guid legalEntityId, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);
            await CheckModuleEnabledForLegalEntity(legalEntityId);

            var request = new DataManager.LegalEntityRelations.Shareholders.RequestResponses.ListShareholdersRequest { LegalEntityId = legalEntityId, PageNumber = pageNumber, PageSize = pageSize };
            var response = await _dataManager.ListShareholdersAsync(request);

            return response.ShareholderItems;
        }

        /// <inheritdoc/>
        public async Task<ShareholderComparisonDTO> GetShareholderForComparisonAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var shareholderComparisonDto = await _dataManager.GetShareholderForComparisonAsync(uniqueRelationId);

            return shareholderComparisonDto;
        }

        /// <inheritdoc/>
        public async Task<ShareholderDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate)
        {
            ArgumentNullException.ThrowIfNull(requestUpdate, nameof(requestUpdate));

            await CheckModuleEnabledAndAccessToCompany(requestUpdate.UniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestUpdateRequest
            {
                UniqueRelationId = requestUpdate.UniqueRelationId,
                UserId = _workContext.IdentityUserId.Value,
                UpdateRequestType = requestUpdate.UpdateRequestType,
                UpdateRequestComments = requestUpdate.UpdateRequestComments
            };

            var response = await _dataManager.RequestUpdateAsync(request);

            return await _dataManager.GetShareholderAsync(requestUpdate.UniqueRelationId);
        }

        /// <inheritdoc/>
        public async Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance)
        {
            ArgumentNullException.ThrowIfNull(requestAssistance, nameof(requestAssistance));

            await _securityManager.RequireClientAccessToCompanyAsync(requestAssistance.LegalEntityId);
            await CheckModuleEnabledForLegalEntity(requestAssistance.LegalEntityId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestAssistanceRequest
            {
                LegalEntityId = requestAssistance.LegalEntityId,
                UserId = _workContext.IdentityUserId.Value,
                AssistanceRequestType = requestAssistance.AssistanceRequestType,
                AssistanceRequestComments = requestAssistance.AssistanceRequestComments
            };

            var response = await _dataManager.RequestAssistanceAsync(request);

            return;
        }

        /// <inheritdoc/>
        public async Task<ShareholderDTO> SetConfirmationAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.ConfirmationRequest
            {
                UniqueRelationId = uniqueRelationId,
                UserId = _workContext.IdentityUserId.Value
            };

            var response = await _dataManager.ConfirmDataAsync(request);

            return await _dataManager.GetShareholderAsync(uniqueRelationId);
        }

        private async Task CheckModuleEnabledForLegalEntity(Guid legalEntityId)
        {
            var module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.BODirectors);
            await _legalEntitiesModulesRepository.CheckModuleEnabledForLegalEntityAsync(legalEntityId, module.Id);
        }

        private async Task CheckModuleEnabledAndAccessToCompany(string uniqueRelationId)
        {
            var shareholder = await _dataManager.FindShareholderAsync(uniqueRelationId);
            await _securityManager.RequireClientAccessToCompanyAsync(shareholder.LegalEntityId);

            await CheckModuleEnabledForLegalEntity(shareholder.LegalEntityId);
        }
    }
}
