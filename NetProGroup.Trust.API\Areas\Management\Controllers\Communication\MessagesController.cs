﻿// <copyright file="MessagesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Communication;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Communication
{
    /// <summary>
    /// Use this controller for message related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/Communication/[Controller]")]
    public class MessagesController : TrustAPIControllerBase
    {
        private readonly ICommunicationAppService _communicationAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessagesController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="communicationAppService">The service for communication.</param>
        public MessagesController(
            ILogger<MessagesController> logger,
            ICommunicationAppService communicationAppService)
            : base(logger)
        {
            _communicationAppService = communicationAppService;
        }

        /// <summary>
        /// Sends an email.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/communication/messages/email
        ///     {
        ///         "RecipientEmailAddress": "",
        ///         "Subject": "",
        ///         "Body": "",
        ///     }.
        /// </remarks>
        /// <param name="model">The DTO holding the parameters for the new email message.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("email")]
        [SwaggerOperation(OperationId = "SendEmailMessage", Summary = "Sends an email.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SendEmailMessage(SendEmailDTO model)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotNullOrEmpty(model.RecipientEmailAddress, nameof(model.RecipientEmailAddress));
                    Check.NotNullOrEmpty(model.Subject, nameof(model.Subject));
                    Check.NotNullOrEmpty(model.Body, nameof(model.Body));
                },
                executeAsync: async () =>
                {
                    await _communicationAppService.SendEmailAsync(model);
                });

            return result.AsNoContentResponse();
        }
    }
}
