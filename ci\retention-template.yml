parameters:
  - name: condition
    type: string
    default: succeeded()

jobs:
- job: RetainRelease
  condition: ${{ parameters.condition }}
  steps:
  - task: PowerShell@2
    displayName: 'Retain pipeline run'
    inputs:
      failOnStderr: true
      targetType: 'inline'
      script: |
        $contentType = "application/json";
        $headers = @{ Authorization = 'Bearer $(System.AccessToken)' };
        $rawRequest = @{ daysValid = 365 * 2; definitionId = $(System.DefinitionId); ownerId = 'User:$(Build.RequestedForId)'; protectPipeline = $true; runId = $(Build.BuildId) };
        $request = ConvertTo-Json @($rawRequest);
        $uri = "$(System.CollectionUri)$(System.TeamProject)/_apis/build/retention/leases?api-version=6.0-preview.1";
        Invoke-RestMethod -uri $uri -method POST -Headers $headers -ContentType $contentType -Body $request;
