﻿// <copyright file="RequestHeadersMiddleware.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Serilog;

namespace NetProGroup.Trust.API.Middleware
{
    /// <summary>
    /// Middelware for inspecting request headers.
    /// </summary>
    public class RequestHeadersMiddleware
    {
        private readonly RequestDelegate _next;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestHeadersMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next middleware to call after Invoke completes.</param>
        public RequestHeadersMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        /// <summary>
        /// Invokation of the middleware.
        /// </summary>
        /// <param name="context">The HttpContext for the current request.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task Invoke(HttpContext context)
        {
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            var headers = context.Request.Headers;
            foreach (var header in headers)
            {
                Log.Logger.Information("Header: {HeaderKey} = {HeaderValue}", header.Key, header.Value);
            }

            await _next(context);
        }
    }
}
