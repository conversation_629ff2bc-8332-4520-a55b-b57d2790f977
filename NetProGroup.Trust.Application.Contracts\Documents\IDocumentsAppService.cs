// <copyright file="IDocumentsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Documents.Models;

namespace NetProGroup.Trust.Application.Contracts.Documents
{
    /// <summary>
    /// Interface for documents operations within the application.
    /// </summary>
    public interface IDocumentsAppService : IScopedService
    {
        /// <summary>
        /// Create a single document.
        /// </summary>
        /// <param name="data">The necessary data to create a document.</param>
        /// <returns>The id of the created Document as Guid.</returns>
        Task<Guid> CreateDocumentAsync(CreateDocumentDTO data);

        /// <summary>
        /// Retreive a document given its id.
        /// </summary>
        /// <param name="documentId">The document id as Guid.</param>
        /// <param name="includeData">Determine if the data for the document needs to be included.</param>
        /// <returns>The Document entity as DocumentDTO.</returns>
        Task<DocumentDTO> GetDocumentByIdAsync(Guid documentId, bool includeData);

        /// <summary>
        /// Retreive a list of documents given a list of Guid containing their id.
        /// </summary>
        /// <param name="documentIds">A list of ids containing the document ids as List of Guid.</param>
        /// <param name="includeData">Determine if the data for the documents needs to be included.</param>
        /// <returns>A list of Document entities as List of DocumentDTO.</returns>
        Task<List<DocumentDTO>> GetDocumentsByIdAsync(List<Guid> documentIds, bool includeData);
    }
}