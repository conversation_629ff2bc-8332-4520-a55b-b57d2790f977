// <copyright file="CreatePaymentResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Application.Contracts.Payments.Response
{
    /// <summary>
    /// Represents the response object returned after a payment transaction is successfully created.
    /// </summary>
    public class CreatePaymentResponseDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the payment.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the currency used in the payment.
        /// </summary>
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the payment was made or processed.
        /// </summary>
        public DateTime DateTime { get; set; }

        /// <summary>
        /// Gets or sets the status of the payment (e.g., Pending, Completed, Failed).
        /// </summary>
        public PaymentStatus Status { get; set; }
    }
}