// <copyright file="ActivityTypeDescriptionMapper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Defines;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Maps activity types to descriptions.
    /// </summary>
    public static class ActivityTypeDescriptionMapper
    {
        /// <summary>
        /// Gets the action to display for the activity type.
        /// </summary>
        /// <param name="activityType">The activity tpe to get the action description for.</param>
        /// <returns>ActionDescription.</returns>
        public static string GetActionDescriptionFromActivityType(string activityType)
        {
            var result = activityType switch
            {
                ActivityLogActivityTypes.MasterClientAdded => "Added a Master Client",
                ActivityLogActivityTypes.MasterClientLegalEntityAdded => "Added a Legal Entity to a Master Client",
                ActivityLogActivityTypes.MasterClientLegalEntityRemoved => "Removed a Legal Entity from a Master Client",
                ActivityLogActivityTypes.MasterClientUserAdded => "Added a User to a Master Client",
                ActivityLogActivityTypes.MasterClientUserRemoved => "Removed a User from a Master Client",
                ActivityLogActivityTypes.UserAuthenticate => "User authenticated",
                ActivityLogActivityTypes.UserLogout => "User logged out",
                ActivityLogActivityTypes.UserAdd => "User added",
                ActivityLogActivityTypes.UserRegister => "User registered",
                ActivityLogActivityTypes.UserAddedToMasterClient => "User added to a Master Client",
                ActivityLogActivityTypes.UserRemovedFromMasterClient => "User removed from a Master Client",
                ActivityLogActivityTypes.UserMFAResetRequested => "Reset of MFA by user requested",
                ActivityLogActivityTypes.UserMFAResetConfirmed => "Reset of MFA by user confirmed",
                ActivityLogActivityTypes.UserMFAReset => "Reset of MFA by management user",
                ActivityLogActivityTypes.UserTermsConditionsAccepted => "Terms and Conditions accepted by user",
                ActivityLogActivityTypes.UserBlocked => "User is blocked",
                ActivityLogActivityTypes.UserUnblocked => "User is unblocked",
                ActivityLogActivityTypes.BeneficialOwnerDataConfirmed => "Beneficial owner data confirmed",
                ActivityLogActivityTypes.BeneficialOwnerDataUpdateRequested => "Beneficial owner data update requested",
                ActivityLogActivityTypes.BeneficialOwnerAssistanceRequested => "Beneficial owner assistance requested",
                ActivityLogActivityTypes.DirectorDataConfirmed => "Director data confirmed",
                ActivityLogActivityTypes.DirectorDataUpdateRequested => "Director data update requested",
                ActivityLogActivityTypes.DirectorAssistanceRequested => "Director assistance requested",
                ActivityLogActivityTypes.ShareholderDataConfirmed => "Shareholder data confirmed",
                ActivityLogActivityTypes.ShareholderDataUpdateRequested => "Shareholder data update requested",
                ActivityLogActivityTypes.ShareholderAssistanceRequested => "Shareholder assistance requested",
                ActivityLogActivityTypes.CompanyActivated => "Company activated",
                ActivityLogActivityTypes.CompanyDeactivated => "Company deactivated",
                ActivityLogActivityTypes.CompanyMasterClientChanged => "Company master client changed",
                ActivityLogActivityTypes.CompanyModuleEnabledChanged => "Company module changed",
                ActivityLogActivityTypes.CompanyModuleApprovalChanged => "Company module approved",
                ActivityLogActivityTypes.CompanyNameChanged => "Company name changed",
                ActivityLogActivityTypes.CompanyCodeChanged => "Company code changed",
                ActivityLogActivityTypes.UserInvitationSentManually => "User invitation sent manually",
                ActivityLogActivityTypes.UserInvitationSent => "User invitation sent",
                ActivityLogActivityTypes.InvoiceDeleted => "Invoice deleted",
                ActivityLogActivityTypes.PaymentDeleted => "Payment deleted",
                ActivityLogActivityTypes.SettingsSaved => "Settings saved",
                ActivityLogActivityTypes.SubmissionStarted => "Submission started",
                ActivityLogActivityTypes.SubmissionSubmitted => "Submission submitted",
                ActivityLogActivityTypes.SubmissionSubmitScheduled => "Submission scheduled",
                ActivityLogActivityTypes.SubmissionResubmitted => "Submission resubmitted",
                ActivityLogActivityTypes.SubmissionReopened => "Submission reopened",
                ActivityLogActivityTypes.SubmissionMigrated => "Submission migrated",
                ActivityLogActivityTypes.SubmissionPaidStatusUpdated => "Submission paid status updated",
                ActivityLogActivityTypes.SubmissionFormContentUpdated => "Submission form contents updated",
                ActivityLogActivityTypes.SubmissionDeleted => "Submission deleted",
                ActivityLogActivityTypes.SubmissionInformationRequested => "Information requested for submission",
                ActivityLogActivityTypes.SubmissionInformationRequestCompleted => "Information request for submission completed",
                ActivityLogActivityTypes.SubmissionInformationRequestCancelled => "Information request for submission cancelled",
                ActivityLogActivityTypes.SubmissionFinancialPeriodChanged => "Financial period changed for a submission",
                ActivityLogActivityTypes.InvoiceMigrated => "Invoice migrated",
                ActivityLogActivityTypes.PaymentMigrated => "Payment migrated",
                ActivityLogActivityTypes.LegalEntityMigrated => "Legal entity migrated",
                ActivityLogActivityTypes.FormDocumentMigrated => "Form document migrated",
                ActivityLogActivityTypes.CompanyApproved => "Company approved",
                ActivityLogActivityTypes.CompanyDeclined => "Company declined",
                ActivityLogActivityTypes.CompanyOnboardingChanged => "Company onboarding updated",
                ActivityLogActivityTypes.CompanyAnnualFeeStatusUpdated => "Company Annual fee status updated",
                ActivityLogActivityTypes.MessageSentToEmployeeForCompany => "Message sent for company",
                ActivityLogActivityTypes.LatePaymentFeeExemptUpdated => "Late payment fee exempt updated",
                ActivityLogActivityTypes.SubmissionFeeUpdated => "Submission fee updated",
                ActivityLogActivityTypes.ModuleForCompanyDeactivated => "Module for company deactivated",
                ActivityLogActivityTypes.PanamaFeeSettingsSaved => "Panama fee settings saved",
                ActivityLogActivityTypes.RfiCompletedNotificationCreated => "Notification of completion of RFI",
                ActivityLogActivityTypes.RfiCreatedNotificationCreated => "Notification of RFI creation",
                ActivityLogActivityTypes.RfiDueInOneWeekNotificationCreated => "Notification of RFI due in one week",
                ActivityLogActivityTypes.RfiDueInOneDayNotificationCreated => "Notification of RFI due in one day",
                ActivityLogActivityTypes.Rfi3DaysOverDueNotificationCreated => "Notification of RFI three days overdue",

                _ => $"Unsupported activitytype ({activityType})",
            };

            return result;
        }
    }
}