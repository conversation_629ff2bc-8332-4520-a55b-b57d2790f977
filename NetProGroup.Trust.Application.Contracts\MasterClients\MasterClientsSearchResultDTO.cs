﻿// <copyright file="MasterClientsSearchResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Model holding the results of the search for master-client/jurisdiction.
    /// </summary>
    public class MasterClientsSearchResultDTO
    {
        /// <summary>
        /// Gets or sets the id of the MasterClient.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string MasterClientCode { get; set; }

        ///// <summary>
        ///// Gets or sets the name of the Jurisdiction.
        ///// </summary>
        // public string Jurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the list of legal entities.
        /// </summary>
        public List<CompanyDTO> Companies { get; set; }

        ///// <summary>
        ///// Gets or sets the id of the Jurisdiction.
        ///// </summary>
        // public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the master client has any active requests for information.
        /// </summary>
        public bool HasActiveRequestsForInformation { get; set; }
    }
}
