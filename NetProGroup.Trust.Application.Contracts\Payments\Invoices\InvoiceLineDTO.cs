// <copyright file="InvoiceLineDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Payments.Invoices
{
    /// <summary>
    /// Represents an invoice line item in a billing system.
    /// </summary>
    public class InvoiceLineDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the foreign key that links to the invoice.
        /// </summary>
        public Guid InvoiceId { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the currency used in the invoice line.
        /// </summary>
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the description of the invoice line.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the sequence number of the invoice line.
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// Gets or sets the amount for the invoice line.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the ArticleNr of the invoice line.
        /// </summary>
        public string ArticleNr { get; set; }
    }
}