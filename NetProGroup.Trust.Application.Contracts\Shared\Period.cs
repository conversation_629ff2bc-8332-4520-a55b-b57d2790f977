﻿// <copyright file="Period.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Represents a period between 2 dates.
    /// </summary>
    public class Period
    {
        /// <summary>
        /// Gets or sets the start of the period.
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Gets or sets the end of the period (including this date).
        /// </summary>
        public DateTime EndDate { get; set; }
    }
}
