﻿// <copyright file="DirectorComparisonDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors
{
    /// <summary>
    /// Represents a set of 2 Directors to compare before confirmed or an update requested.
    /// </summary>
    public class DirectorComparisonDTO
    {
        /// <summary>
        /// Gets or sets the current (latest version) of the Director.
        /// </summary>
        public DirectorDTO CurrentVersion { get; set; }

        /// <summary>
        /// Gets or sets the prior version of the Director.
        /// </summary>
        public DirectorDTO PriorVersion { get; set; }
    }
}