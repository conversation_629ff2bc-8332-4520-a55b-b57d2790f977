﻿// <copyright file="ListBeneficialOwnersDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a list of ListBeneficialOwnersDTO.
    /// </summary>
    public class ListBeneficialOwnersDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<BeneficialOwnerDTO> BeneficialOwners { get; set; } = new List<BeneficialOwnerDTO>();
    }
}
