// <copyright file="DocumentsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Documents;
using NetProGroup.Trust.Application.Contracts.Errors;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Common.Controllers
{
    /// <summary>
    /// Use this controller for Document related events.
    /// </summary>
    [Area("Common")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class DocumentsController : TrustAPIControllerBase
    {
        private readonly IDocumentsAppService _documentsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DocumentsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="documentsAppService">An instance of IDocumentsAppService.</param>
        public DocumentsController(
            ILogger<DocumentsController> logger,
            IDocumentsAppService documentsAppService)
            : base(logger)
        {
            _documentsAppService = documentsAppService;
        }

        /// <summary>
        /// Upload a document.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/common/documents
        ///     {
        ///         data
        ///     }.
        /// </remarks>
        /// <param name="data">The data necessary to create a document.</param>
        /// <response code="200">OK.</response>
        /// <response code="401">Unauthorized.</response>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "CreateDocument", Summary = "Upload a document.")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateDocument(
            [SwaggerParameter(Description = "The data necessary to create or update a document for an employee.")]
            [FromForm]
            CreateDocumentDTO data)
        {
            Guid response = Guid.Empty;
            var result = await ProcessRequestAsync<object>(

                validate: () =>
                {
                    // The data validations performed to documents in general are:
                    Check.NotNull(data, nameof(data));
                    Check.NotNull(data.File, nameof(data.File));
                },

                executeAsync: async () =>
                {
                    response = await _documentsAppService.CreateDocumentAsync(data);
                },

                createResponseModel: () =>
                {
                    return response;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Retrieve a document given its id.
        /// Sample request:
        ///     GET /api/v1/common/documents/{documentId}.
        /// </summary>
        /// <param name="documentId">The document id as Guid.</param>
        /// <param name="includeData">Determine if the data for the document needs to be included.</param>
        /// <returns>The document entity as DocumentDTO.</returns>
        [HttpGet("{documentId}")]
        [SwaggerOperation(Summary = "Retrieve a document given its id.")]
        [ProducesResponseType(typeof(DocumentDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetDocumentById(
            Guid documentId,
            bool includeData = true)
        {
            DocumentDTO dto = null;

            var result = await ProcessRequestAsync<DocumentDTO>(

                validate: () =>
                {
                    // The data validations performed to documents in general are:
                    Check.NotDefaultOrNull<Guid>(documentId, nameof(documentId));
                },

                executeAsync: async () =>
                {
                    dto = await _documentsAppService.GetDocumentByIdAsync(documentId, includeData);
                },

                createResponseModel: () =>
                {
                    return dto;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Retreive a list of documents given a list of Guid containing their id.
        /// Sample request:
        ///     GET /api/v1/common/documents&amp;documentIds={documentId}.
        /// </summary>
        /// <param name="documentIds">A list of Guid containing the documents id.</param>
        /// <param name="includeData">Determine if the data for the document needs to be included.</param>
        /// <returns>The document entity as DocumentDTO.</returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Retreive a list of documents given a list of Guid containing their id.")]
        [ProducesResponseType(typeof(List<DocumentDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetDocumentsById(
            [FromQuery]
            Guid[] documentIds,
            [FromQuery]
            bool includeData = true)
        {
            List<DocumentDTO> dto = null;

            var result = await ProcessRequestAsync<List<DocumentDTO>>(

                validate: () =>
                {
                    // Check and validations
                    if (documentIds == null || documentIds.ToList().Count == 0)
                    {
                        throw new BadRequestException(
                            ApplicationErrors.DOCUMENT_ID_EMPTY_ARRAY.ToErrorCode(),
                            $"The documentIds property does not contain any values");
                    }

                    foreach (var documentId in documentIds)
                    {
                        Check.NotDefaultOrNull<Guid>(documentId, nameof(documentId));
                    }
                },

                executeAsync: async () =>
                {
                    dto = await _documentsAppService.GetDocumentsByIdAsync(documentIds.ToList(), includeData);
                },

                createResponseModel: () =>
                {
                    return dto;
                });

            return result.AsResponse();
        }
    }
}
