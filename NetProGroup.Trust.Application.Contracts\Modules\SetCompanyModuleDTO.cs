﻿// <copyright file="SetCompanyModuleDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a module.
    /// </summary>
    public class SetCompanyModuleDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets a value indicating whether the module is enabled for the entity.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is enabled for the entity.
        /// </summary>
        public bool IsApproved { get; set; }
    }
}
