﻿// <copyright file="SubmissionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.API.Swagger.ResponseTypes;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Submissions
{
    /// <summary>
    /// Use this controller for submission related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/submissions")]
    public class SubmissionsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly ISubmissionsAppService _submissionsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="submissionsAppService">The service for submissions.</param>
        public SubmissionsController(
            ILogger<SubmissionsController> logger,
            IConfiguration configuration,
            ISubmissionsAppService submissionsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _submissionsAppService = submissionsAppService;
        }

        /// <summary>
        /// Gets the given submission with optionally the FormDocument data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/submissions/{submissionId}?includeFormDocument=true.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <param name="includeFormDocument">Denotes wether to include the form document in the result.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpGet("{submissionId}")]
        [SwaggerOperation(OperationId = "Client_GetSubmission")]
        [ProducesResponseType(typeof(SubmissionDTOResponseTypes), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubmission(Guid submissionId, bool includeFormDocument)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync<SubmissionDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _submissionsAppService.GetSubmissionAsync(submissionId, includeFormDocument);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Updates the dataset in the given submission.
        /// </summary>
        /// <remarks>
        /// This will update the latest revision.
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/client/submissions/{submissionId}/dataset
        ///             {
        ///
        ///             }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to update.</param>
        /// <param name="submissionDataSet">The DTO with the dataset.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpPut("{submissionId}/dataset")]
        [SwaggerOperation(OperationId = "Client_PutSubmissionDataSet")]
        [ProducesResponseType(typeof(SubmissionDataSetDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> PutSubmission(
            Guid submissionId,
            SubmissionDataSetDTO submissionDataSet)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync<SubmissionDTO>(
                validate: () =>
                {
                    if (submissionDataSet.Id != Guid.Empty && submissionDataSet.Id != submissionId)
                    {
                        throw new BadHttpRequestException("SubmissionIds are not equal");
                    }

                    if (submissionDataSet.Id == Guid.Empty)
                    {
                        submissionDataSet.Id = submissionId;
                    }
                },

                executeAsync: async () =>
                {
                    item = await _submissionsAppService.UpdateSubmissionDataSetAsync(submissionDataSet);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Finalizes/submits the submission.
        /// </summary>
        /// <remarks>
        /// This will finalize the submisison, the document and the current revision.
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/client/submissions/{submissionId}/finalize-request
        ///             {
        ///
        ///             }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to finalize (submit).</param>
        /// <param name="submission">The model with information about the finalizing of the submission.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpPost("{submissionId}/finalize-request")]
        [SwaggerOperation(OperationId = "Client_SubmitSubmission")]
        [ProducesResponseType(typeof(SubmissionDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitSubmission(
            Guid submissionId,
            SubmitSubmissionDTO submission)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync<SubmissionDTO>(
                validate: () =>
                {
                    submission.SubmissionId = submissionId;
                },

                executeAsync: async () =>
                {
                    item = await _submissionsAppService.SubmitSubmissionAsync(submission);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Deletes the submission.
        /// </summary>
        /// <remarks>
        /// This will mark the submission as deleted.
        ///
        /// Sample request:
        ///
        ///     DELETE /api/v1/client/submissions/{submissionId}
        ///         {
        ///
        ///         }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to delete as Guid.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpDelete("{submissionId}")]
        [SwaggerOperation(OperationId = "Client_DeleteSubmission")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> DeleteSubmission(
            Guid submissionId)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync<SubmissionDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));
                },
                executeAsync: async () =>
                {
                    await _submissionsAppService.DeleteSubmissionAsync(submissionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Updates the submission general information.
        /// </summary>
        /// <remarks>
        /// This update basic information fo a submission.
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/client/submissions/{submissionId}/information
        ///         {
        ///             data
        ///         }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to delete as Guid.</param>
        /// <param name="data">The necessary data to update the submission general information.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{submissionId}/information")]
        [SwaggerOperation(OperationId = "Client_UpdateSubmissionInformation")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> UpdateSubmissionInformation(
            Guid submissionId,
            UpdateSubmissionInformationDTO data)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));
                    Check.NotNull(data, nameof(data));
                },
                executeAsync: async () =>
                {
                    await _submissionsAppService.UpdateSubmissionGeneralInformationAsync(submissionId, data);
                });

            return result.AsResponse();
        }
    }
}
