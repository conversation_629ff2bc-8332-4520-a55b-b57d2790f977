// <copyright file="CompleteRequestForInformationDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represent the necessary data used to complete a request for information.
    /// </summary>
    public class CompleteRequestForInformationDTO
    {
        /// <summary>
        /// Gets or sets the response made by the user.
        /// </summary>
        public string Response { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the rfi will include documents.
        /// </summary>
        /// <remarks>
        /// If the value is true the rfi status will be set to Draft.
        /// </remarks>
        public bool IncludeAttachments { get; set; }
    }
}