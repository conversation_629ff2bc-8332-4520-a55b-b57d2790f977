﻿// <copyright file="SearchSubmissionsRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// DTO for the request to search for submissions.
    /// </summary>
    public class SearchSubmissionsRequestDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the id of the module to search the submissions for.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities.
        /// </summary>
        public string LegalEntitySearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within masterclients.
        /// </summary>
        public string MasterClientSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within referral offices.
        /// </summary>
        public string ReferralOfficeSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities, masterclients or referral offices.
        /// </summary>
        /// <remarks>
        /// Use this searchterm to search with a single term for 'or-ring' the search.
        /// </remarks>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the country to search for.
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the financial year to search the submisisons for.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission has been exported or not.
        /// </summary>
        public bool? IsExported { get; set; }

        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        [SortableColumns(
            nameof(ListSubmissionDTO.CreatedAt),
            nameof(ListSubmissionDTO.CreatedByEmail),
            nameof(ListSubmissionDTO.ExportedAt),
            nameof(ListSubmissionDTO.FinancialYear),
            nameof(ListSubmissionDTO.LegalEntityCode),
            nameof(ListSubmissionDTO.LegalEntityName),
            nameof(ListSubmissionDTO.LegalEntityVPCode),
            nameof(ListSubmissionDTO.LegalEntityVPStatus),
            nameof(ListSubmissionDTO.IncorporationNr),
            nameof(ListSubmissionDTO.MasterClientCode),
            nameof(ListSubmissionDTO.IsPaid),
            nameof(ListSubmissionDTO.Status),
            nameof(ListSubmissionDTO.SubmittedAt),
            nameof(ListSubmissionDTO.PaymentMethod),
            nameof(ListSubmissionDTO.PaymentReceivedAt),
            nameof(ListSubmissionDTO.PaymentReference))] // TODO we could also use an enum for this but for now wanted to keep the type of the property the same as the base class
        public override string SortBy { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be deleted or not.
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
