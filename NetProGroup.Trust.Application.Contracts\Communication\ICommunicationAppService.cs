// <copyright file="ICommunicationAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Messaging.Tokens;
using System.Collections.ObjectModel;

namespace NetProGroup.Trust.Application.Contracts.Communication
{
    /// <summary>
    /// Interface for the communications.
    /// </summary>
    public interface ICommunicationAppService : IScopedService
    {
        /// <summary>
        /// Sends an inbox message and an email.
        /// </summary>
        /// <param name="fromUserId">The user ID of the sender.</param>
        /// <param name="fromRolId">The role ID of the sender.</param>
        /// <param name="toUserId">The user ID of the recipient.</param>
        /// <param name="toRoleId">The role ID of the recipient.</param>
        /// <param name="recipientEmail">The email address of the recipient for sending an email.</param>
        /// <param name="inboxMessageTemplateId">The ID of the inbox message template to use (optional).</param>
        /// <param name="inboxMessageTemplateName">The name of the inbox message template to use (optional).</param>
        /// <param name="tokensInbox">Tokens to be used in the inbox message template (optional).</param>
        /// <param name="emailMessageTemplateId">The ID of the email message template to use (optional).</param>
        /// <param name="emailMessageTemplateName">The name of the email message template to use (optional).</param>
        /// <param name="tokensEmail">Tokens to be used in the email message template (optional).</param>
        /// <param name="files">List of files to be attached to the inbox message (optional).</param>
        /// <param name="saveChanges">Indicates whther changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendInboxAndEmail(Guid fromUserId, Guid? fromRolId, Guid toUserId,
                               Guid? toRoleId, string recipientEmail,
                               Guid? inboxMessageTemplateId = null,
                               string inboxMessageTemplateName = null,
                               TokenList tokensInbox = null,
                               Guid? emailMessageTemplateId = null,
                               string emailMessageTemplateName = null,
                               TokenList tokensEmail = null,
                               Collection<IFormFile> files = null,
                               bool saveChanges = false);

        /// <summary>
        /// Sends an email message with the MFA verification code to the user.
        /// </summary>
        /// <param name="userId">Id of th euser to send the email to.</param>
        /// <param name="verificationCode">The verification code to include in the message.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendMFAVerificationCodeAsync(Guid userId, string verificationCode, TokenList tokens = null);

        /// <summary>
        /// Sends an email message with a request for update.
        /// </summary>
        /// <param name="productionOffice">Used for dicovery of recipient.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendRequestForUpdateAsync(string productionOffice, TokenList tokens = null);

        /// <summary>
        /// Sends an email message with a request for assistance.
        /// </summary>
        /// <param name="productionOffice">Used for dicovery of recipient.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendRequestForAssistanceAsync(string productionOffice, TokenList tokens = null);

        /// <summary>
        /// Sends an email message with an invitation to register.
        /// </summary>
        /// <param name="userId">Id of the user to send the invitation to.</param>
        /// <param name="masterClientCode">The masterclient code to note in the message.</param>
        /// <param name="reregistration">Denotes whether this is for a reregistration.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendUserInvitationAsync(Guid userId, string masterClientCode, bool reregistration, TokenList tokens = null);

        /// <summary>
        /// Sends an email with the free subject and text to the recipient.
        /// </summary>
        /// <param name="model">The DTO with the parameters for the new email.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendEmailAsync(SendEmailDTO model);

        /// <summary>
        /// Sends an email notification for an announcement.
        /// </summary>
        /// <param name="recipientEmail">The email address of the recipient.</param>
        /// <param name="emailSubject">The subject of the email.</param>
        /// <param name="templateName">The name of the email template to use.</param>
        /// <param name="tokens">Optional tokens to use in the template.</param>
        /// <param name="saveChanges">Whether to save changes immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendAnnouncementAsync(string recipientEmail, string emailSubject, string templateName, TokenList tokens = null, bool saveChanges = false);
    }
}