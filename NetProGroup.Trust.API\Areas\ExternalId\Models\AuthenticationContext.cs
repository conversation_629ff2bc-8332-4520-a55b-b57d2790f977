﻿// <copyright file="AuthenticationContext.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
    public class AuthenticationContext
    {
        public string correlationId { get; set; }

        public AuthenticationContext_Client client { get; set; }

        public string protocol { get; set; }

        public AuthenticationContext_ServicePrincipal clientServicePrincipal { get; set; }

        public AuthenticationContext_ServicePrincipal resourceServicePrincipal { get; set; }

        public AuthenticationContext_User user { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AuthenticationContext_Client
#pragma warning restore SA1402 // File may only contain a single type
    {
        public string ip { get; set; }

        public string locale { get; set; }

        public string market { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AuthenticationContext_ServicePrincipal
#pragma warning restore SA1402 // File may only contain a single type
    {
        public string id { get; set; }

        public string appId { get; set; }

        public string appDisplayName { get; set; }

        public string displayName { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AuthenticationContext_User
#pragma warning restore SA1402 // File may only contain a single type
    {
        // Display name
        [StringLength(120, ErrorMessage = "DisplayName length can't be more than 120.")]
        public string displayName { get; set; }

        // User object ID
        [StringLength(120, ErrorMessage = "ID length can't be more than 120.")]
        public string id { get; set; }

        // UPN
        [StringLength(120, ErrorMessage = "Surname length can't be more than 120.")]
        public string userPrincipalName { get; set; }

        // User type
        [StringLength(120, ErrorMessage = "UserType length can't be more than 120.")]
        public string userType { get; set; }

        // Mail address
        [StringLength(120, ErrorMessage = "Mail length can't be more than 120.")]
        public string mail { get; set; }
    }
#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
