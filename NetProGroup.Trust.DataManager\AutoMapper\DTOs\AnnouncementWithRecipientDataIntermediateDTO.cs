// <copyright file="AnnouncementWithRecipientDataIntermediateDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Announcements;

namespace NetProGroup.Trust.DataManager.AutoMapper.DTOs
{
    /// <summary>
    /// Intermediate class to support Automapper Projection.
    /// This is needed because there is no EF navigation property to the recipients, as they can be of different types.
    /// So we first join the Announcement with the MasterClientCodes and JurisdictionNames and then map to the DTO.
    /// </summary>
    internal sealed class AnnouncementWithRecipientDataIntermediateDTO
    {
        /// <summary>
        /// Gets or sets the Announcement entity.
        /// </summary>
        public Announcement Announcement { get; set; }

        /// <summary>
        /// Gets or sets the master client codes.
        /// </summary>
        public IEnumerable<string> MasterClientCodes { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction names.
        /// </summary>
        public IEnumerable<string> JurisdictionNames { get; set; }
    }
}