﻿// <copyright file="UserMasterClientsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// A DTO for updating the masterclients for a user.
    /// </summary>
    public class UserMasterClientsDTO
    {
        /// <summary>
        /// Gets or sets the id of the use to set the masterclients for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the list of ids of the master clients.
        /// </summary>
        public IList<Guid> MasterClientIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets a value indicating whether to remove the user from the masterclients that are not mentioned.
        /// </summary>
        public bool RemoveUnmentionedMasterClients { get; set; }
    }
}
