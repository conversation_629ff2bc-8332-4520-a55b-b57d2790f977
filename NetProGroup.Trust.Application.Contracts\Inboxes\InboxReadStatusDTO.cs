﻿// <copyright file="InboxReadStatusDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Data Transfer Object for InboxReadStatus.
    /// </summary>
    public class InboxReadStatusDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the inbox ID.
        /// </summary>
        public Guid InboxId { get; set; }

        /// <summary>
        /// Gets or sets the owner ID.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the inbox was read.
        /// </summary>
        public DateTime ReadAt { get; set; }
    }
}
