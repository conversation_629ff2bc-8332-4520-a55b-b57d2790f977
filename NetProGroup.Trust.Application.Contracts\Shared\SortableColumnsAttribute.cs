﻿// <copyright file="SortableColumnsAttribute.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Attribute to specify which columns are sortable for a property.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public sealed class SortableColumnsAttribute : Attribute
    {
        private readonly string[] _sortableColumns;

        /// <summary>
        /// Initializes a new instance of the <see cref="SortableColumnsAttribute"/> class.
        /// </summary>
        /// <param name="sortableColumns">The array of column names that can be used for sorting.</param>
        public SortableColumnsAttribute(params string[] sortableColumns)
        {
            _sortableColumns = sortableColumns ?? Array.Empty<string>();
        }

        /// <summary>
        /// Gets the array of sortable column names.
        /// </summary>
        public string[] SortableColumns => _sortableColumns;
    }
}