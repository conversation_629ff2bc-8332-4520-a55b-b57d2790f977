﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1014;CA1716</NoWarn>
		<!-- CA1014 == CLS Compliancy, not required -->
		<!-- CA1716 == Don't use Shared keyword in namespace. -->
	</PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\**" />
    <Compile Remove="Reports\Financial\**" />
    <EmbeddedResource Remove="Models\**" />
    <EmbeddedResource Remove="Reports\Financial\**" />
    <None Remove="Models\**" />
    <None Remove="Reports\Financial\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.24" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.FormBuilder\NetProGroup.Trust.Forms.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Shared\NetProGroup.Trust.Domain.Shared.csproj" />
  </ItemGroup>

</Project>
