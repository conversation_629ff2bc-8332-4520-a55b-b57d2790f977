// <copyright file="RequestForInformationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.BackgroundServices
{
    /// <summary>
    /// Background service for requests for information in the application.
    /// </summary>
    public class RequestForInformationService : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        private IServiceProvider _scopedServiceProvider;
        private IServiceScope _servicescope;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestForInformationService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The serviceprovider to get new scoped services on execution.</param>
        /// <param name="logger">The logger instance.</param>
        public RequestForInformationService(
            IServiceProvider serviceProvider,
            ILogger<RequestForInformationService> logger)
        {
            // Use the container
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <inheritdoc/>
#pragma warning disable CA1816 // Dispose methods should call SuppressFinalize
        public override void Dispose()
#pragma warning restore CA1816 // Dispose methods should call SuppressFinalize
        {
            if (_servicescope != null)
            {
                _servicescope.Dispose();
                _servicescope = null;
            }

            base.Dispose();
        }

        /// <inheritdoc/>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Delay before actually starting the service
            await Task.Delay(5000, stoppingToken);

            using var tickTimer = new PeriodicTimer(TimeSpan.FromSeconds(10));

            while (await tickTimer.WaitForNextTickAsync(stoppingToken))
            {
                _servicescope = _serviceProvider.CreateScope();
                _scopedServiceProvider = _servicescope.ServiceProvider;

                // Small random delay to get out of a possible race condition
#pragma warning disable CA5394 // Do not use insecure randomness
                await Task.Delay(new Random().Next(100, 1000), stoppingToken);
#pragma warning restore CA5394 // Do not use insecure randomness

                var repository = _scopedServiceProvider.GetRequiredService<IRequestForInformationRepository>();
                var dataManager = _scopedServiceProvider.GetRequiredService<IRequestForInformationManager>();

#pragma warning disable CA1031 // Do not catch general exception types
                try
                {
                    // Process RFIs using the manager methods
                    await dataManager.ProcessRFIsDueInOneWeekAsync();
                    await dataManager.ProcessRFIsDueInOneDayAsync();
                    await dataManager.ProcessRFIsThreeDaysOverdueAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process RFI notifications.");
                }
#pragma warning restore CA1031 // Do not catch general exception types

                // Set the timer period to check every minute
                tickTimer.Period = TimeSpan.FromMinutes(1);
            }
        }
    }
}
