﻿// <copyright file="ManagementBeneficialOwnersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Permissions;

namespace NetProGroup.Trust.Application.AppServices.LegalEntityRelations.BeneficialOwners
{
    /// <summary>
    /// Application service for BeneficialOwners.
    /// </summary>
    public class ManagementBeneficialOwnersAppService : IManagementBeneficialOwnersAppService
    {
        private readonly IBeneficialOwnersDataManager _dataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ManagementBeneficialOwnersAppService"/> class.
        /// </summary>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="securityManager">The security manager for getting roles etc.</param>
        public ManagementBeneficialOwnersAppService(IBeneficialOwnersDataManager dataManager,
                                          ISecurityManager securityManager)
        {
            _dataManager = dataManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(Guid beneficialOwnerId)
        {
            Check.NotDefaultOrNull<Guid>(beneficialOwnerId, nameof(beneficialOwnerId));

            var beneficialOwner = await _dataManager.CheckBeneficialOwnerByIdAsync(beneficialOwnerId);
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.BODIRModule_View, beneficialOwner.LegalEntityId);

            return await _dataManager.GetBeneficialOwnerAsync(beneficialOwnerId);
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(Guid beneficialOwnerId)
        {
            Check.NotDefaultOrNull<Guid>(beneficialOwnerId, nameof(beneficialOwnerId));

            var beneficialOwner = await _dataManager.CheckBeneficialOwnerByIdAsync(beneficialOwnerId);
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.BODIRModule_View, beneficialOwner.LegalEntityId);

            return await _dataManager.GetBeneficialOwnerForComparisonAsync(beneficialOwnerId);
        }
    }
}
