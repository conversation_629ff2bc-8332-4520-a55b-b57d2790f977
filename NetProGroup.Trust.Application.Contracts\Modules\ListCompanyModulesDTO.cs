﻿// <copyright file="ListCompanyModulesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a list of modules for a company.
    /// </summary>
    public class ListCompanyModulesDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<CompanyModuleDTO> Modules { get; set; } = new List<CompanyModuleDTO>();
    }
}
