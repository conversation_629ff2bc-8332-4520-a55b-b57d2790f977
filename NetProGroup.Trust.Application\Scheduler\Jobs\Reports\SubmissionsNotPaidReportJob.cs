// <copyright file="SubmissionsNotPaidReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.AppServices.Reports.Nevis.SubmissionsNotPaidReport;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <summary>
    /// Report job implementation for generating reports of submissions not paid.
    /// </summary>
    /// <param name="logger">The logger.</param>
    /// <param name="serviceProvider">The service provider.</param>
    public class SubmissionsNotPaidReportJob(ILogger<SubmissionsNotPaidReportJob> logger, IServiceProvider serviceProvider)
        : JobBase<object>(logger, serviceProvider), ISubmissionsNotPaidReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{E3585FE3-3EE3-4711-B369-CA4A3A4A1FAB}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.nevis.submissions-not-paid";

        /// <inheritdoc/>
        public string ScheduledJobName => "Submissions Not Paid Report Job - Nevis";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await base.AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var paymentReportService = ServiceProvider.GetRequiredService<ISubmissionsNotPaidReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await paymentReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}