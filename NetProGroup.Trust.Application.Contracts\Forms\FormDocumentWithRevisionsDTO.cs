﻿// <copyright file="FormDocumentWithRevisionsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a form document with revisions.
    /// </summary>
    public class FormDocumentWithRevisionsDTO : FormDocumentDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentWithRevisionsDTO"/> class.
        /// </summary>
        public FormDocumentWithRevisionsDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the collection of revisions for the document.
        /// </summary>
        public IReadOnlyCollection<FormDocumentRevisionDTO> Revisions { get; set; }
    }
}
