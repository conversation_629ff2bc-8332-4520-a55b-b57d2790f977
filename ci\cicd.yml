trigger: none # Is triggered by the version bump pipeline

variables:
- template: variables/global.yml
- name: isDevelopmentBranch
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/development')]
- name: isAlphaRelease
  value: $[and(
          startsWith(variables['Build.SourceBranch'], 'refs/tags/'),
          contains(variables['Build.SourceBranch'], 'alpha')
          )]
- name: isStableRelease
  value: $[and(
          startsWith(variables['Build.SourceBranch'], 'refs/tags/'),
          not(contains(variables['Build.SourceBranch'], 'alpha')),
          not(contains(variables['Build.SourceBranch'], 'beta'))
          )]

stages:
- stage: Build
  jobs:
  - template: build.yml
  - template: retention-template.yml
    parameters:
      condition: eq(variables['isStableRelease'], true)

# Development deployment
- template: deploy.yml
  parameters:
    stageName: DeployToDev
    dependsOn: Build
    condition: and(succeeded(), or(eq(variables['isAlphaRelease'], true), eq(variables['isDevelopmentBranch'], true)))
    targetEnvironment: 'dev'
    serviceConnection: 'DEV Infra Service Connection'

# Test deployment
- template: deploy.yml
  parameters:
    stageName: DeployToTest
    dependsOn: Build
    condition: and(succeeded(), eq(variables['isStableRelease'], true))
    targetEnvironment: 'tst'
    serviceConnection: 'DEV Infra Service Connection'

# Acceptance2 deployment
- template: deploy.yml
  parameters:
    stageName: DeployToAcc2
    dependsOn: DeployToTest
    condition: and(succeeded(), eq(variables['isStableRelease'], true))
    targetEnvironment: 'acc2'
    serviceConnection: 'PRD2 Application Service Connection'
    retain: true

# Production deployment
- template: deploy.yml
  parameters:
    stageName: DeployToPrd2
    dependsOn: DeployToAcc2
    condition: and(succeeded(), eq(variables['isStableRelease'], true))
    targetEnvironment: 'prd2'
    serviceConnection: 'PRD2 Application Service Connection'
    retain: true

# Acceptance deployment
- template: deploy.yml
  parameters:
    stageName: DeployToAcc
    dependsOn: DeployToTest
    condition: false # currently deploying to UAT only from the releases/M1 branch, which has a different version of this pipeline
    targetEnvironment: 'acc'
    serviceConnection: 'PRD Application Service Connection'
    retain: true

# Production deployment
- template: deploy.yml
  parameters:
    stageName: DeployToPrd
    dependsOn: DeployToAcc
    condition: false # currently deploying to UAT only from the releases/M1 branch, which has a different version of this pipeline
    targetEnvironment: 'prd'
    serviceConnection: 'PRD Application Service Connection'
    retain: true
