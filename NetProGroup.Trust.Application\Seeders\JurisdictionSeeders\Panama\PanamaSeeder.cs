﻿// <copyright file="PanamaSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.Common;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Forms;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Panama
{
    /// <summary>
    /// Seeder for Panama data.
    /// </summary>
    public class PanamaSeeder : SeederBase, IPanamaSeeder
    {
        private readonly ILogger _logger;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISettingsManager _settingsManager;
        private readonly AppSettings _appSettings;

        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="PanamaSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="settingsManager">Instance of the settings manager.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="options">Instance of the app settings.</param>
        public PanamaSeeder(ILogger<PanamaSeeder> logger,
                           IServiceProvider serviceProvider,
                           ISettingsManager settingsManager,
                           IJurisdictionsRepository jurisdictionsRepository,
                           IOptions<AppSettings> options)
            : base(logger, serviceProvider)
        {
            ArgumentNullException.ThrowIfNull(options);

            _logger = logger;
            _settingsManager = settingsManager;
            _jurisdictionsRepository = jurisdictionsRepository;
            _appSettings = options.Value;
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindSingleOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Panama);

            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.BasicFinancialReportPanama, JurisdictionCodes.Panama);
            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.BODirectors, JurisdictionCodes.Panama);

            await CreateBasicFinancialReportFormTemplateAsync();

            if (_appSettings.SeedTestDataOnStartup)
            {
                var testDataSeeder = ServiceProvider.GetRequiredService<IPanamaTestDataSeeder>();
                await testDataSeeder.RunAsync();
            }
        }

        /// <summary>
        /// Setup the form template for the Panama jurisdiction.
        /// </summary>
        private async Task CreateBasicFinancialReportFormTemplateAsync()
        {
            // Setup variables
            var moduleKey = ModuleKeyConsts.BasicFinancialReportPanama;
            var jurisdictionCode = JurisdictionCodes.Panama;

            // Retrieve the services needed
            var moduleRepository = ServiceProvider.GetRequiredService<IModulesRepository>();
            var module = await moduleRepository.FindFirstOrDefaultByConditionAsync(x => x.Key == moduleKey);

            var jurisdictionRepository = ServiceProvider.GetRequiredService<IJurisdictionsRepository>();
            var jurisdiction = await jurisdictionRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == jurisdictionCode, q => q.Include(x => x.JurisdictionModules));

            var formTemplatesRepository = ServiceProvider.GetRequiredService<IFormTemplatesRepository>();

            // Check if the template already exists.
            var formTemplate = await formTemplatesRepository.FindFirstOrDefaultByConditionAsync(ft => ft.JurisdictionId == jurisdiction.Id && ft.ModuleId == module.Id, q => q.Include(ft => ft.FormTemplateVersions));
            if (formTemplate == null)
            {
                formTemplate = new FormTemplate(Guid.NewGuid())
                {
                    JurisdictionId = jurisdiction.Id,
                    ModuleId = module.Id,
                    Key = moduleKey,
                    Name = module.Name + " template",
                };
                await formTemplatesRepository.InsertAsync(formTemplate, true);
            }

            var year = 2024;

            var formTemplateVersion = formTemplate.FormTemplateVersions.FirstOrDefault(ftv => ftv.Year == year);
            string version = "1.0";

            if (formTemplateVersion == null)
            {
                formTemplateVersion = new FormTemplateVersion { Name = year.ToString(), Version = version, Year = year, StartAt = null };
                formTemplate.FormTemplateVersions.Add(formTemplateVersion);
            }
            else
            {
                formTemplateVersion.Name = $"{formTemplate.Name} {year.ToString()}";
                formTemplateVersion.Version = version;
                formTemplateVersion.Year = year;
                formTemplateVersion.StartAt = null;
                formTemplateVersion.Year = year;
            }

            // Setup the form as KeyValueForm
            var sampleKeyValueForm = new NetProGroup.Trust.Forms.Forms.KeyValueForm();
            sampleKeyValueForm.Id = $"{moduleKey.ToLower()}.{year}";
            sampleKeyValueForm.Name = $"{moduleKey}.{year}";
            sampleKeyValueForm.Version = "1";
            sampleKeyValueForm.CreatedAt = DateTime.UtcNow;
            sampleKeyValueForm.CreatedBy = "TestController";
            sampleKeyValueForm.Description = $"Sample template for module {moduleKey}, year {year} (jurisdiction {jurisdiction.Name})";

            // Create some fields for the main section
            sampleKeyValueForm.DataSet.Add("is-firstFinancial-report", "");
            sampleKeyValueForm.DataSet.Add("financial-period-from", "");
            sampleKeyValueForm.DataSet.Add("financial-period-to", "");
            sampleKeyValueForm.DataSet.Add("main-activity", "");
            sampleKeyValueForm.DataSet.Add("other-activity", "");
            sampleKeyValueForm.DataSet.Add("use-trident-tool", "");

            if (string.IsNullOrEmpty(formTemplateVersion.DataAsJson))
            {
                var bldr = new FormBuilder();
                bldr.Form = sampleKeyValueForm;
                formTemplateVersion.DataAsJson = bldr.ToJson();
            }

            await formTemplatesRepository.UpdateAsync(formTemplate, true);
        }
    }
}
