﻿// <copyright file="SetCompanyAnnualFeesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// DTO holding the paid status of the annual fees fo the conpany for setting them.
    /// </summary>
    public class SetCompanyAnnualFeesDTO
    {
        /// <summary>
        /// Gets or sets the id of the company.
        /// </summary>
        [JsonIgnore]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the list with annual fee items.
        /// </summary>
        public List<AnnualFeeDTO> AnnualFees { get; set; }
    }
}
