// <copyright file="FeatureFlagsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.FeatureFlags;
using NetProGroup.Trust.Application.Contracts.FeatureFlags.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Common.Controllers
{
    /// <summary>
    /// Use this controller for Feature Flags.
    /// </summary>
    [Area("Common")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class FeatureFlagsController : TrustAPIControllerBase
    {
        private readonly IFeatureFlagsAppService _featureFlagsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="FeatureFlagsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="featureFlagsAppService">An instance of IFeatureFlagsAppService.</param>
        public FeatureFlagsController(
            ILogger<FeatureFlagsController> logger,
            IFeatureFlagsAppService featureFlagsAppService)
            : base(logger)
        {
            _featureFlagsAppService = featureFlagsAppService;
        }

        /// <summary>
        /// Retreive a list of feature flags and if they ae enabled or not.
        /// Sample request:
        ///     GET /api/v1/common/featureflags.
        /// </summary>
        /// <returns>The list of feature flags.</returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Retreive a list of feature flags enabled or disabled for the system.", OperationId = "Common_GetFeatureFlags")]
        [ProducesResponseType(typeof(List<FeatureFlagDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetFeatureFlags()
        {
            List<FeatureFlagDTO> dto = null;

            var result = await ProcessRequestAsync<List<FeatureFlagDTO>>(

                executeAsync: async () =>
                {
                    dto = await _featureFlagsAppService.GetFeatureFlagsAsync();
                },

                createResponseModel: () =>
                {
                    return dto;
                });

            return result.AsResponse();
        }
    }
}
