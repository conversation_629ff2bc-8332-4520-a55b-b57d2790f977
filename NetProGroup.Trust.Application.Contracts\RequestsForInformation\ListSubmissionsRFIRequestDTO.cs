// <copyright file="ListSubmissionsRFIRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represent the necessary data to filter submissions with created RFI requests.
    /// </summary>
    public class ListSubmissionsRFIRequestDTO
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; } = new PagingInfo(1, 10);

        /// <summary>
        /// Gets or sets the info for sorting.
        /// </summary>
        public SortingInfo SortingInfo { get; set; } = new SortingInfo();

        /// <summary>
        /// Gets or sets the id of the legalentity to get submissions for.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module to get submissions for.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission based on the company's incorporation before a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission based on the company's incorporation after a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedBeforeDate { get; set; }
    }
}