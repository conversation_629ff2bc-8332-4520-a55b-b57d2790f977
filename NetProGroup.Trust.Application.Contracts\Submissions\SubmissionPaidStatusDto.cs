// <copyright file="SubmissionPaidStatusDto.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// DTO containing paid status for a specific company and year.
    /// </summary>
    public class SubmissionPaidStatusDto
    {
        /// <summary>
        /// Gets or sets the company VP code.
        /// </summary>
        public string CompanyVPCode { get; set; }

        /// <summary>
        /// Gets or sets the financial year.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission is paid.
        /// Will be null if no submission exists.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a submission exists for this company and year.
        /// </summary>
        public bool SubmissionAvailable { get; set; }
    }
}