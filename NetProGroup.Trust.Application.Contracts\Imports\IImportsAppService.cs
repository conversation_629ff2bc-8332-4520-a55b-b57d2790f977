﻿// <copyright file="IImportsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;

namespace NetProGroup.Trust.Application.Contracts.Imports
{
    /// <summary>
    /// Interface for ImportsAppService.
    /// </summary>
    public interface IImportsAppService
    {
        /// <summary>
        /// Imports a MasterClient Excel file.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to upload the file for.</param>
        /// <param name="formFile">The uploaded IFormFile.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ImportMasterClients(Guid jurisdictionId, IFormFile formFile);
    }
}