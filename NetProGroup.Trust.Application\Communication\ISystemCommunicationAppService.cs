﻿// <copyright file="ISystemCommunicationAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Messaging.Tokens;

namespace NetProGroup.Trust.Application.Communication
{
    /// <summary>
    /// Interface for the communications from the system.
    /// </summary>
    public interface ISystemCommunicationAppService : IScopedService
    {
        /// <summary>
        /// Sends an email message with the MFA verification code to the user.
        /// </summary>
        /// <param name="userId">Id of th euser to send the email to.</param>
        /// <param name="verificationCode">The verification code to include in the message.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task SendMFAVerificationCodeAsync(Guid userId, string verificationCode, TokenList tokens = null);

        /// <summary>
        /// Sends an email message with the MFA reset confimation code to the user.
        /// </summary>
        /// <param name="userId">Id of the user to send the email to.</param>
        /// <param name="confirmationCode">The confirmation code to include in the message.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task SendMFAResetConfirmationCodeAsync(Guid userId, string confirmationCode, TokenList tokens = null);

        /// <summary>
        /// Sends an email message with an invitation to register.
        /// </summary>
        /// <param name="userId">Id of the user to send the invitation to.</param>
        /// <param name="masterClientCode">The masterclient code to note in the message.</param>
        /// <param name="reregistration">Denotes whether this is for a reregistration.</param>
        /// <param name="tokens">Optional list of tokens to use.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task SendUserInvitationAsync(Guid userId, string masterClientCode, bool reregistration, TokenList tokens = null);
    }
}