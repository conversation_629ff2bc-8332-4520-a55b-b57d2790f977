// <copyright file="InboxAttachmentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Represents an attachment of an Inbox message.
    /// </summary>
    public class InboxAttachmentDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the original name of the file/document.
        /// </summary>
        public string Filename { get; set; }

        /// <summary>
        /// Gets or sets a description of the document.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the size of the file in bytes.
        /// </summary>
        public long FileSize { get; set; }
    }
}
