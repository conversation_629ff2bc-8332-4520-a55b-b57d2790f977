﻿// <copyright file="TokenHelpers.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;

namespace NetProGroup.Trust.API.Security
{
    /// <summary>
    /// Helpers for handling tokens.
    /// </summary>
    /// <remarks>
    /// https://auth0.com/blog/how-to-validate-jwt-dotnet/.
    /// </remarks>
    public static class TokenHelpers
    {
        /// <summary>
        /// Gets the token as validated JwtSecurityToken.
        /// </summary>
        /// <param name="token">The token as a string.</param>
        /// <param name="issuer">The required issuer.</param>
        /// <param name="audience">The required audience.</param>
        /// <param name="logger">The logger instance.</param>
        /// <returns>A <see cref="Task{JwtSecurityToken}"/> representing the result of the asynchronous operation.</returns>
        public static async Task<JwtSecurityToken> GetValidatedJwtSecurityTokenAsync(string token, string issuer, string audience, ILogger logger)
        {
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = audience,
                ValidateIssuerSigningKey = true,
                IssuerSigningKeys = await GetSigningKeysAsync(issuer),
                ValidateLifetime = true
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var result = await tokenHandler.ValidateTokenAsync(token, validationParameters);
            if (result.IsValid)
            {
                return (JwtSecurityToken)result.SecurityToken;
            }
            else
            {
                if (result.Exception != null)
                {
                    logger.LogWarning("Token validation failed with message: {Message}", result.Exception.Message);
                }

                return null;
            }
        }

        /// <summary>
        /// Gets the collection of signingkeys.
        /// </summary>
        /// <param name="issuer">The issuer to get the signing keys for.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public static async Task<IEnumerable<SecurityKey>> GetSigningKeysAsync(string issuer)
        {
            var metadataAddress = $"{issuer}/.well-known/openid-configuration";

            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                metadataAddress,
                new OpenIdConnectConfigurationRetriever(),
                new HttpDocumentRetriever());

            var discoveryDocument = await configurationManager.GetConfigurationAsync();
            var signingKeys = discoveryDocument.SigningKeys;

            return signingKeys;
        }
    }
}
