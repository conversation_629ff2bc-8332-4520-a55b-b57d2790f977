// <copyright file="BoDirAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Export;
using NetProGroup.Trust.DataManager.BoDir;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.BoDir
{
    /// <summary>
    /// Implementation of the Beneficial Owner and Director (BO/Dir) application service.
    /// </summary>
    public class BoDirAppService : IBoDirAppService
    {
        private readonly IBoDirDataManager _boDirDataManager;
        private readonly ISecurityManager _securityManager;
        private readonly IBoDirExportGenerator _boDirExportGenerator;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirAppService"/> class.
        /// </summary>
        /// <param name="boDirDataManager">The manager to use for BO/Dir operations.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="boDirExportGenerator">The export generator.</param>
        /// <param name="mapper">The AutoMapper instance.</param>
        public BoDirAppService(IBoDirDataManager boDirDataManager,
            ISecurityManager securityManager,
            IBoDirExportGenerator boDirExportGenerator,
            IMapper mapper)
        {
            _boDirDataManager = boDirDataManager;
            _securityManager = securityManager;
            _boDirExportGenerator = boDirExportGenerator;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<BoDirItemDTO>> SearchBoDirsAsync(SearchBoDirRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var jurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(WellKnownPermissionNames.BODIRModule_Search);

            var searchRequest = _mapper.Map<SearchBoDirRequest>(request);
            searchRequest.AuthorizedJurisdictionIDs = jurisdictionIDs;

            if (searchRequest.DataStatuses.Count > 0 && searchRequest.Specifics.Contains(BoDirSpecifics.NoBoDirInformation))
            {
                throw new BadRequestException("NoBoDirInformation can not be combined with a status");
            }

            return await _boDirDataManager.SearchBoDirsAsync(searchRequest);
        }

        /// <inheritdoc />
        public async Task<BoDirDownloadResponseDTO> GenerateBoDirRSearchResultsExportAsync(SearchBoDirRequestDTO request)
        {
            var jurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(WellKnownPermissionNames.BODIRModule_Export);

            return await _boDirExportGenerator.GenerateAsync(request, jurisdictionIDs);
        }
    }
}
