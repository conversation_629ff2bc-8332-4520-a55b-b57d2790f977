// <copyright file="ReportsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Report;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Report
{
    /// <summary>
    /// Use this controller for report related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class ReportsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IReportAppService _reportAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReportsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="reportAppService">The service for reports.</param>
        public ReportsController(
            ILogger<ReportsController> logger,
            IConfiguration configuration,
            IReportAppService reportAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;
            _reportAppService = reportAppService;
        }

        /// <summary>
        /// Gets a paginated list of reports by specified types.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/reports?PageNumber=1&amp;PageSize=100&amp;ReportTypes=Financial&amp;ReportTypes=ContactsInfo
        /// .
        /// </remarks>
        /// <param name="request">The request parameters for pagination and filtering of reports.</param>
        /// <returns>A paginated list of reports matching the specified types.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_GetReportsByType")]
        [ProducesResponseType(typeof(PaginatedResponse<ReportDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReportsByType([FromQuery] ReportRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                request.ToPagingInfo(),
                validate: () =>
                {
                    Check.Positive(request.PageNumber, nameof(request.PageNumber));
                    Check.Positive(request.PageSize, nameof(request.PageSize));
                },
                executeAsync: async _ => await _reportAppService.GetReportsByTypeAsync(request));

            return result.AsResponse();
        }

        /// <summary>
        /// Downloads a financial report as a file based on the specified <paramref name="reportId"/>.
        /// </summary>
        /// <param name="reportId">
        /// A unique identifier (GUID) for the report to download. This is specified in the route.
        /// </param>
        /// <returns>
        /// A <see cref="FileContentResult"/> containing the financial report file with appropriate content type
        /// and file name if the download is successful. Returns an appropriate response code if the request is unauthorized or forbidden.
        /// </returns>
        [HttpGet("{reportId:guid}/download")]
        [SwaggerOperation(OperationId = "Management_DownloadReport")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadReport([FromRoute] Guid reportId)
        {
            ReportDownloadResponseDTO item = null;
            var result = await ProcessRequestAsync<ExportSubmissionResponseDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(reportId, nameof(reportId));
                },
                executeAsync: async () =>
                {
                    item = await _reportAppService.DownloadReportAsync(reportId);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }
    }
}