﻿// <copyright file="PermissionNameAttribute.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Represents an attribute that is used to mark a property as a permission name.
    /// This will be used to generate Swagger documentation with the possible values.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public sealed class PermissionNameAttribute : Attribute;
}