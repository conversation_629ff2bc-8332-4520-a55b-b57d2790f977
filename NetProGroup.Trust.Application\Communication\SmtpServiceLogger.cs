﻿// <copyright file="SmtpServiceLogger.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.SendGrid;
using NetProGroup.Framework.Services.Communication.Models;
using NetProGroup.Framework.Smtp;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;

namespace NetProGroup.Trust.Application.Communication
{
    /// <summary>
    /// A logger for the SMTP service that logs email sending operations.
    /// </summary>
    internal sealed class SmtpServiceLogger : ISmtpService
    {
        private readonly SmtpService _smtpServiceImplementation;
        private readonly ILogger<SmtpService> _logger;
        private readonly IApplicationInsightsDependencyTracker _dependencyTracker;

        /// <summary>
        /// Initializes a new instance of the <see cref="SmtpServiceLogger"/> class.
        /// </summary>
        /// <param name="smtpServiceImplementation">The SMTP service implementation.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="dependencyTracker">The dependency tracker for app insights.</param>
        public SmtpServiceLogger(
            SmtpService smtpServiceImplementation,
            ILogger<SmtpService> logger,
            IApplicationInsightsDependencyTracker dependencyTracker)
        {
            _smtpServiceImplementation = Check.NotNull(smtpServiceImplementation, nameof(smtpServiceImplementation));
            _logger = Check.NotNull(logger, nameof(logger));
            _dependencyTracker = Check.NotNull(dependencyTracker, nameof(dependencyTracker));
        }

        /// <inheritdoc />
        public async Task<bool> SendEmailAsync(EmailInfo emailInfo, string toEmail, string userName)
        {
            return await _dependencyTracker.TrackDependencyAsync(
                "SMTP.SendEmail",
                () =>
                {
                    _logger.LogInformation("Sending email {EmailProperties}", new
                    {
                        toEmail,
                        userName,
                        emailInfo.Subject,
                        emailInfo.ToUserId
                    });

                    return _smtpServiceImplementation.SendEmailAsync(emailInfo, toEmail, userName);
                },
                "Email",
                "SMTP",
                $"To: {toEmail}, Subject: {emailInfo.Subject}");
        }

        /// <inheritdoc />
        public async Task<bool> SendEmailAsync(QueuedMessageDTO message)
        {
            return await _dependencyTracker.TrackDependencyAsync(
                "SMTP.SendEmail",
                () =>
                {
                    _logger.LogInformation("Sending email {EmailProperties}", new
                    {
                        message.To,
                        message.ToName,
                        message.Subject
                    });

                    return _smtpServiceImplementation.SendEmailAsync(message);
                },
                "Email",
                "SMTP",
                $"To: {message.To}, Subject: {message.Subject}");
        }
    }
}