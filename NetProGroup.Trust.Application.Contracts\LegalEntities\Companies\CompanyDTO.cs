﻿// <copyright file="CompanyDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Enums;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// Represents a Company.
    /// </summary>
    public class CompanyDTO : LegalEntityDTO
    {
        /// <summary>
        /// Gets or sets the incorporation nr. of the company.
        /// </summary>
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the company legacy code.
        /// </summary>
        public string LegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the incorporation date of the company.
        /// </summary>
        public DateTime IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the referral office of the company.
        /// </summary>
        public string ReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the onboarding status of the company.
        /// </summary>
        public OnboardingStatus OnboardingStatus { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the compnay was previously declined.
        /// </summary>
        public bool PreviouslyDeclined { get; set; }
    }
}
