// <copyright file="IContactsInfoReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.ContactsInfoReports
{
    /// <summary>
    /// Interface for generating contact information reports.
    /// </summary>
    public interface IContactsInfoReportService : ITransientService, IReportService;
}
