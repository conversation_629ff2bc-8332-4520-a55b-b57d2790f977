// <copyright file="InboxInfoDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Represents the inbox of a user.
    /// </summary>
    public class InboxInfoDTO
    {
        /// <summary>
        /// Gets or sets the total number of messages in the inbox.
        /// </summary>
        public int TotalMessages { get; set; }

        /// <summary>
        /// Gets or sets the total number of unread messages.
        /// </summary>
        public int TotalUnreadMessages { get; set; }
    }
}
