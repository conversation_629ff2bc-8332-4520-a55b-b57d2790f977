﻿// <copyright file="STRLatePaymentFeeSettingsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Representts a list of STRLatePaymentFeeDTO.
    /// </summary>
    public class STRLatePaymentFeeSettingsDTO : IAttributedSettings
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="STRLatePaymentFeeSettingsDTO"/> class.
        /// </summary>
        public STRLatePaymentFeeSettingsDTO()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="STRLatePaymentFeeSettingsDTO"/> class.
        /// </summary>
        /// <param name="items">The items for this list.</param>
        public STRLatePaymentFeeSettingsDTO(IReadOnlyCollection<STRLatePaymentFeeDTO> items)
        {
            STRLatePaymentFees = items.ToArray();
        }

        /// <summary>
        /// Gets or sets a list of STRLatePaymentFeeDTO.
        /// </summary>
        public ICollection<STRLatePaymentFeeDTO> STRLatePaymentFees { get; set; } = new HashSet<STRLatePaymentFeeDTO>();
    }
}
