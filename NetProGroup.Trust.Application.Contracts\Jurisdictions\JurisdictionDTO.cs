﻿// <copyright file="JurisdictionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Jurisdictions
{
    /// <summary>
    /// Represents a jurisdiction.
    /// </summary>
    public class JurisdictionDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionDTO"/> class.
        /// </summary>
        public JurisdictionDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the jurisdictions.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the jurisdictions.
        /// </summary>
        public string Code { get; set; }
    }
}
