﻿// <copyright file="OneOfOperationFilter.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;
using NetProGroup.Framework.Tools;
using OneOf;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace NetProGroup.Trust.API.Swagger
{
    /// <summary>
    /// Swagger filter to render OneOf types as OpenApiSchema. This allows us to specify multiple response types for a single status code.
    /// </summary>
    public class OneOfOperationFilter : IOperationFilter
    {
        /// <inheritdoc/>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            Check.NotNull(operation, nameof(operation));

            var methodInfo = context?.MethodInfo;
            var methodAttributes = methodInfo?
                                   .GetCustomAttributes()
                                   .OfType<ProducesResponseTypeAttribute>()
                                   ?? new List<ProducesResponseTypeAttribute>();

            var containingType = methodInfo?.DeclaringType;
            var classAttributes = containingType?
                                  .GetCustomAttributes()
                                  .OfType<ProducesResponseTypeAttribute>() ?? new List<ProducesResponseTypeAttribute>();

            var baseClassAttributes = containingType?
                                      .BaseType?
                                      .GetCustomAttributes()
                                      .OfType<ProducesResponseTypeAttribute>() ?? new List<ProducesResponseTypeAttribute>();

            var oneOfResponseAttributes = Enumerable.Concat(Enumerable.Concat(methodAttributes, classAttributes), baseClassAttributes).ToList();

            if (oneOfResponseAttributes.Count == 0)
            {
                return;
            }

            // process all ProducesResponseType attributes
            foreach (var oneOfResponseAttribute in oneOfResponseAttributes)
            {
                var statusCode = oneOfResponseAttribute.StatusCode;
                var oneOfResponseType = oneOfResponseAttribute.Type;

                // skip if current type is not IOneOf
                if (!oneOfResponseType.IsAssignableTo(typeof(IOneOf)))
                {
                    continue;
                }

                // find parent class of type OneOf<T1, ...> or OneOfBase<T1, ...>
                while (oneOfResponseType is { IsGenericType: false })
                {
                    oneOfResponseType = oneOfResponseType.BaseType;
                }

                // skip if unable to find appropriate parent class
                if (oneOfResponseType == null)
                {
                    continue;
                }

                var allOneOfTypes = GetGenericParameterTypes(oneOfResponseType);

                var oneOfSchemas = new List<OpenApiSchema>();

                foreach (var oneOfType in allOneOfTypes)
                {
                    var oneOfTypeSchema = context.SchemaGenerator.GenerateSchema(oneOfType, context.SchemaRepository);
                    if (oneOfTypeSchema != null)
                    {
                        oneOfSchemas.Add(oneOfTypeSchema);
                    }
                }

                if (oneOfSchemas.Count != 0)
                {
                    var operationResponse = operation.Responses[statusCode.ToString()];
                    operationResponse.Content.Clear();
                    operationResponse.Content["application/json"] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Type = "object",
                            OneOf = oneOfSchemas,
                        },
                    };
                }
            }
        }

        private static List<Type> GetGenericParameterTypes(Type type)
        {
            if (type.IsGenericType && type.IsAssignableTo(typeof(IOneOf)))
            {
                return type.GetGenericArguments().ToList();
            }
            else
            {
                throw new ArgumentException($"Type {type.FullName} is not assignable to {typeof(IOneOf)}");
            }
        }
    }
}