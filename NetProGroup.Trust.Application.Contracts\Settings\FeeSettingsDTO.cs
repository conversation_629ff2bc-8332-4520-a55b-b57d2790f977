﻿// <copyright file="FeeSettingsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Settings;

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Represents the fee settings.
    /// </summary>
    public class FeeSettingsDTO : IAttributedSettings
    {
        /// <summary>
        /// Gets or sets the fee to pay for a Simple Tax Return submission.
        /// </summary>
        [SettingKey(key: ConfigurationKeys.STRSubmissionFee)]
        public decimal? STRSubmissionFee { get; set; }

        /// <summary>
        /// Gets or sets the text to use for the invoice for a Simple Tax Return submission.
        /// </summary>
        [SettingKey(key: "str-submission-fee-invoicetext")]
        public string STRSubmissionFeeInvoiceText { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether there is an exempt for 'late payment fee' for a Simple Tax Return submission.
        /// </summary>
        [SettingKey(key: ConfigurationKeys.STRSubmissionLatePaymentFeeExempt)]
        public bool? STRSubmissionLatePaymentFeeExempt { get; set; }

        /// <summary>
        /// Gets or sets the fee to pay for a Basic Financial Report submission.
        /// </summary>
        [SettingKey(key: ConfigurationKeys.BFRSubmissionFee)]
        public decimal? BFRSubmissionFee { get; set; }
    }
}
