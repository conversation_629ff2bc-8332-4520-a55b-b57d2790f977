﻿// <copyright file="SubmissionKeyValueDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Submissions;

namespace NetProGroup.Trust.API.Swagger.ResponseTypes
{
    /// <summary>
    /// Version of <see cref="SubmissionDTO"/> that includes a <see cref="FormDocumentWithRevisionsKeyValueDTO"/> form document.
    /// Intended to generate correct OpenAPI documentation.
    /// </summary>
    public class SubmissionKeyValueDTO : SubmissionDTO
    {
        /// <summary>
        /// Gets or sets the FormDocument.
        /// Override of the type of the form document, to display the correct type in OpenAPI documentation.
        /// </summary>
        public new FormDocumentWithRevisionsKeyValueDTO FormDocument { get; set; }
    }
}