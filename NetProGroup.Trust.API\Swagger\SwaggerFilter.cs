﻿// <copyright file="SwaggerFilter.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace NetProGroup.Trust.API.Swagger
{
    /// <summary>
    /// Setup custom swagger filter.
    /// </summary>
    public class SwaggerFilter : Swashbuckle.AspNetCore.SwaggerGen.IOperationFilter
    {
        /// <summary>
        /// Apply the custom headers to a swagger request.
        /// </summary>
        /// <param name="operation">An instance of OpenApiOperation.</param>
        /// <param name="context">An instance of OperationFilterContext.</param>
        public void Apply([NotNull] OpenApiOperation operation, [NotNull] OperationFilterContext context)
        {
            if (operation.Parameters == null)
            {
                operation.Parameters = new List<OpenApiParameter>();
            }

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "x-userid",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string"
                }
            });
        }
    }
}
