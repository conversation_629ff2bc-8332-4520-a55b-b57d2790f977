﻿// <copyright file="BoDirDataStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.BoDir
{
    /// <summary>
    /// Represents the possible data status values for BO/Dir entries.
    /// </summary>
    public enum BoDirDataStatus // TODO use existing enum
    {
        /// <summary>
        /// Initial status.
        /// </summary>
        Initial = 0,

        /// <summary>
        /// Refreshed status.
        /// </summary>
        Refreshed = 1,

        /// <summary>
        /// Confirmed status.
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// Pending update request status.
        /// </summary>
        PendingUpdateRequest = 3,

        /// <summary>
        /// Subsequent status.
        /// </summary>
        Subsequent = 4
    }
}
