// <copyright file="FilterAnnouncementsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Represent the necessary data used to filter announcements.
    /// </summary>
    public class FilterAnnouncementsDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities, masterclients or referral offices.
        /// </summary>
        /// <remarks>
        /// Use this searchterm to search with a single term for 'or-ring' the search.
        /// </remarks>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the field to sort by.
        /// </summary>
        [SortableColumns(
            nameof(ListAnnouncementDTO.SendAt),
            nameof(ListAnnouncementDTO.Subject),
            nameof(ListAnnouncementDTO.Status),
            nameof(ListAnnouncementDTO.MasterClientIds),
            nameof(ListAnnouncementDTO.MasterClientCodes),
            nameof(ListAnnouncementDTO.JurisdictionIds),
            nameof(ListAnnouncementDTO.JurisdictionNames))]
        public override string SortBy { get; set; }
    }
}