﻿// <copyright file="UsersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Communication;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.MFA;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Invitations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.DataManager.Users.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Permissions;
using System.Reflection;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Users
{
    /// <summary>
    /// AppService for users related methods.
    /// </summary>
    public class UsersAppService : IUsersAppService
    {
        private readonly IWorkContext _workContext;
        private readonly IUserManager _userManager;
        private readonly IUsersDataManager _usersDataManager;
        private readonly IUserRepository _usersRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly ISystemCommunicationAppService _systemCommunicationAppService;
        private readonly IUserInvitationsDataManager _userInvitationsDataManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IApplicationRolesManager _applicationRolesManager;
        private readonly ISecurityManager _securityManager;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;

        private readonly TrustDbContext _dbContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="UsersAppService"/> class.
        /// </summary>
        /// <param name="workContext">The current work context.</param>
        /// <param name="dbContext">The current DbContext.</param>
        /// <param name="userManager">The manager responsible for user operations (from the NetPro framework).</param>
        /// <param name="usersDataManager">The DataManager to handle platform specific actions.</param>
        /// <param name="usersRepository">The repository for users.</param>
        /// <param name="systemCommunicationAppService">The service for sending system message to users.</param>
        /// <param name="userInvitationsDataManager">The service for user invitations.</param>
        /// <param name="systemAuditManager">The manager for system audits.</param>
        /// <param name="applicationRolesManager">Instance of the applicationroles manager.</param>
        /// <param name="securityManager">Instance of the securitymanager.</param>
        /// <param name="masterClientsRepository">The repository for masterclients.</param>
        /// <param name="jurisdictionsRepository">The repository for jurisdictions.</param>
        public UsersAppService(IWorkContext workContext,
            TrustDbContext dbContext,
            IUserManager userManager,
            IUsersDataManager usersDataManager,
            IUserRepository usersRepository,
            ISystemCommunicationAppService systemCommunicationAppService,
            IUserInvitationsDataManager userInvitationsDataManager,
            ISystemAuditManager systemAuditManager,
            IApplicationRolesManager applicationRolesManager,
            ISecurityManager securityManager,
            IMasterClientsRepository masterClientsRepository,
            IJurisdictionsRepository jurisdictionsRepository)
        {
            _workContext = workContext;
            _dbContext = dbContext;
            _userManager = userManager;
            _usersDataManager = usersDataManager;
            _systemCommunicationAppService = systemCommunicationAppService;
            _userInvitationsDataManager = userInvitationsDataManager;
            _systemAuditManager = systemAuditManager;
            _usersRepository = usersRepository;
            _applicationRolesManager = applicationRolesManager;
            _securityManager = securityManager;
            _masterClientsRepository = masterClientsRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
        }

        #region Public methods

        /// <inheritdoc/>
        public async Task<TermsConditionsStatusDTO> GetTermsConditionsStatusAsync(Guid userId)
        {
            return await _usersDataManager.GetTermsConditionsStatusAsync(userId);
        }

        /// <inheritdoc/>
        public async Task AcceptTermsConditionsAsync(Guid userId, AcceptTermsConditionsDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            Check.NotNullOrEmpty(model.Version, nameof(model.Version));

            await _usersDataManager.AcceptTermsConditionsAsync(new AcceptTermsConditionsRequest() { UserId = userId, Version = model.Version });
        }

        /// <inheritdoc/>
        public async Task<GetUserMFAMethodDTO> GetUserMFAMethodAsync(Guid userId)
        {
            var method = await _usersDataManager.GetMFAMethodAsync(userId);
            return new GetUserMFAMethodDTO { UserId = userId, MFAMethod = method };
        }

        /// <inheritdoc/>
        public async Task<MFAInfoDTO> SetUserMFAMethodAsync(SetUserMFAMethodDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _usersDataManager.SetMFAMethodAsync(model.UserId, model.MFAMethod);
            var response = await _usersDataManager.GetUserMFAInfoAsync(new GetUserMFARequest { UserId = model.UserId });

            if (response.MFAEmailCodeExpiresAt.HasValue)
            {
                // Do not send. Application is also calling endpoint for email code
                // await SendMFACodeToUserAsync(response);
            }

            return MapMFAInfoDTO(response);
        }

        /// <inheritdoc/>
        public async Task<MFAInfoDTO> GetUserMFAInfoAsync(Guid userId)
        {
            var response = await _usersDataManager.GetUserMFAInfoAsync(new GetUserMFARequest { UserId = userId });
            return MapMFAInfoDTO(response);
        }

        /// <inheritdoc/>
        public async Task<MFAInfoDTO> RequestMFACodeByEmailAsync(Guid userId)
        {
            var response = await _usersDataManager.GetUserMFAInfoAsync(new GetUserMFARequest { UserId = userId });

            // Use the code from the response to send an email.
            var emailCode = response.MFAEmailCode;

            await SendMFACodeToUserAsync(response);

            return MapMFAInfoDTO(response);
        }

        /// <inheritdoc/>
        public async Task<VerifyMFACodeResultDTO> VerifyMFACodeAsync(VerifyMFACodeDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var response = await _usersDataManager.VerifyUserMFAResponseAsync(new VerifyUserMFARequest { UserId = model.UserId, ResponseCode = model.VerificationCode });
            return new VerifyMFACodeResultDTO
            {
                VerificationCode = model.VerificationCode,
                Success = response.Success
            };
        }

        /// <inheritdoc/>
        public async Task ResetUserMFAInfoAsync(Guid userId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Users_ResetAuthentication);

            await _usersDataManager.ResetMFAMethodAsync(userId);
            await _systemAuditManager.AddActivityLogAsync(new ApplicationUser(userId), ActivityLogActivityTypes.UserMFAReset, "Reset of MFA for user.", saveChanges: true);
        }

        /// <inheritdoc/>
        public async Task<PCPApplicationUserDTO> ExternalIdUserSignedInAsync(string emailAddress, Guid objectId)
        {
            PCPApplicationUserDTO result = null;

            // Try to find by objectid first
            var usersByObjectId = await _usersRepository.FindUsersByObjectIdsAsync(new List<Guid> { objectId });
            var user = usersByObjectId.SingleOrDefault();

            // Object unknown so get by email and null objectid
            if (user == null)
            {
                user = await _usersRepository.FindByUserByPredicateAsync(x => x.Email == emailAddress && x.ObjectId == null);
            }

            if (user == null)
            {
                var text = $"The {typeof(ApplicationUser)} with email '{emailAddress}' was not found";
                var code = ApplicationErrors.USER_EMAIL_NOT_FOUND.ToErrorCode();

                throw new NotFoundException(code, text);
            }

            if (user.LockoutEnabled)
            {
                return await _usersDataManager.GetUserByIdAsync(user.Id);
            }

            // From this point on we know the user
            if (!_workContext.IdentityUserId.HasValue || _workContext.IdentityUserId.Value == Guid.Empty)
            {
                _workContext.IdentityUserId = user.Id;
            }

            if (!user.ObjectId.HasValue)
            {
                await _usersDataManager.UpdateUserObjectIdAsync(user.Id, objectId);

                await _usersDataManager.SetAttributeValueAsync(user.Id, UserAttributeKeys.RegistrationAt, DateTime.UtcNow);

                await _systemAuditManager.AddUserRegisteredActivityLogAsync(user);
            }

            await _systemAuditManager.AddUserAuthenticatedActivityLogAsync(user, saveChanges: true);

            result = await _usersDataManager.GetUserByIdAsync(user.Id);

            return result;
        }

        /// <inheritdoc/>
        public async Task<PCPApplicationUserDTO> EntraUserSignedInAsync(Guid objectId)
        {
            PCPApplicationUserDTO result = null;

            var applicationId = _workContext.ClientApplicationId();
            await SyncEntraUserByObjectIdAsync(objectId, applicationId.Value, true);

            var user = await _usersRepository.FindByUserByPredicateAsync(x => x.ObjectId == objectId);

            if (user != null && user.LockoutEnabled)
            {
                return await _usersDataManager.GetUserByIdAsync(user.Id);
            }

            // From this point on we know the user
            if (!_workContext.IdentityUserId.HasValue || _workContext.IdentityUserId.Value == Guid.Empty)
            {
                _workContext.IdentityUserId = user.Id;
            }

            if (!user.ObjectId.HasValue)
            {
                await _usersDataManager.UpdateUserObjectIdAsync(user.Id, objectId);

                await _systemAuditManager.AddUserRegisteredActivityLogAsync(user, saveChanges: true);
            }

            await _systemAuditManager.AddUserAuthenticatedActivityLogAsync(user, saveChanges: true);

            result = await _usersDataManager.GetUserByIdAsync(user.Id);

            return result;
        }

        /// <inheritdoc/>
        public async Task UserSignedOutAsync(Guid userId)
        {
            var user = await _usersRepository.CheckUserByIdAsync(userId);

            await _systemAuditManager.AddUserLogoutActivityLogAsync(user, saveChanges: true);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListApplicationUsersDTO>> ListUsersAsync(UsersRequestDTO request)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Users_Search);

            return await _usersDataManager.ListUsersAsync(request);
        }

        /// <inheritdoc/>
        public async Task<PCPApplicationUserDTO> GetUserByIdAsync(Guid id)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Users_View);

            return await _usersDataManager.GetUserByIdAsync(id);
        }

        /// <inheritdoc/>
        public async Task<bool> BlockUnblockUserAsync(Guid id, BlockUserDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Authorization
            if (request.IsBlocked)
            {
                await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Users_Block);
            }
            else
            {
                await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Users_Unblock);
            }

            return await _usersDataManager.BlockUnblockUserAsync(id, request);
        }

        /// <inheritdoc/>
        public async Task SetUserMasterClientsAsync(UserMasterClientsDTO userMasterClientsDTO)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_Add_Trident_Users);

            await _usersDataManager.SetUserMasterClientsAsync(userMasterClientsDTO);
        }

        /// <inheritdoc/>
        public async Task<IList<UserPermissionDTO>> GetPermissionsAsync(Guid? userId, Guid? jurisdictionId)
        {
            // TODO do we need this method, does the frontend ever need to get the permissions of a user that is not the logged-in user?
            if (!userId.HasValue)
            {
                await _securityManager.RequireUserAsync();
                userId = _securityManager.UserId;
            }

            var userPermissionsResponse = new List<UserPermissionDTO>();
            await GetUserPermissions(userId, jurisdictionId, userPermissionsResponse);

            return userPermissionsResponse;
        }

        /// <inheritdoc/>
        public async Task<IList<UserPermissionDTO>> GetPermissionsAsync(Guid? jurisdictionId)
        {
            await _securityManager.RequireClientUserAsync();

            var userPermissionsResponse = new List<UserPermissionDTO>();
            await GetUserPermissions(_workContext.IdentityUserId, jurisdictionId, userPermissionsResponse);

            return userPermissionsResponse;
        }

        /// <inheritdoc/>
        public async Task<IList<Guid>> GetAuthorizedJurisdictionsAsync()
        {
            return await _securityManager.GetAllJurisdictionsForManagementPermissionsAsync();
        }

#if DEBUG
        /// <inheritdoc/>
        public async Task<ApplicationUserDTO> CreateUserAsync(CreateUserDTO createUserModel)
        {
            return await _usersDataManager.CreateUserAsync(createUserModel);
        }
#endif

        /// <inheritdoc/>
        public async Task SendInvitationAsync(Guid userId, Guid? masterClientId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_SendInvitation);

            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
            var user = await _usersRepository.CheckUserByIdAsync(userId);

            var masterClientCode = string.Empty;
            if (masterClientId.HasValue)
            {
                var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(userId);
                masterClientCode = masterClient.Code;
            }

            // If user already has an objectid, it is a reregistration (however an invitation is not needed as the user already has an account)
            bool reregistration = user.ObjectId != null;

            await _systemAuditManager.AddActivityLogAsync(user, ActivityLogActivityTypes.UserInvitationSentManually, "Invitation sent.", $"An invitation for user '{user.Email}' is sent without conditions.");
            await _userInvitationsDataManager.SendInvitationToUserAsync(userId, masterClientCode: masterClientCode, force: true, reregistration: reregistration, saveChanges: true);
        }

        /// <inheritdoc/>
        public async Task SendInvitationsAsync(SendInvitationsDTO request)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_SendInvitation);

            ArgumentNullException.ThrowIfNull(request, nameof(request));

            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            try
            {
                if (request.UserIds != null)
                {
                    foreach (var userId in request.UserIds)
                    {
                        var user = await _usersRepository.CheckUserByIdAsync(userId);

                        // If user already has an objectid, it is a reregistration (however an invitation is not needed as the user already has an account)
                        bool reregistration = user.ObjectId != null;

                        await _systemAuditManager.AddActivityLogAsync(user, ActivityLogActivityTypes.UserInvitationSentManually, "Invitation sent.", $"An invitation for user '{user.Email}' is sent without conditions.");
                        await _userInvitationsDataManager.SendInvitationToUserAsync(userId, force: true, reregistration: reregistration, saveChanges: true);
                    }
                }

                if (request.UserMasterClients != null)
                {
                    foreach (var userMasterClient in request.UserMasterClients)
                    {
                        var user = await _usersRepository.CheckUserByIdAsync(userMasterClient.UserId);
                        var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(userMasterClient.MasterClientId);

                        masterClient = await _masterClientsRepository.GetByIdAsync(userMasterClient.MasterClientId, q => q.Include(mc => mc.MasterClientUsers));
                        if (!masterClient.MasterClientUsers.Any(mcu => mcu.UserId == userMasterClient.UserId))
                        {
                            var code = ApplicationErrors.MASTERCLIENT_USER_NOT_FOUND.ToErrorCode();
                            throw new NotFoundException(code, $"User {user.Email} is not assigned to masterclient {masterClient.Code}");
                        }

                        var masterClientCode = masterClient.Code;

                        // If user already has an objectid, it is a reregistration (however an invitation is not needed as the user already has an account)
                        bool reregistration = user.ObjectId != null;

                        await _systemAuditManager.AddActivityLogAsync(user, ActivityLogActivityTypes.UserInvitationSentManually, "Invitation sent.", $"An invitation for user '{user.Email}' is sent without conditions.");
                        await _userInvitationsDataManager.SendInvitationToUserAsync(userMasterClient.UserId, masterClientCode: masterClientCode, force: true, reregistration: reregistration, saveChanges: true);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<RequestMFAResetResultDTO> RequestUserMFAResetAsync(Guid userId)
        {
            var response = await _usersDataManager.RequestMFAResetAsync(new MFAResetRequest { UserId = userId });

            // Use the code from the response to send an email.
            await SendMFAResetCodeToUserAsync(response);

            var result = new RequestMFAResetResultDTO
            {
                UserId = userId,
                MFAEmailCodeExpiresIn = response.MFAEmailCodeExpiresIn,
                MFAEmailCodeExpiresAt = response.MFAEmailCodeExpiresAt
            };

            return result;
        }

        /// <inheritdoc/>
        public async Task<ConfirmMFAResetResultDTO> ConfirmUserMFAResetAsync(ConfirmMFAResetDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var response = await _usersDataManager.ConfirmMFAResetAsync(new ConfirmMFAResetRequest { UserId = model.UserId, ResponseCode = model.ConfirmationCode });

            if (response.Success)
            {
                await _systemAuditManager.AddActivityLogAsync(new ApplicationUser(model.UserId), ActivityLogActivityTypes.UserMFAResetConfirmed, "Reset of MFA confirmed by user.", saveChanges: true);
            }

            var result = new ConfirmMFAResetResultDTO { ConfirmationCode = model.ConfirmationCode, Success = response.Success };

            return result;
        }

        /// <inheritdoc/>
        public async Task ComplementWithPermissionsAsync(PCPApplicationUserDTO user)
        {
            ArgumentNullException.ThrowIfNull(user, nameof(user));

            var collectedPermissions = new List<UserPermissionDTO>();

            var allJurisdictions = await _jurisdictionsRepository.FindAllAsync();
            foreach (var jurisdiction in allJurisdictions)
            {
                await GetUserPermissions(user.Id, jurisdiction.Id, collectedPermissions);
            }

            // Un-duplicate permissions; a user can have permissions based on different roles
            foreach (var permission in collectedPermissions)
            {
                if (!user.Permissions.Any(p => p.PermissionName == permission.PermissionName))
                {
                    user.Permissions.Add(permission);
                }
            }
        }

        #endregion

        #region Private methods

        private static MFAInfoDTO MapMFAInfoDTO(GetUserMFAResponse response)
        {
            var result = new MFAInfoDTO
            {
                UserId = response.UserId,
                MFAMethod = response.MFAMethod,
                MFAIsEnabled = response.MFAIsEnabled,
                MFAAuthenticatorSecret = response.MFAAuthenticatorSecret,
                MFAAuthenticatorQRUrl = response.MFAAuthenticatorQRUrl,
                MFAEmailCodeExpiresAt = response.MFAEmailCodeExpiresAt,
                MFAEmailCodeExpiresIn = response.MFAEmailCodeExpiresIn
            };

            return result;
        }

        /// <summary>
        /// Send the MFA code to the user.
        /// </summary>
        /// <param name="mfaInfo">The response of the MFA check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SendMFACodeToUserAsync(GetUserMFAResponse mfaInfo)
        {
            await _systemCommunicationAppService.SendMFAVerificationCodeAsync(mfaInfo.UserId, mfaInfo.MFAEmailCode);
        }

        /// <summary>
        /// Send the code for MFA reset to the user.
        /// </summary>
        /// <param name="mfaResetResponse">The response of the MFA check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SendMFAResetCodeToUserAsync(MFAResetResponse mfaResetResponse)
        {
            await _systemCommunicationAppService.SendMFAResetConfirmationCodeAsync(mfaResetResponse.UserId, mfaResetResponse.MFAEmailCode);
        }

        /// <summary>
        /// Gets the user information including the application roles from Entra and create/updates the data in the database.
        /// </summary>
        /// <param name="objectId">ObjectId of the user to sync.</param>
        /// <param name="applicationId">Id of the application to get the roles for.</param>
        /// <param name="syncRoles">Determine if the reoles are meant to be synced.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task<ApplicationUserDTO> SyncEntraUserByObjectIdAsync(Guid objectId, Guid applicationId, bool syncRoles = false)
        {
            Check.NotDefaultOrNull<Guid>(objectId, nameof(objectId));
            Check.NotDefaultOrNull<Guid>(applicationId, nameof(applicationId));

            await _applicationRolesManager.SyncUserFromEntraAsync(objectId, new List<string> { applicationId.ToString() }, syncRoles);

            var result = await _userManager.GetUserByObjectIdAsync(objectId);

            return result;
        }

        /// <summary>
        /// Gets the permissions for a given user with the optional jurisdiction and adds it to the provided list of user permissions.
        /// </summary>
        /// <param name="userId">The optional id of the user.</param>
        /// <param name="jurisdictionId">The optional unique identifier of the jurisdiction.</param>
        /// <param name="userPermissionsResponse">The list to which the permissions will be added.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        private async Task GetUserPermissions(Guid? userId, Guid? jurisdictionId, List<UserPermissionDTO> userPermissionsResponse)
        {
            var permissionFields =
                typeof(WellKnownPermissionNames)
                    .GetFields(BindingFlags.Public | BindingFlags.Static)
                    .Where(f => f.FieldType == typeof(string)); ;

            if (jurisdictionId.HasValue)
            {
                _workContext.SetProperty("jurisdictionId", jurisdictionId.Value);
            }

            var savedUserId = _securityManager.UserId;

            try
            {
                if (userId.HasValue)
                {
                    _securityManager.UserId = userId.Value;
                }

                foreach (var field in permissionFields)
                {
                    var permissionName = (string)field.GetValue(null);
                    if (await _securityManager.HasManagementPermissionAsync(permissionName))
                    {
                        userPermissionsResponse.Add(new UserPermissionDTO { PermissionName = permissionName });
                    }
                }
            }
            finally
            {
                _securityManager.UserId = savedUserId;
            }
        }

        #endregion
    }
}
