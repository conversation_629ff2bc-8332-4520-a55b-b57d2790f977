﻿// <copyright file="InvoiceNumberingSettingsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Represents the retrieved settings for invoice numbering.
    /// </summary>
    public class InvoiceNumberingSettingsDTO
    {
        /// <summary>
        /// Gets or sets the id of the jurisdiction this configuration applies to.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module this configuration applies to.
        /// </summary>
        /// <remarks>
        /// If null, this configuration is the default for the jurisdiction.
        /// </remarks>
        public Guid? ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the format for the prefix in the invoice number. For example '{yy}{mm}/'.
        /// </summary>
        public string PrefixFormat { get; set; }

        /// <summary>
        /// Gets or sets the format for the range prefix in the invoice number. For example '{yy}{mm}'.
        /// </summary>
        /// <remarks>
        /// The range prefix is used to check if the numberign should reset.
        /// For eample '{yy}' or '{yyyy}' wil reset the number to the initial number but '{yy}{mm}' will reset every month.
        /// </remarks>
        public string RangeFormat { get; set; }

        /// <summary>
        /// Gets or sets the format for the full invoice number. For example '{prefix}/{number}'.
        /// </summary>
        public string FullInvoiceNumberFormat { get; set; }

        /// <summary>
        /// Gets or sets the initial number to start the invoicenumber generation with when the prefix changes.
        /// </summary>
        public int InitialNumber { get; set; } = -1;
    }
}
