// <copyright file="ZipFileGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Documents.Models;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Application.Contracts.Tools;
using System.IO.Compression;

namespace NetProGroup.Trust.Application.AppServices.Tools
{
    /// <summary>
    /// Interface for zip file events.
    /// </summary>
    public class ZipFileGenerator : IZipFileGenerator
    {
        private readonly IDocumentManager _documentManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ZipFileGenerator"/> class.
        /// </summary>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        public ZipFileGenerator(
            IDocumentManager documentManager)
        {
            _documentManager = documentManager;
        }

        /// <inheritdoc/>
        public async Task<ZipFileDTO> GenerateZipFileAsync(List<Guid> documentIds)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(documentIds, nameof(documentIds));

            if (documentIds.Count == 0)
            {
                return null;
            }

            // Retrieve the selected documents.
            var documents = new List<DocumentDTO>();

            foreach (var documentId in documentIds)
            {
                var document = await _documentManager.GetDocumentAsync(documentId, true);

                if (document != null)
                {
                    documents.Add(document);
                }
            }

            // Create a temporary memory stream for the zip file
            using var memoryStream = new MemoryStream();

            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                foreach (var document in documents)
                {
                    var entry = archive.CreateEntry(document.Filename, CompressionLevel.Fastest);
                    using var entryStream = entry.Open();
                    await entryStream.WriteAsync(document.DocumentData);
                }
            }

            memoryStream.Seek(0, SeekOrigin.Begin);

            // Generate the zip file
            var response = new ZipFileDTO()
            {
                FileContent = memoryStream,
                FileName = "document",
                ContentType = "application/zip",
                Extension = ".zip"
            };

            return response;
        }

        /// <inheritdoc/>
        public async Task<ZipFileDTO> GenerateZipFileAsync(List<SubmissionDTO> submissions)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(submissions, nameof(submissions));

            // Create a temporary memory stream for the zip file
            using var memoryStream = new MemoryStream();

            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                foreach (var submission in submissions)
                {
                    if (submission.DocumentIds != null && submission.DocumentIds.Count != 0)
                    {
                        foreach (var documentId in submission.DocumentIds)
                        {
                            var document = await _documentManager.GetDocumentAsync(documentId, true);

                            if (document != null)
                            {
                                var entry = archive.CreateEntry($"{submission.Id}/{document.Filename}", CompressionLevel.Fastest);
                                using var entryStream = entry.Open();
                                await entryStream.WriteAsync(document.DocumentData);
                            }
                        }
                    }
                }
            }

            memoryStream.Seek(0, SeekOrigin.Begin);

            // Generate the zip file
            var response = new ZipFileDTO()
            {
                FileContent = memoryStream,
                FileName = "document",
                ContentType = "application/zip",
                Extension = ".zip"
            };

            return response;
        }
    }
}