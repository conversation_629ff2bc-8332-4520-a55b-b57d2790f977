﻿// <copyright file="SetModulesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a list of ModuleDTO to set for a company or jurisdiction.
    /// </summary>
    public class SetModulesDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<SetModuleDTO> Modules { get; set; } = new List<SetModuleDTO>();
    }
}
