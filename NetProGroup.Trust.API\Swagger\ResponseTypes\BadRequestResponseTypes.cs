﻿// <copyright file="BadRequestResponseTypes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Models.Exceptions;
using OneOf;

namespace NetProGroup.Trust.API.Swagger.ResponseTypes
{
    /// <summary>
    /// Represents the response types for a bad request, for Swagger documentation.
    /// </summary>
    [GenerateOneOf]
    public partial class BadRequestResponseTypes : OneOfBase<ValidationProblemDetails, APIExceptionModel>;
}