﻿// <copyright file="ListActivityLogResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Audits;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Response model for getting ActivityLog data.
    /// </summary>
    public class ListActivityLogResponse
    {
        /// <summary>
        /// Gets or sets the paged list with ActivityLogItemDTO items.
        /// </summary>
        public IPagedList<ActivityLogItemDTO> ActivityLogItems { get; set; }
    }
}
