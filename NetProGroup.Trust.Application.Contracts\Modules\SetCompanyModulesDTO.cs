﻿// <copyright file="SetCompanyModulesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a list of CompanyModuleDTO to set for a company or jurisdiction.
    /// </summary>
    public class SetCompanyModulesDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<SetCompanyModuleDTO> Modules { get; set; } = new List<SetCompanyModuleDTO>();
    }
}
