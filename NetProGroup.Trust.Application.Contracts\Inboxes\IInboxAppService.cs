﻿// <copyright file="IInboxAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Paging;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Interface for the Inboxes AppService.
    /// </summary>
    public interface IInboxAppService : IScopedService
    {
        /// <summary>
        /// Gets the info about the Inbox for the authenticated user.
        /// </summary>
        /// <returns>A <see cref="InboxInfoDTO"/> representing the asynchronous operation.</returns>
        Task<InboxInfoDTO> GetInboxInfoAsync();

        /// <summary>
        /// Gets a specific message from the inbox.
        /// </summary>
        /// <param name="messageId">Id of the message to get.</param>
        /// <returns>A <see cref="InboxMessageListItemDTO"/> representing the message.</returns>
        Task<InboxMessageDTO> GetInboxMessageAsync(Guid messageId);

        /// <summary>
        /// Gets a paged list with inbox messages.
        /// </summary>
        /// <param name="isRead">Optional flag to select only read or unread messages.</param>
        /// <param name="pageNumber">Optional pagenumber.</param>
        /// <param name="pageSize">Optional pagesize.</param>
        /// <returns>A <see cref="IPagedList{InboxMessageItemDTO}"/> representing the resulting paged list.</returns>
        Task<IPagedList<InboxMessageListItemDTO>> GetInboxMessagesAsync(bool? isRead = null, int pageNumber = 1, int pageSize = int.MaxValue);

        /// <summary>
        /// Deletes an inbox read status by its ID, thereby marking it as unread for the referenced user.
        /// If the inbox read status does not exist, the method does nothing.
        /// </summary>
        /// <param name="inboxId">The ID of the inbox read status to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteInboxReadStatusForUserAsync(Guid inboxId);

        /// <summary>
        /// Creates or updates an inbox read status, thereby marking the referenced inbox as read for the referenced user.
        /// If the inbox read status already exists, the method does nothing.
        /// </summary>
        /// <param name="inboxId">The ID of the inbox for which to create the read status.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task CreateInboxReadStatusForUserAsync(Guid inboxId);
    }
}
