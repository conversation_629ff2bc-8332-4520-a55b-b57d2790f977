﻿// <copyright file="PermissionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Security;
using NetProGroup.Trust.DataManager.Security;

namespace NetProGroup.Trust.Application.AppServices.Security
{
    /// <summary>
    /// Application service for file imports.
    /// </summary>
    public class PermissionsAppService : IPermissionsAppService
    {
        private readonly ILogger _logger;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="PermissionsAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="securityManager">Instance of the securitymanager.</param>
        public PermissionsAppService(ILogger<PermissionsAppService> logger,
                                     ISecurityManager securityManager)
        {
            _logger = logger;

            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public List<string> GetAllPermissions()
        {
            var result = new List<string>();

            result = _securityManager.GetPermissionsWithRoles().Keys.ToList();

            return result;
        }
    }
}
