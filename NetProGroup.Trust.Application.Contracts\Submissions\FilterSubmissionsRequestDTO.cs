﻿// <copyright file="FilterSubmissionsRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// DTO for the request to search for submissions.
    /// </summary>
    public class FilterSubmissionsRequestDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the module id as Guid.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities, masterclients or referral offices.
        /// </summary>
        /// <remarks>
        /// Use this searchterm to search with a single term for 'or-ring' the search.
        /// </remarks>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission which the financial period starts.
        /// </summary>
        public DateTime? FinancialPeriodStartAt { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission which the financial period ends.
        /// </summary>
        public DateTime? FinancialPeriodEndAt { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission is using the accounting records tool.
        /// </summary>
        public bool? IsUsingAccountingRecordsTool { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission has been exported or not.
        /// </summary>
        public bool? IsExported { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be deleted or not.
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        [SortableColumns(
            nameof(ListSubmissionDTO.IsUsingAccountingRecordsTool),
            nameof(ListSubmissionDTO.LegalEntityName),
            nameof(ListSubmissionDTO.LegalEntityCode),
            nameof(ListSubmissionDTO.LegalEntityVPCode),
            nameof(ListSubmissionDTO.MasterClientCode),
            nameof(ListSubmissionDTO.Status),
            nameof(ListSubmissionDTO.FinancialPeriodStartsAt),
            nameof(ListSubmissionDTO.FinancialPeriodEndsAt),
            nameof(ListSubmissionDTO.CreatedAt),
            nameof(ListSubmissionDTO.PaymentMethod),
            nameof(ListSubmissionDTO.PaymentReceivedAt),
            nameof(ListSubmissionDTO.PaymentReference))] // TODO we could also use an enum for this but for now wanted to keep the type of the property the same as the base class
        public override string SortBy { get; set; }
    }
}
