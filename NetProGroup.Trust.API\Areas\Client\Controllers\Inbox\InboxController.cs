﻿// <copyright file="InboxController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Inboxes;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;
using X.PagedList;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Inbox
{
    /// <summary>
    /// Use this controller for Inbox related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class InboxController : TrustAPIControllerBase
    {
        private readonly IInboxAppService _inboxAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="InboxController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="inboxAppService">The service for Inboxes.</param>
        public InboxController(
            ILogger<InboxController> logger,
            IInboxAppService inboxAppService)
            : base(logger)
        {
            _inboxAppService = inboxAppService;
        }

        /// <summary>
        /// Returns the info for the inbox.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/inbox.
        ///
        /// </remarks>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetInboxInfo")]
        [ProducesResponseType(typeof(InboxInfoDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetInboxInfo()
        {
            InboxInfoDTO item = null;

            var result = await ProcessRequestAsync<InboxInfoDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _inboxAppService.GetInboxInfoAsync();
                },

                createResponseModel: () => item);

            return result.AsResponse();
        }

        /// <summary>
        /// Get inbox messages with pagination.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a paginated list of messages for the given user with the specified page number and page size.
        /// Sample request:
        ///
        ///    GET /api/v1/client/inbox/messages?userId={userId}&amp;pagenumber=1&amp;pagesize=20.
        ///
        /// </remarks>
        /// <param name="isRead">Optional flag to specify whether to select only the Unread or the Read messages.</param>
        /// <param name="pageNumber">The optional pageNumber for the messages.</param>
        /// <param name="pageSize">The optional pageSize for the messages.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet("messages")]
        [SwaggerOperation(OperationId = "GetInboxMessages")]
        [ProducesResponseType(typeof(PaginatedResponse<InboxMessageListItemDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]

        public async Task<IActionResult> GetInboxMessages(
            bool? isRead = null,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },

                executeAsync: async (pagingInfo) =>
                {
                    return await _inboxAppService.GetInboxMessagesAsync(isRead, pageNumber, pageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given inbox message.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    GET /api/v1/client/inbox/messages/{messageId}.
        ///
        /// </remarks>
        /// <param name="messageId">Id of the message to get.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet("messages/{messageId}")]
        [SwaggerOperation(OperationId = "GetInboxMessage")]
        [ProducesResponseType(typeof(InboxMessageDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetInboxMessage(Guid messageId)
        {
            InboxMessageDTO item = null;

            var result = await ProcessRequestAsync<InboxMessageDTO>(

               validate: () =>
               {
                   Check.NotDefaultOrNull<Guid>(messageId, nameof(messageId));
               },

               executeAsync: async () =>
               {
                   item = await _inboxAppService.GetInboxMessageAsync(messageId);
               },

               createResponseModel: () =>
               {
                   return item;
               });

            return result.AsResponse();
        }

        /// <summary>
        /// Creates a message read status.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/client/inbox/messages/{messageId}/readstatus.
        /// </remarks>
        /// <param name="messageId">The ID of the message to create a read status for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("messages/{messageId:guid}/readstatus")]
        [SwaggerOperation(OperationId = "CreateMessageReadStatus", Summary = "Creates an inbox read status.")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateMessageReadStatus(Guid messageId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(messageId, nameof(messageId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    await _inboxAppService.CreateInboxReadStatusForUserAsync(messageId);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Deletes a message read status for the user by the message ID.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     DELETE /api/v1/client/inbox/messages/{messageId}/readstatus.
        /// </remarks>
        /// <param name="messageId">The ID of the message to delete the read status for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpDelete("messages/{messageId:guid}/readstatus")]
        [SwaggerOperation(OperationId = "DeleteInboxReadStatus", Summary = "Deletes an inbox read status by the message ID.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> DeleteInboxReadStatus(Guid messageId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(messageId, nameof(messageId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    await _inboxAppService.DeleteInboxReadStatusForUserAsync(messageId);
                });

            return result.AsNoContentResponse();
        }
    }
}
