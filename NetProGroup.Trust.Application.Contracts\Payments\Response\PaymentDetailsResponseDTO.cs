// <copyright file="PaymentDetailsResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Application.Contracts.Payments.Response
{
    /// <summary>
    /// Represents the data transfer object for a payment details response.
    /// </summary>
    public class PaymentDetailsResponseDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the payment.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the legal entity making the payment.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the currency used in the payment.
        /// </summary>
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the amount of the payment.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the status of the payment.
        /// </summary>
        public PaymentStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the details of the payment transactions.
        /// </summary>
        public IEnumerable<PaymentTransactionResponseDTO> PaymentTransactions { get; set; }
    }
}