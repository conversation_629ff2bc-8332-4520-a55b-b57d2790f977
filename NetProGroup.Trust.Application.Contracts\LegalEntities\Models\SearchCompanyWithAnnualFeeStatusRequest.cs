﻿// <copyright file="SearchCompanyWithAnnualFeeStatusRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Models
{
    /// <summary>
    /// Request model for searching companies' annual fee status.
    /// </summary>
    public class SearchCompanyWithAnnualFeeStatusRequest : PagedRequest, IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the search term.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the financial year.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to filter for paid or unpaid annual fees.
        /// </summary>
        public bool IsPaid { get; set; }

        /// <inheritdoc />
        public List<Guid> AuthorizedJurisdictionIDs { get; set; }
    }
}
