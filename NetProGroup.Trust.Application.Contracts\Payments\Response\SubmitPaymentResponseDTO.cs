// <copyright file="SubmitPaymentResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Payment submission response model.
    /// </summary>
    public class SubmitPaymentResponseDTO
    {
        /// <summary>
        /// Gets or sets the transactionid for the transaction in our database.
        /// </summary>
        public Guid TransactionId { get; set; }

        /// <summary>
        /// Gets or sets the transactionid as returned by the payment provider.
        /// </summary>
        public string ProviderTransactionId { get; set; }

        /// <summary>
        /// Gets or sets the result code as received from the provider.
        /// This varies by provider but indicates 'approved', 'declined' etc.
        /// </summary>
        public int Result { get; set; }

        /// <summary>
        /// Gets or sets the result code in text as received from the provider.
        /// This varies by provider but indicates 'approved', 'declined' etc.
        /// </summary>
        public string ResultText { get; set; }

        /// <summary>
        /// Gets or sets the numeric value for the result.
        /// This might map to a table with results depending on the provider.
        /// </summary>
        public int ResultNumber { get; set; }
    }
}