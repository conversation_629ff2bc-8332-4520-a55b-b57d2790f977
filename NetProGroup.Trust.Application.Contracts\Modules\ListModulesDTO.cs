﻿// <copyright file="ListModulesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a list of ModuleDTO.
    /// </summary>
    public class ListModulesDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<ModuleDTO> Modules { get; set; } = new List<ModuleDTO>();
    }
}
