﻿// <copyright file="IActivityLogsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Shared;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Audits
{
    /// <summary>
    /// Interface for AuditsAppService.
    /// </summary>
    public interface IActivityLogsAppService : IScopedService
    {
        /// <summary>
        /// Adds an entry in the activityLog.
        /// </summary>
        /// <param name="model">The model for adding the ActivityLog.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task AddActivityLogAsync(AddActivityLogDTO model);

        /// <summary>
        /// Gets all ActivityLogs created in the given year + week.
        /// </summary>
        /// <param name="period">The period to get the data for.</param>
        /// <param name="entityId">Optional. The ID of the entity to filter activity logs for.</param>
        /// <param name="pageNumber">Number of the page to get.</param>
        /// <param name="pageSize">Max number of items on the page.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation holding a paged list of <see cref="ActivityLogItemDTO"/>.</returns>
        Task<IPagedList<ActivityLogItemDTO>> GetActivityLogAsync(Period period, Guid? entityId = null, int pageNumber = 1, int pageSize = 20);

        /// <summary>
        /// Gets a specific ActivityLog.
        /// </summary>
        /// <param name="activityLogId">The given id for the ActivityLog.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation holding a single <see cref="ActivityLogItemDTO"/>.</returns>
        Task<ActivityLogItemDTO> GetActivityLogAsync(Guid activityLogId);
    }
}
