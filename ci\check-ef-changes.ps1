param(
    [Parameter(Mandatory = $true)]
    [string]$BuildSourcesDirectory,
    [Parameter(Mandatory = $false)]
    [string]$BuildConfiguration = "Release",
    [Parameter(Mandatory = $false)]
    [string]$NoBuild = $true
)

$projectPath = "$BuildSourcesDirectory\NetProGroup.Trust.API"
$migrationsProjectPath = "$BuildSourcesDirectory\NetProGroup.Trust.Repository"

Write-Host "Checking for pending Entity Framework model changes in $projectPath and $migrationsProjectPath"

if ($NoBuild -eq $true) {
    dotnet ef migrations has-pending-model-changes --startup-project $projectPath --project $migrationsProjectPath --configuration $BuildConfiguration --no-build
}
else {
    dotnet ef migrations has-pending-model-changes --startup-project $projectPath --project $migrationsProjectPath --configuration $BuildConfiguration
}
