﻿// <copyright file="ILegalEntitiesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.Domain.Shared.Enums;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities
{
    /// <summary>
    /// Interface for the LegalEntities AppService.
    /// </summary>
    public interface ILegalEntitiesAppService : IScopedService
    {
        /// <summary>
        /// Gets the paged list of companies.
        /// </summary>
        /// <param name="masterClientId">Optional id of the master client to get the companies for.</param>
        /// <param name="searchTerm">The search term to filter the companies.</param>
        /// <param name="active">The active status of the companies.</param>
        /// <param name="onboardingStatuses">Optional onboarding status to filter companies.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <param name="sortBy">The field to sort on.</param>
        /// <param name="sortOrder">The order to sort on.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the companies as a paged list.</returns>
        Task<IPagedList<CompanyDTO>> ListCompaniesAsync(Guid? masterClientId, string searchTerm, bool? active, List<OnboardingStatus> onboardingStatuses, int pageNumber, int pageSize, string sortBy, string sortOrder);

        /// <summary>
        /// Gets a paged list with BeneficialOwners.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="includeMetaData">Flag indicating whether to include meta data.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the beneficial owners as a paged list.</returns>
        Task<IPagedList<BeneficialOwnerDTO>> ListBeneficialOwnersAsync(Guid legalEntityId, bool includeMetaData, int pageNumber, int pageSize);

        /// <summary>
        /// Gets a paged list with Directors.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="includeMetaData">Flag indicating whether to include meta data.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the directors as a paged list.</returns>
        Task<IPagedList<DirectorDTO>> ListDirectorsAsync(Guid legalEntityId, bool includeMetaData, int pageNumber, int pageSize);

        /// <summary>
        /// Gets a paged list with Shareholders.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the Shareholders as a paged list.</returns>
        Task<IPagedList<ShareholderDTO>> ListShareholdersAsync(Guid legalEntityId, int pageNumber, int pageSize);

        /// <summary>
        /// Request for assistance..
        /// </summary>
        /// <param name="requestAssistance">The model holding the parameters for the assistance request.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance);

        /// <summary>
        /// Gets a paged list of companies with their annual fee status.
        /// </summary>
        /// <param name="financialYear">The financial year to get the status for.</param>
        /// <param name="isPaid">Whether the annual fee is paid or not.</param>
        /// <param name="searchTerm">The search term to filter the companies.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the company annual fee statuses as a paged list.</returns>
        Task<IPagedList<CompanyWithAnnualFeeStatusSearchResultDTO>> SearchCompaniesWithAnnualFeeStatusAsync(int financialYear, bool isPaid, string searchTerm, int pageNumber, int pageSize);

        /// <summary>
        /// Updates the annual fee status for multiple companies.
        /// </summary>
        /// <param name="legalEntityIds">The list of legal entity IDs to update.</param>
        /// <param name="financialYear">The financial year for which to update the status.</param>
        /// <param name="isPaid">Whether the annual fee is paid or not.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task UpdateCompanyAnnualFeeStatusAsync(List<Guid> legalEntityIds, int financialYear, bool isPaid);

        /// <summary>
        /// Sets the annual fee status for a single legal entity.
        /// </summary>
        /// <param name="model">The DTO with the finacial years and their paid status.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task<CompanyAnnualFeesDTO> SetCompanyAnnualFeeStatusAsync(SetCompanyAnnualFeesDTO model);

        /// <summary>
        /// Gets the annual fee status for a single legal entity.
        /// </summary>
        /// <param name="legalEntityId">The id of the company to get the annual fee status for.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task<CompanyAnnualFeesDTO> GetCompanyAnnualFeeStatusAsync(Guid legalEntityId);

        /// <summary>
        /// Retrieves a company by its ID.
        /// </summary>
        /// <param name="companyId">The ID of the company to retrieve.</param>
        /// <returns>A <see cref="Task{CompanyDTO}"/> representing the asynchronous operation.</returns>
        Task<CompanyDTO> GetCompanyByIdAsync(Guid companyId);

        /// <summary>
        /// Approves a company's registration.
        /// </summary>
        /// <param name="companyId">The ID of the company to approve.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task ApproveCompanyAsync(Guid companyId);

        /// <summary>
        /// Declines a company's registration.
        /// </summary>
        /// <param name="companyId">The ID of the company to decline.</param>
        /// <param name="declineReason">The reason for declining the registration.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeclineCompanyAsync(Guid companyId, string declineReason);
    }
}
