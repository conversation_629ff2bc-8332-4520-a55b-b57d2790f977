// <copyright file="IBasicFinancialReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.AppServices.Reports.Panama.BasicFinancialReport
{
    /// <summary>
    /// Interface for basic financial report service.
    /// </summary>
    public interface IBasicFinancialReportService : ITransientService, IReportService;
}