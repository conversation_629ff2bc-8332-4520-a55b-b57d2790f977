// <copyright file="DocumentsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Documents.Models;
using NetProGroup.Trust.Application.Contracts.Documents;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using System.Diagnostics.CodeAnalysis;

namespace NetProGroup.Trust.Application.AppServices.Documents
{
    /// <summary>
    /// Application service for documents.
    /// </summary>
    public class DocumentsAppService : IDocumentsAppService
    {
        private readonly IDocumentManager _documentManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="DocumentsAppService"/> class.
        /// </summary>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        /// <param name="securityManager">An instance of ISecurityManager.</param>
        public DocumentsAppService(
            IDocumentManager documentManager,
            ISecurityManager securityManager)
        {
            _documentManager = documentManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<Guid> CreateDocumentAsync([NotNull] CreateDocumentDTO data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            await _securityManager.RequireUserAsync();

            // Create the document and return the created id.
            return await _documentManager.CreateDocumentAsync(
                (int)data.Type,
                string.IsNullOrEmpty(data.Description) ? data.File.Name : data.Description,
                data.File,
                "default",
                true,
                DateTime.UtcNow.AddHours(24));
        }

        /// <inheritdoc/>
        public async Task<DocumentDTO> GetDocumentByIdAsync(Guid documentId, bool includeData)
        {
            await _securityManager.RequireUserAsync();

            // Check the document entity
            var documentCheck = await _documentManager.CheckDocumentByIdAsync(documentId);
            return await _documentManager.GetDocumentAsync(documentId, includeData);
        }

        /// <inheritdoc/>
        public async Task<List<DocumentDTO>> GetDocumentsByIdAsync([NotNull] List<Guid> documentIds, bool includeData)
        {
            ArgumentNullException.ThrowIfNull(documentIds, nameof(documentIds));

            await _securityManager.RequireUserAsync();

            var documents = new List<DocumentDTO>();

            foreach (var documentId in documentIds)
            {
                documents.Add(await GetDocumentByIdAsync(documentId, includeData));
            }

            return documents;
        }
    }
}
