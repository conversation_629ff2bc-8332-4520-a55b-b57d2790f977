// <copyright file="CreateDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Http;
using NetProGroup.Trust.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Documents
{
    /// <summary>
    /// Represents the necessary data to create a document.
    /// </summary>
    public class CreateDocumentDTO
    {
        /// <summary>
        /// Gets or sets the file.
        /// </summary>
        [Required]
        public IFormFile File { get; set; }

        /// <summary>
        /// Gets or sets the documet type as <see cref="DocumentType"/>.
        /// </summary>
        public DocumentType Type { get; set; }

        /// <summary>
        /// Gets or sets the documet description as string.
        /// </summary>
        public string Description { get; set; }
    }
}