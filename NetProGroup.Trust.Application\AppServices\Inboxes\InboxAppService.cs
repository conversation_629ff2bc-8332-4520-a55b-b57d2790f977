﻿// <copyright file="InboxAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Inboxes;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.DataManager.Security;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Inboxes
{
    /// <summary>
    /// Implementation of <see cref="IInboxAppService"/> for managing inboxes.
    /// </summary>
    public class InboxAppService : IInboxAppService
    {
        private readonly IInboxDataManager _dataManager;
        private readonly IWorkContext _workContext;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="InboxAppService"/> class.
        /// </summary>
        /// <param name="workContext">The context for the current request.</param>
        /// <param name="dataManager">The data manager for inboxes.</param>
        /// <param name="securityManager">The security manager.</param>
        public InboxAppService(IWorkContext workContext,
            IInboxDataManager dataManager,
            ISecurityManager securityManager)
        {
            _workContext = workContext;
            _dataManager = dataManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc />
        public async Task CreateInboxReadStatusForUserAsync(Guid inboxId)
        {
            Check.NotDefaultOrNull<Guid>(inboxId, nameof(inboxId));

            await _securityManager.RequireClientUserAsync();
            var userId = _workContext.IdentityUserId.Value;

            await _dataManager.CreateInboxReadStatusIfNotExistsAsync(inboxId, userId, DateTime.UtcNow);
        }

        /// <inheritdoc />
        public async Task DeleteInboxReadStatusForUserAsync(Guid inboxId)
        {
            Check.NotDefaultOrNull<Guid>(inboxId, nameof(inboxId));

            await _securityManager.RequireClientUserAsync();
            var userId = _workContext.IdentityUserId.Value;

            await _dataManager.DeleteInboxReadStatusIfExistsAsync(inboxId, userId);
        }

        /// <inheritdoc/>
        public async Task<InboxInfoDTO> GetInboxInfoAsync()
        {
            await _securityManager.RequireUserAsync();

            // Retrieve the autheticated user id
            var userId = _workContext.IdentityUserId.Value;

            return await _dataManager.GetInboxInfoAsync(userId);
        }

        /// <inheritdoc/>
        public async Task<InboxMessageDTO> GetInboxMessageAsync(Guid messageId)
        {
            Check.NotDefaultOrNull<Guid>(messageId, nameof(messageId));
            await _securityManager.RequireUserAsync();

            return await _dataManager.GetInboxMessageAsync(messageId);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<InboxMessageListItemDTO>> GetInboxMessagesAsync(bool? isRead = null, int pageNumber = 1, int pageSize = int.MaxValue)
        {
            await _securityManager.RequireUserAsync();

            // Retrieve the autheticated user id
            var userId = _workContext.IdentityUserId.Value;

            return await _dataManager.GetInboxMessagesAsync(userId, isRead, pageNumber, pageSize);
        }
    }
}
