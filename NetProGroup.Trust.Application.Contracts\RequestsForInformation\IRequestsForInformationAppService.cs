// <copyright file="IRequestsForInformationAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Interface for request for information events.
    /// </summary>
    public interface IRequestsForInformationAppService : IScopedService
    {
        /// <summary>
        /// Searches for submissions with created RFI requests for a given jurisdiction/legalentity/module.
        /// </summary>
        /// <param name="request">The necessary data to filter submissions with RFI requests created.</param>
        /// <returns>A paginated list of submissions with RFI requestes created.</returns>
        Task<IPagedList<ListSubmissionRFIDTO>> ListSubmissionRFIAsync(ListSubmissionsRFIRequestDTO request);

        /// <summary>
        /// Creates a request for information for a submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to create the request for information.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task<Guid> CreateRequestForInformationAsync(Guid submissionId, CreateRFIDTO data);

        /// <summary>
        /// Retrieve the RFI a request for information for a submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task<SubmissionRFIDetailsDTO> GetRFISubmissionDetailsAsync(Guid submissionId);

        /// <summary>
        /// Creates a request for information document entity by a management user.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="data">The necessary dataset used to create a request for information document.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CreateRFIDocumentByManagementAsync(Guid requestForInformationId, CreateRFIDocumentDTO data);

        /// <summary>
        /// Mark a request for information entity as Cancelled by a management user.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="reason">The cancellation reason.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CancelRequestForInformationAsync(Guid requestForInformationId, string reason);

        /// <summary>
        /// Creates a request for information document entity by a client user.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="data">The necessary dataset used to complete a request for information document.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CreateRequestForInformationDocumentByClientAsync(Guid requestForInformationId, CreateRFIDocumentDTO data);

        /// <summary>
        /// Completes a request for information for a submission (Client).
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="data">The data used to complete the request for information.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task CompleteRequestForInformationAsync(Guid requestForInformationId, CompleteRequestForInformationDTO data);
    }
}