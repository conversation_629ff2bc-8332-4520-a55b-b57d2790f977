﻿// <copyright file="ErrorEnumExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;

namespace NetProGroup.Trust.Application.Contracts.Errors
{
    /// <summary>
    /// Extension methods for ErrorEnums.
    /// </summary>
    public static class ErrorEnumExtensions
    {
        /// <summary>
        /// Use this extension to create an ErrorCode from an ApplicationError.
        /// </summary>
        /// <param name="value">The enumerated value fot eh error code.</param>
        /// <returns>The resulting ErrorCode.</returns>
        public static ErrorCode ToErrorCode(this ApplicationErrors value)
        {
            return new ErrorCode { Code = (int)value, Error = value.ToString() };
        }
    }
}
