﻿// <copyright file="AttributeCollectionRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
    public class AttributeCollectionRequest
    {
        private static JsonSerializerOptions _jsonSerializerOptions = new JsonSerializerOptions { WriteIndented = true };

        public AttributeCollectionRequest()
        {
            data = new AttributeCollectionRequest_Data();
        }

        [JsonPropertyName("data")]
        public AttributeCollectionRequest_Data data { get; set; }

        public override string ToString()
        {
            return JsonSerializer.Serialize(this, _jsonSerializerOptions);
        }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionRequest_Data : AllRequestData
#pragma warning restore SA1402 // File may only contain a single type
    {
        public UserSignUpInfo userSignUpInfo { get; set; }
    }
#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}