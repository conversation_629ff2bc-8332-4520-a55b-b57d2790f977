// <copyright file="AcceptTermsConditionsResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.TermsConditions
{
    /// <summary>
    /// DTO for Terms and Conditions acceptance result.
    /// </summary>
    public class AcceptTermsConditionsResultDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the acceptance was successful.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets when Terms and Conditions were accepted.
        /// </summary>
        public DateTime AcceptedAt { get; set; }
    }
}