// <copyright file="JurisdictionSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the jurisdictions.
    /// </summary>
    public static partial class JurisdictionSeeder
    {
        /// <summary>
        /// Defines the seeding.
        /// </summary>
        /// <param name="modelBuilder">Instance of ModelBuilder.</param>
        public static void SeedJurisdictions(this ModelBuilder modelBuilder)
        {
            ArgumentNullException.ThrowIfNull(modelBuilder);

            var date = new DateTime(2024, 1, 1);

            modelBuilder
                .Entity<Jurisdiction>()
                .HasData(
                    new Jurisdiction(
                        new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9"))
                    {
                        Name = "Nevis",
                        Code = "Nevis",
                        CreatedAt = date,
                        UpdatedAt = date
                    },
                    new Jurisdiction(
                        new Guid("2CA76629-B3E6-409D-B788-C63C802A4D4F"))
                    {
                        Name = "Bahamas",
                        Code = JurisdictionCodes.Bahamas,
                        CreatedAt = date,
                        UpdatedAt = date
                    },
                    new Jurisdiction(
                        new Guid("AB54F8A6-DC29-4DD2-A39C-660BB980F789"))
                    {
                        Name = "BVI",
                        Code = JurisdictionCodes.BritishVirginIslands,
                        CreatedAt = date,
                        UpdatedAt = date
                    },
                    new Jurisdiction(
                        new Guid("298175E2-A24F-4A7E-B7F3-A83E0447908C"))
                    {
                        Name = "Panama",
                        Code = JurisdictionCodes.Panama,
                        CreatedAt = date,
                        UpdatedAt = date
                    });
        }
    }
}
