﻿// <copyright file="ListDirectorsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors
{
    /// <summary>
    /// Represents a list of ListDirectorDTO.
    /// </summary>
    public class ListDirectorsDTO
    {
        /// <summary>
        /// Gets or sets the collection of modules.
        /// </summary>
        public IReadOnlyCollection<DirectorDTO> Directors { get; set; } = new List<DirectorDTO>();
    }
}
