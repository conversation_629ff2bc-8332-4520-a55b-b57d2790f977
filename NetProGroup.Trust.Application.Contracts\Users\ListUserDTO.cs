﻿// <copyright file="ListUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// Represents a user for a list.
    /// </summary>
    public class ListUserDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the firstname of the user.
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// Gets or sets the lastname of the user.
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// Gets or sets the displayname for the user.
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the emial address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is registered (objectid from ExternalId known).
        /// </summary>
        public bool IsRegistered { get; set; }

        /// <summary>
        /// Gets or sets the details about invitation for the user.
        /// </summary>
        public UserInvitationDetailsDTO InvitationDetails { get; set; }
    }
}
