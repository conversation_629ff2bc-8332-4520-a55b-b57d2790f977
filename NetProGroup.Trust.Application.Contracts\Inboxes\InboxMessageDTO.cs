// <copyright file="InboxMessageDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Represents a message from the inbox.
    /// </summary>
    public class InboxMessageDTO : InboxMessageListItemDTO
    {
        /// <summary>
        /// Gets or sets the body of the message.
        /// </summary>
        public string Body { get; set; }

        /// <summary>
        /// Gets the attachments of the inbox message.
        /// </summary>
        public ICollection<InboxAttachmentDTO> InboxAttachments { get; } = new List<InboxAttachmentDTO>();

        /// <summary>
        /// Gets or sets the url attachments of the inbox message.
        /// </summary>
        public List<string> UrlAttachments { get; set; }
    }
}
