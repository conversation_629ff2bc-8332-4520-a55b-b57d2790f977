// <copyright file="IReportAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Report
{
    /// <summary>
    /// Interface for report app service.
    /// </summary>
    public interface IReportAppService : IScopedService
    {
        /// <summary>
        /// Downloads a financial report.
        /// </summary>
        /// <param name="reportId">The id of the report to download.</param>
        /// <returns>The financial report download response.</returns>
        Task<ReportDownloadResponseDTO> DownloadReportAsync(Guid reportId);

        /// <summary>
        /// Gets a paginated list of reports by specified types.
        /// </summary>
        /// <param name="request">The request containing the parameters for the search.</param>
        /// <returns>The list of reports matching the criteria.</returns>
        Task<IPagedList<ReportDTO>> GetReportsByTypeAsync(ReportRequestDTO request);
    }
}