﻿// <copyright file="CurrenciesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.Currencies;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Currencies;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.Application.Currencies
{
    /// <summary>
    /// Implementation of <see cref="ICurrenciesAppService"/> for managing currencies.
    /// </summary>
    public class CurrenciesAppService : ICurrenciesAppService
    {
        private readonly ILogger<CurrenciesAppService> _logger;
        private readonly IUserManager _userManager;
        private readonly ICurrenciesRepository _currenciesRepository;
        private readonly IMapper _mapper;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="CurrenciesAppService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="userManager">The user manager instance.</param>
        /// <param name="currenciesRepository">The repository for currencies.</param>
        /// <param name="mapper">The AutoMapper instance for mapping entities to DTOs.</param>
        /// <param name="securityManager">The security manager.</param>
        public CurrenciesAppService(
            ILogger<CurrenciesAppService> logger,
            IUserManager userManager,
            ICurrenciesRepository currenciesRepository,
            IMapper mapper,
            ISecurityManager securityManager)
        {
            _logger = logger;
            _userManager = userManager;
            _currenciesRepository = currenciesRepository;
            _mapper = mapper;
            _securityManager = securityManager;
        }

        /// <summary>
        /// Retrieves all currencies with pagination.
        /// </summary>
        /// <param name="name">The name of the currency to filter by. Optional.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paged list of currencies.</returns>
        public async Task<IPagedList<CurrencyDTO>> GetAllCurrenciesAsync(string name, int pageNumber, int pageSize)
        {
            await _securityManager.RequireManagementUserAsync();

            Expression<Func<Currency, bool>> filterCondition = x => true;

            if (!string.IsNullOrEmpty(name))
            {
                filterCondition = x => x.Name.Contains(name);
            }

            // Retrieve currencies with pagination using the filter condition
            var currencies = await _currenciesRepository
                .FindByConditionAsPagedListAsync(filterCondition, pageNumber, pageSize);

            // Map domain entities to DTOs
            var currencyDTOs = _mapper.Map<IEnumerable<CurrencyDTO>>(currencies);

            // Return as paged list
            return new StaticPagedList<CurrencyDTO>(currencyDTOs, currencies.GetMetaData());
        }

        /// <summary>
        /// Creates or updates a currency.
        /// </summary>
        /// <param name="model">The data transfer object containing the currency details.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task CreateCurrencyAsync(CreateCurrencyDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementUserAsync();

            // Create a new currency
            Guid currencyId = Guid.NewGuid();

            var newCurrency = new Currency(currencyId, model.Name, model.Code, model.Symbol);

            await _currenciesRepository.InsertAsync(newCurrency, true);
        }

        /// <summary>
        /// Deletes a currency by its ID.
        /// </summary>
        /// <param name="currencyId">The ID of the currency to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task DeleteCurrencyAsync(Guid currencyId)
        {
            await _securityManager.RequireManagementUserAsync();

            // Check if currency exists
            var currency = await _currenciesRepository.CheckCurrencyByIdAsync(currencyId);

            // Delete currency
            await _currenciesRepository.DeleteAsync(currency, true);
        }
    }
}
