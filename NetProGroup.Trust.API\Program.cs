// <copyright file="Program.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights.Extensibility.Implementation;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.API.Middleware;
using NetProGroup.Trust.API.Swagger;
using NetProGroup.Trust.Application;
using NetProGroup.Trust.DataManager;
using NetProGroup.Trust.DataMigration;
using NetProGroup.Trust.Domain.Shared.Constants;
using NetProGroup.Trust.Domain.Shared.Utilities;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API
{
    /// <summary>
    /// Program.
    /// </summary>
    public static class Program
    {
        /// <summary>
        /// The main entry point.
        /// </summary>
        /// <param name="args">Startup arguments.</param>
        public static void Main(string[] args)
        {
            TelemetryDebugWriter.IsTracingDisabled = true;
            var builder = WebApplication.CreateBuilder(args);

            // Application Insights.
            builder.Services.AddApplicationInsightsTelemetry(options =>
            {
                options.EnableDebugLogger = false;
            });

            // Add services to the container.
            builder.Services.AddControllers()
                   .AddJsonOptions(options =>
                   {
                       options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                       options.JsonSerializerOptions.Converters.Add(new DateTimeConverter(GeneralConsts.DateTimeZoneFormat));
                   });

            // Swagger Gen
            builder.ConfigureSwaggerGen();

            // Setup versioning
            builder.SetupVersioning();
            builder.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();

            // Setup defaults
            builder.SetupDefaults();

            // Setup DbContext
            builder.SetupDbContext();

            // Setup NetPro framework
            builder.ConfigureNetProFramework();

            // Setup scheduling of cron jobs
            builder.SetupScheduling();

            // Setup data migration
            builder.SetupDataMigration();

            // Setup startup service
            builder.SetupStartupTasks();

            // Setup health checks
            builder.SetupHealthChecks();

            Log.Logger.Information(new string('=', 80));
            Log.Logger.Information("NetProGroup Trust API setup successful!");
            Log.Logger.Information("API starting for environment '{Environment}'", Globals.Environment.ToString());

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                Log.Logger.Information("Start migration");
                app.Services.Migrate();

                Log.Logger.Information("Configure SwaggerUI");
                app.ConfigureSwaggerUI();
            }
            else if (builder.Configuration.GetValue<bool>("Swagger", false))
            {
                app.ConfigureSwaggerUI();
            }

#if DEBUG
            app.ClearLocks();
#endif
            app.UseCors(policy =>
                policy
                    .WithOrigins("*")
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .WithExposedHeaders("X-Pagination", "X-UserObjectId"));

            app.UseHealthChecks("/hc");

            app.UseHttpsRedirection();

            bool showPii = app.Configuration.GetValue<bool>("ShowPII");

            if (showPii)
            {
                Log.Logger.Information("Enabling ShowPII");

                Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
                Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;

                // Add a middleware to log the request headers
                app.UseMiddleware<RequestHeadersMiddleware>();
            }

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseWorkContext();

            app.MapControllers();

#pragma warning disable CA1031 // Do not catch general exception types
            try
            {
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "app.Run() failed");
            }
#pragma warning restore CA1031 // Do not catch general exception types
        }
    }
}
