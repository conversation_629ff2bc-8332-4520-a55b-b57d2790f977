// <copyright file="MarkSubmissionsAsPaidRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// DTO for updating submission payment status.
    /// </summary>
    public class MarkSubmissionsAsPaidRequestDTO
    {
        /// <summary>
        /// Gets or sets the IDs of the submissions to update.
        /// </summary>
        public IEnumerable<Guid> SubmissionIds { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submissions should be marked as paid.
        /// </summary>
        public bool IsPaid { get; set; }
    }
}
