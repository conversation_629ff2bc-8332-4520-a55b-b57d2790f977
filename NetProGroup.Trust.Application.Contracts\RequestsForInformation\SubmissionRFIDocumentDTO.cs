// <copyright file="SubmissionRFIDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Documents.Models;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represents a request for information document entity.
    /// </summary>
    public class SubmissionRFIDocumentDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the document id as Guid.
        /// </summary>
        public Guid DocumentId { get; set; }

        /// <summary>
        /// Gets or sets the document entity.
        /// </summary>
        public virtual DocumentDTO Document { get; set; }

        /// <summary>
        /// Gets or sets the request for information id as Guid.
        /// </summary>
        public Guid RequestForInformationId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the rfi document was created by a management user or not.
        /// </summary>
        public bool CreatedByManagement { get; set; }
    }
}