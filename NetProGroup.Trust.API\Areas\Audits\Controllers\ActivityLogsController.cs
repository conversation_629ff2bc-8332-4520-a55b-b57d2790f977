﻿// <copyright file="ActivityLogsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Audits.Controllers
{
    /// <summary>
    /// Controller for the 'activitylog' endpoints.
    /// </summary>
    [ApiController]
    [Area("Audits")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class ActivityLogsController : TrustAPIControllerBase
    {
        private readonly IActivityLogsAppService _auditsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActivityLogsController"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="auditsAppService">Implementation for Audits.</param>
        public ActivityLogsController(
            ILogger<ActivityLogsController> logger,
            IActivityLogsAppService auditsAppService) : base(logger)
        {
            _auditsAppService = auditsAppService;
        }

        /// <summary>
        /// Endpoint for adding an ActivityLog to the database.
        /// </summary>
        /// <remarks>
        /// The Activity log will be assigned to the current user.
        ///
        /// Sample request:
        ///
        ///     POST /api/audits/activitylogs.
        ///
        /// </remarks>
        /// <param name="model">The model as AddActivityLogDTO.</param>
        /// <returns>ActionResult.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "AddActivityLog", Summary = "Adds an activitylog entry in the database")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> AddActivityLog(AddActivityLogDTO model)
        {
            var result = await ProcessRequestAsync<object>(

                validateAsync: async () =>
                {
                    ValidateWorkContextUserId();
                    Check.NotNull(model, nameof(model));

                    await Task.CompletedTask;
                },

                executeAsync: async () =>
                {
                    await _auditsAppService.AddActivityLogAsync(model);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Returns a paginated list of activitylogs.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/audits/activitylogs?entityId=5701384D-0411-49E6-8378-D6263AAD497B&amp;fromdate=2024-07-01&amp;toDate=2024-10-01&amp;pagenumber=1.
        ///
        /// </remarks>
        /// <param name="fromDate">The optional start date for the period to look for.</param>
        /// <param name="toDate">The optional end date for the period to look for.</param>
        /// <param name="entityId">Optional. The ID of the entity to filter activity logs for.</param>
        /// <param name="pageNumber">Optional. Page number for pagination (default is 1).</param>
        /// <param name="pageSize">Optional. Number of records per page (default is 20).</param>
        /// <returns>A <see cref="Task{IActionResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "ListActivityLogs")]
        [ProducesResponseType(typeof(PaginatedResponse<ActivityLogItemDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListActivityLogs(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            Guid? entityId = null,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),

                validate: () =>
                {
                },

                executeAsync: async (pagingInfo) =>
                {
                    var period = new Period { StartDate = fromDate ?? DateTime.MinValue, EndDate = toDate ?? DateTime.MaxValue };
                    return await _auditsAppService.GetActivityLogAsync(period, entityId, pagingInfo.PageNumber, pagingInfo.PageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Returns a specific activitylog.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/audits/activitylogs/{activityLogId}.
        ///
        /// </remarks>
        /// <param name="activityLogId">Id of the ActivityLog to get.</param>
        /// <returns>A <see cref="Task{IActionResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet("{activityLogId}")]
        [SwaggerOperation(OperationId = "GetActivityLog")]
        [ProducesResponseType(typeof(ActivityLogItemDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetActivityLog(Guid activityLogId)
        {
            ActivityLogItemDTO item = null;

            var result = await ProcessRequestAsync(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(activityLogId, nameof(activityLogId));
                },

                executeAsync: async () =>
                {
                    item = await _auditsAppService.GetActivityLogAsync(activityLogId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
