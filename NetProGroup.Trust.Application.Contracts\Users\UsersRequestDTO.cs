// <copyright file="UsersRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// Request for getting users.
    /// </summary>
    public class UsersRequestDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the info for filtering.
        /// </summary>
        public string Filter { get; set; }

        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        [SortableColumns(
            nameof(ListApplicationUsersDTO.Email),
            nameof(ListApplicationUsersDTO.IsBlocked),
            nameof(ListApplicationUsersDTO.PrimaryRoleLabel))]
        public override string SortBy { get; set; }
    }
}