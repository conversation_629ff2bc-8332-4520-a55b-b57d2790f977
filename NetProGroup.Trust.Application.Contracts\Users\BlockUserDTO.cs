// <copyright file="BlockUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// Data transfer object (DTO) for blocking or unblocking a user.
    /// </summary>
    public class BlockUserDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the user should be blocked or unblocked.
        /// </summary>
        [Required]
        public bool IsBlocked { get; set; }
    }
}