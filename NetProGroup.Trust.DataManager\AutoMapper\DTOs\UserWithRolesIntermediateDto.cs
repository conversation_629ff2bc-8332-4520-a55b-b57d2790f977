﻿// <copyright file="UserWithRolesIntermediateDto.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Identity.EFModels;

namespace NetProGroup.Trust.DataManager.AutoMapper.DTOs
{
    /// <summary>
    /// Intermediate class to support Automapper Projection.
    /// This is needed because there is no EF navigation property to the roles where we can get the role names.
    /// So we first join the user and its roles in this class, and then we can project to the DTOs we need.
    /// </summary>
    internal sealed class UserWithRolesIntermediateDto
    {
        /// <summary>
        /// Gets or sets the user.
        /// </summary>
        public ApplicationUser User { get; set; }

        /// <summary>
        /// Gets or sets the roles of the user.
        /// </summary>
        public IEnumerable<string> RoleNames { get; set; }
    }
}