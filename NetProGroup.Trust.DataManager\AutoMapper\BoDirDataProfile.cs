﻿// <copyright file="BoDirDataProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Repository.Builders;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The Beneficial Owners/ Directors profile for AutoMapper.
    /// </summary>
    public class BoDirDataProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirDataProfile "/> class.
        /// </summary>
        public BoDirDataProfile()
        {
            // LegalEntity to BoDirItemDTO mapping
            CreateMap<LegalEntity, BoDirItemDTO>()
                .ForMember(dest => dest.DirectorName,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.Position,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.VPEntityNumber,
                    opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.EntityPortalCode,
                    opt => opt.MapFrom(src => src.LegacyCode))
                .ForMember(dest => dest.ConfirmedDate,
                    opt => opt.MapFrom(src => (DateTime?)null))
                .ForMember(dest => dest.Status,
                    opt => opt.MapFrom(src => LegalEntityRelationStatus.Initial))
                .ForMember(dest => dest.RequestUpdateDate,
                    opt => opt.MapFrom(src => (DateTime?)null))
                .ForMember(dest => dest.DirectorType,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.OfficerType,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.DirectorVPCode,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.Specifics,
                    opt => opt.MapFrom(src => "MISSING BO/DIR"))
                .ForMember(dest => dest.HasMissingInformation,
                    opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.HasBoDirInformation,
                    opt => opt.MapFrom(BoDirFilterExpressionBuilder.EntityHasBoDirInfo()))
                .ForMember(dest => dest.IsIndividual,
                    opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.DateOfBirth,
                    opt => opt.MapFrom(src => (DateTime?)null))
                .ForMember(dest => dest.PlaceOfBirth,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.CountryOfBirth,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.Nationality,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.ResidentialAddress,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.IncorporationDate,
                    opt => opt.MapFrom(src => (DateTime?)null))
                .ForMember(dest => dest.IncorporationPlace,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.CountryOfFormation,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.NameOfRegulator,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.JurisdictionOfRegulator,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.SovereignState,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.StockExchangeCode,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.StockExchangeName,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.RelationType,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.AppointmentDate,
                    opt => opt.MapFrom(src => (DateTime?)null))
                .ForMember(dest => dest.ServiceAddress,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.Address,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.Country,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.TIN,
                    opt => opt.MapFrom(src => string.Empty));

            CreateMap<Director, BoDirItemDTO>()
                .ForMember(dest => dest.DirectorName,
                    opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Position,
                    opt => opt.MapFrom(src => BoDirPosition.Director.ToString()))
                .ForMember(dest => dest.ProductionOffice,
                    opt => opt.MapFrom(src => src.LegalEntity.ProductionOffice))
                .ForMember(dest => dest.ReferralOffice,
                    opt => opt.MapFrom(src => src.LegalEntity.ReferralOffice))
                .ForMember(dest => dest.VPEntityNumber,
                    opt => opt.MapFrom(src => src.LegalEntity.Code))
                .ForMember(dest => dest.EntityPortalCode,
                    opt => opt.MapFrom(src => src.LegalEntity.LegacyCode))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))
                .ForMember(dest => dest.ConfirmedDate,
                    opt => opt.MapFrom(src => src.DirectorHistories.OrderBy(o => o.CreatedAt).Where(o => o.ConfirmedAt != null).Select(o => o.ConfirmedAt).LastOrDefault()))
                .ForMember(dest => dest.Status,
                    opt => opt.MapFrom(src => src.DirectorHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status).LastOrDefault()))
                .ForMember(dest => dest.RequestUpdateDate,
                    opt => opt.MapFrom(src => src.DirectorHistories.OrderBy(o => o.CreatedAt).Where(o => o.UpdateRequestedAt != null).Select(o => o.UpdateRequestedAt).LastOrDefault()))
                .ForMember(dest => dest.DirectorType,
                    opt => opt.MapFrom(src => src.RelationType))
                .ForMember(dest => dest.OfficerType,
                    opt => opt.MapFrom(src => src.OfficerTypeName))
                .ForMember(dest => dest.DirectorVPCode,
                    opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.Specifics,
                    opt => opt.MapFrom(BoDirFilterExpressionBuilder.DirSpecificsExpression))
                .ForMember(dest => dest.HasMissingInformation,
                    opt => opt.MapFrom(BoDirFilterExpressionBuilder.DirHasMissingInformationExpression))
                .ForMember(dest => dest.HasBoDirInformation,
                    opt => opt.MapFrom(_ => true))
                .ForMember(dest => dest.CountryOfFormation,
                    opt => opt.MapFrom(src => src.Country))
                .ForMember(dest => dest.NameOfRegulator,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.JurisdictionOfRegulator,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.SovereignState,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.StockExchangeCode,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.StockExchangeName,
                    opt => opt.MapFrom(src => string.Empty));

            CreateMap<BeneficialOwner, BoDirItemDTO>()
                .ForMember(dest => dest.DirectorName,
                    opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Position,
                    opt => opt.MapFrom(src => BoDirPosition.BeneficialOwner.ToString()))
                .ForMember(dest => dest.ProductionOffice,
                    opt => opt.MapFrom(src => src.LegalEntity.ProductionOffice))
                .ForMember(dest => dest.ReferralOffice,
                    opt => opt.MapFrom(src => src.LegalEntity.ReferralOffice))
                .ForMember(dest => dest.VPEntityNumber,
                    opt => opt.MapFrom(src => src.LegalEntity.Code))
                .ForMember(dest => dest.EntityPortalCode,
                    opt => opt.MapFrom(src => src.LegalEntity.LegacyCode))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.LegalEntity.MasterClient.Code))
                .ForMember(dest => dest.ConfirmedDate,
                    opt => opt.MapFrom(src => src.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Where(o => o.ConfirmedAt != null).Select(o => o.ConfirmedAt).LastOrDefault()))
                .ForMember(dest => dest.Status,
                    opt => opt.MapFrom(src => src.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status).LastOrDefault()))
                .ForMember(dest => dest.RequestUpdateDate,
                    opt => opt.MapFrom(src => src.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Where(o => o.UpdateRequestedAt != null).Select(o => o.UpdateRequestedAt).LastOrDefault()))
                .ForMember(dest => dest.DirectorType,
                    opt => opt.MapFrom(src => src.OfficerTypeCode))
                .ForMember(dest => dest.OfficerType,
                    opt => opt.MapFrom(src => src.OfficerTypeName))
                .ForMember(dest => dest.DirectorVPCode,
                    opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.Specifics,
                    opt => opt.MapFrom(BoDirFilterExpressionBuilder.BoSpecificsExpression))
                .ForMember(dest => dest.HasMissingInformation,
                    opt => opt.MapFrom(BoDirFilterExpressionBuilder.BoHasMissingInformationExpression))
                .ForMember(dest => dest.HasBoDirInformation, opt => opt.MapFrom(_ => true))
                .ForMember(dest => dest.RelationType,
                    opt => opt.MapFrom(src => string.Empty))
                .ForMember(dest => dest.IncorporationPlace,
                    opt => opt.MapFrom(src => string.Empty));
        }
    }
}