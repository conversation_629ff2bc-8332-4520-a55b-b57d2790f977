﻿// <copyright file="UpdateMasterClientDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClient.
    /// </summary>
    public class UpdateMasterClientDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string Code { get; set; }
    }
}
