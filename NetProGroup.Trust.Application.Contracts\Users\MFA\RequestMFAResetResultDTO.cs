﻿// <copyright file="RequestMFAResetResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// A DTO holding the result for the request to reset the MFA.
    /// </summary>
    public class RequestMFAResetResultDTO
    {
        /// <summary>
        /// Gets or sets the id of the user that the MFA infomation is for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the time that the code expires in Utc.
        /// </summary>
        public DateTime? MFAEmailCodeExpiresAt { get; set; }

        /// <summary>
        /// Gets or sets the number of seconds that the code is valid.
        /// </summary>
        public int MFAEmailCodeExpiresIn { get; set; }
    }
}
