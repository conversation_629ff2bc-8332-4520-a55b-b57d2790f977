﻿// <copyright file="VerifyMFACodeResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// DTO for the result of verifiying the MFA code (either authenticator or e-mail).
    /// </summary>
    public class VerifyMFACodeResultDTO
    {
        /// <summary>
        /// Gets or sets the result of the verification.
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the verification succeeded.
        /// </summary>
        public bool Success { get; set; }
    }
}
