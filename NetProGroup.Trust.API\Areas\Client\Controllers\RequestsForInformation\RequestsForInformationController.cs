// <copyright file="RequestsForInformationController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.RequestsForInformation
{
    /// <summary>
    /// Use this controller for request for information related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/requests-for-information")]
    public class RequestsForInformationController : TrustAPIControllerBase
    {
        private readonly IRequestsForInformationAppService _requestsForInformationAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestsForInformationController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="requestsForInformationAppService">The service for requests for information.</param>
        public RequestsForInformationController(
            ILogger<RequestsForInformationController> logger,
            IRequestsForInformationAppService requestsForInformationAppService)
            : base(logger)
        {
            _requestsForInformationAppService = requestsForInformationAppService;
        }

        /// <summary>
        /// Gets the given submission with the request for information and financial period changes details added.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/requests-for-information/details?submissionId={submissionId}.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionRFIDetailsDTO"/>.</returns>
        [HttpGet("details")]
        [SwaggerOperation(OperationId = "Client_GetSubmissionRFIDetails")]
        [ProducesResponseType(typeof(SubmissionRFIDetailsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubmissionRFIDetailsById([FromQuery] Guid submissionId)
        {
            SubmissionRFIDetailsDTO item = null;

            var result = await ProcessRequestAsync<SubmissionRFIDetailsDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _requestsForInformationAppService.GetRFISubmissionDetailsAsync(submissionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Complete a request for information.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/management/requests-for-information/{requestForInformationId}/completed.
        /// </remarks>
        /// <param name="requestForInformationId">The id of the request for information as Guid.</param>
        /// <param name="data">The necessary data used to complete a request for information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpPut("{requestForInformationId}/completed")]
        [SwaggerOperation(OperationId = "Client_Rfi_Ccomplete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CompleteRequestForInformation(
            Guid requestForInformationId,
            CompleteRequestForInformationDTO data)
        {
            Guid item = Guid.Empty;
            var result = await ProcessRequestAsync<Guid>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    await _requestsForInformationAppService.CompleteRequestForInformationAsync(requestForInformationId, data);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Upload a document for a request for information.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/client/requests-for-information/{requestForInformationId}/documents
        ///     {
        ///         data
        ///     }.
        /// </remarks>
        /// <param name="requestForInformationId">The id of the request for information as Guid.</param>
        /// <param name="data">The data necessary to create a request for information document.</param>
        /// <response code="200">OK.</response>
        /// <response code="401">Unauthorized.</response>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("{requestForInformationId}/documents")]
        [SwaggerOperation(OperationId = "Client_Rfi_Document_Create", Summary = "Upload a document for a request for information")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateRequestForinformationDocument(
            Guid requestForInformationId,
            [FromForm]
            CreateRFIDocumentDTO data)
        {
            var result = await ProcessRequestAsync<object>(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));
                    Check.NotNull(data, nameof(data));
                    Check.NotNull(data.File, nameof(data.File));
                },

                executeAsync: async () =>
                {
                    await _requestsForInformationAppService.CreateRequestForInformationDocumentByClientAsync(requestForInformationId, data);
                });

            return result.AsResponse();
        }
    }
}