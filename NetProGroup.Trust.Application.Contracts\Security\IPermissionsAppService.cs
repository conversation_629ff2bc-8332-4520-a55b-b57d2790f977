﻿// <copyright file="IPermissionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.Security
{
    /// <summary>
    /// Interface for the PermissionsAppService.
    /// </summary>
    public interface IPermissionsAppService : IScopedService
    {
        /// <summary>
        /// Gets a list with all available permissions.
        /// </summary>
        /// <returns>A collection of permissions.</returns>
        List<string> GetAllPermissions();
    }
}