// <copyright file="GetSubmissionListRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Request model for getting submissions.
    /// </summary>
    public class GetSubmissionListRequestDTO
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; }

        /// <summary>
        /// Gets or sets the info for sorting.
        /// </summary>
        public SortingInfo SortingInfo { get; set; }

        /// <summary>
        /// Gets or sets the company id.
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the master client code.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the financial year.
        /// </summary>
        [Required]
        public int FinancialYear { get; set; }
    }
}