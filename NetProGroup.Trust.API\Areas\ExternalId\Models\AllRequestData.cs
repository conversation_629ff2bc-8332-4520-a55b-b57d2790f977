﻿// <copyright file="AllRequestData.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
    public class AllRequestData
    {
        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        public string tenantId { get; set; }

        public string authenticationEventListenerId { get; set; }

        public string customAuthenticationExtensionId { get; set; }

        public AuthenticationContext authenticationContext { get; set; }

#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}
