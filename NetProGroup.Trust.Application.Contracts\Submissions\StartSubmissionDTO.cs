﻿// <copyright file="StartSubmissionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Model for starting a new submission.
    /// </summary>
    public class StartSubmissionDTO
    {
        /// <summary>
        /// Gets or sets the id of the legal entity to start the submission for.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module to start the submission for.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the year to start the submission for.
        /// </summary>
        public int? FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the email of the user starting the submission.
        /// </summary>
        public string UserEmail { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier of the user starting the submission.
        /// </summary>
        public Guid UserId { get; set; }
    }
}
