﻿// <copyright file="CompanyAnnualFeesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// DTO holding the paid status of the annual fees fo the conpany.
    /// </summary>
    public class CompanyAnnualFeesDTO
    {
        /// <summary>
        /// Gets or sets the id of the company.
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the list with annual fee items.
        /// </summary>
        public List<AnnualFeeDTO> AnnualFees { get; set; } = new List<AnnualFeeDTO>();
    }
}
