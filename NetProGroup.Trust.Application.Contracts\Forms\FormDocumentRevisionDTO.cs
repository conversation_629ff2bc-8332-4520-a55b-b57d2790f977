﻿// <copyright file="FormDocumentRevisionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a revision of a form document.
    /// </summary>
    public class FormDocumentRevisionDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentRevisionDTO"/> class.
        /// </summary>
        public FormDocumentRevisionDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the date and time that the revision was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the revision of the document.
        /// </summary>
        public int Revision { get; set; }

        /// <summary>
        /// Gets or sets the status of the document revision.
        /// </summary>
        public FormDocumentRevisionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the document revision as text.
        /// </summary>
        public string StatusText { get; set; }

        /// <summary>
        /// Gets or sets the actual form. Is only set when getting a single item.
        /// </summary>
        public FormBuilder FormBuilder { get; set; }
    }
}
