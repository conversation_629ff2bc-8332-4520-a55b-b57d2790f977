﻿// <copyright file="LatePaymentFeeDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Represents the data for LatePaymentFee.
    /// </summary>
    public class LatePaymentFeeDTO
    {
        /// <summary>
        /// Gets or sets the id for the entry to enable updating this entry even when dates changed.
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the description of this entry.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the text to use on the invoice when this fee is applied.
        /// </summary>
        public string InvoiceText { get; set; }

        /// <summary>
        /// Gets or sets the date for the start of the period.
        /// </summary>
        public DateTime StartAt { get; set; }

        /// <summary>
        /// Gets or sets the date for the end of the period.
        /// </summary>
        public DateTime EndAt { get; set; }

        /// <summary>
        /// Gets or sets the year that this fee applies to.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the amount of the fee to pay when the submission was submitted in this period.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the code of the currency for the Amount.
        /// </summary>
        public string CurrencyCode { get; set; }
    }
}
