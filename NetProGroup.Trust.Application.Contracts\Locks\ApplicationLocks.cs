// <copyright file="ApplicationLocks.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Locks
{
    /// <summary>
    /// Application locks.
    /// </summary>
    public static class ApplicationLocks
    {
        /// <summary>
        /// Lock for Payment Transaction.
        /// </summary>
#pragma warning disable CA2211 // Non-constant fields should not be visible
#pragma warning disable SA1310 // Field names should not contain underscore
#pragma warning disable SA1401 // Fields should be private
        public static string PAYMENT_TRANSACTION_LOCK = "PaymentLock";
#pragma warning restore SA1401 // Fields should be private
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore CA2211 // Non-constant fields should not be visible
    }
}