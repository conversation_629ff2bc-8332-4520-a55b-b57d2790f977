// <copyright file="InvoiceDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Application.Contracts.Payments.Invoices
{
    /// <summary>
    /// Data Transfer Object for Invoice information.
    /// </summary>
    public class InvoiceDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the unique identifier of the invoice.
        /// </summary>
        public string InvoiceNr { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the amount of the invoice.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the financial year of the invoice.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number.
        /// </summary>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the file associated with the invoice.
        /// </summary>
        public string File { get; set; }

        /// <summary>
        /// Gets or sets the date when the invoice was issued.
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Gets or sets the payment status of the invoice.
        /// </summary>
        public PaymentStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the currency symbol for the invoice amount.
        /// </summary>
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// Gets or sets the date when the invoice was paid.
        /// </summary>
        public DateTime? PaidDate { get; set; }

        /// <summary>
        /// Gets or sets the transaction ID associated with the invoice payment.
        /// </summary>
        public Guid? TransactionId { get; set; }

        /// <summary>
        /// Gets or sets the transaction ID string associated with the invoice payment.
        /// </summary>
        public string TxId { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the currency used in the invoice.
        /// </summary>
        public Guid CurrencyId { get; set; }

        /// <summary>
        /// Gets or sets the layout of the invoice.
        /// </summary>
        public string Layout { get; set; }

        /// <summary>
        /// Gets or sets the collection of invoice line items associated with the invoice.
        /// </summary>
        /// <value>
        /// A collection of <see cref="InvoiceLineDTO"/> objects representing the individual items in the invoice.
        /// </value>
        public IEnumerable<InvoiceLineDTO> InvoiceLines { get; set; }

        /// <summary>
        /// Gets or sets the date when the invoice was generated.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the Address1 form value of the submission.
        /// </summary>
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the Address2 form value of the submission.
        /// </summary>
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the AddressZipCode form value of the submission.
        /// </summary>
        public string AddressZipCode { get; set; }

        /// <summary>
        /// Gets or sets the AddressCity form value of the submission.
        /// </summary>
        public string AddressCity { get; set; }

        /// <summary>
        /// Gets or sets the AddressCountry form value of the submission.
        /// </summary>
        public string AddressCountry { get; set; }
    }
}
