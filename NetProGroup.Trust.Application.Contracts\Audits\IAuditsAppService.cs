// <copyright file="IAuditsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Audits
{
    /// <summary>
    /// Interface for the Audits application service.
    /// </summary>
    public interface IAuditsAppService : IScopedService
    {
        /// <summary>
        /// Gets all Audits for a given entity.
        /// </summary>
        /// <param name="entityId">The ID of the entity to get audits for.</param>
        /// <param name="pageNumber">Number of the page to get.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <returns>Paged list of AuditUnitOfWorkDTO.</returns>
        Task<IPagedList<AuditUnitOfWorkDTO>> GetAuditsAsync(Guid entityId, int pageNumber = 1, int pageSize = 20);

        /// <summary>
        /// Gets a specific Audit.
        /// </summary>
        /// <param name="auditId">The given id for the Audit.</param>
        /// <returns>AuditUnitOfWorkDTO.</returns>
        Task<AuditUnitOfWorkDTO> GetAuditAsync(Guid auditId);
    }
}
