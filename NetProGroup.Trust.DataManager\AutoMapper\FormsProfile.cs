﻿// <copyright file="FormsProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The forms profile for AutoMapper.
    /// </summary>
    public class FormsProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormsProfile"/> class.
        /// </summary>
        public FormsProfile()
        {
            CreateMap<FormTemplate, FormTemplateDTO>()
                .ForMember(dest =>
                    dest.JurisdictionName,
                    opt => opt.MapFrom(src => src.Jurisdiction == null ? null : src.Jurisdiction.Name))
                .ForMember(dest =>
                    dest.ModuleName,
                    opt => opt.MapFrom(src => src.Module == null ? null : src.Module.Name));

            CreateMap<FormTemplate, FormTemplateWithVersionsDTO>()
                .ForMember(dest => dest.Versions, opt => opt.MapFrom(src => src.FormTemplateVersions));
            CreateMap<FormTemplateVersion, FormTemplateVersionDTO>()
                .ForMember(dest => dest.FormBuilder, opt => opt.Ignore());

            CreateMap<FormDocument, FormDocumentDTO>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()));

            CreateMap<FormDocument, FormDocumentWithRevisionsDTO>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest =>
                    dest.Revisions,
                    opt => opt.MapFrom(src => src.FormDocumentRevisions));

            CreateMap<FormDocumentRevision, FormDocumentRevisionDTO>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest =>
                    dest.FormBuilder,
                    opt => opt.MapFrom(src => FormBuilder.FromJson(src.DataAsJson).Form));
        }
    }
}
