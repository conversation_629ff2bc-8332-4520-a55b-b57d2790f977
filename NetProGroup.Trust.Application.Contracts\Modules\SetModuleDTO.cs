﻿// <copyright file="SetModuleDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.Modules
{
    /// <summary>
    /// Represents a module.
    /// </summary>
    public class SetModuleDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets whether the module is enabled for the enetity that it is requested for or always false for a complete list.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets whether the module is enabled for the enetity that it is requested for or always false for a complete list.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? IsApproved { get; set; }
    }
}
