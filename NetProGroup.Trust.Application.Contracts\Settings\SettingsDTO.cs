﻿// <copyright file="SettingsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// A model for getting/creating/updating settings.
    /// </summary>
    public class SettingsDTO
    {
        /// <summary>
        /// Gets or sets the JurisdictionDocument settings.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public JurisdictionDocumentSettingsDTO JurisdictionDocumentSettings { get; set; }

        /// <summary>
        /// Gets or sets the Fee settings.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public FeeSettingsDTO FeeSettings { get; set; }

        /// <summary>
        /// Gets or sets the STRLatePaymentFee settings.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public STRLatePaymentFeeSettingsDTO STRLatePaymentFeeSettings { get; set; }

        /// <summary>
        /// Gets or sets the Submission settings.
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public SubmissionSettingsDTO SubmissionSettings { get; set; }

        /// <summary>
        /// Gets or sets the logs for the settings.
        /// </summary>
        public List<ActivityLogItemDTO> Logs { get; set; }
    }
}
