﻿// <copyright file="BeneficialOwnerComparisonDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners
{
    /// <summary>
    /// Represents a set of 2 BeneficialOwners to compare before confirmed or an update requested.
    /// </summary>
    public class BeneficialOwnerComparisonDTO
    {
        /// <summary>
        /// Gets or sets the current (latest version) of the BeneficialOwner.
        /// </summary>
        public BeneficialOwnerDTO CurrentVersion { get; set; }

        /// <summary>
        /// Gets or sets the prior version of the BeneficialOwner.
        /// </summary>
        public BeneficialOwnerDTO PriorVersion { get; set; }
    }
}