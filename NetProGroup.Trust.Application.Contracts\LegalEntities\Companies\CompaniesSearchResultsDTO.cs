﻿// <copyright file="CompaniesSearchResultsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// Model holding the results of the search for companies.
    /// </summary>
    public class CompaniesSearchResultsDTO
    {
        /// <summary>
        /// Gets or sets the collection of found companies.
        /// </summary>
        public IReadOnlyCollection<CompaniesSearchResultDTO> Companies { get; set; }
    }
}
