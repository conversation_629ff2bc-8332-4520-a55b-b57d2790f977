﻿// <copyright file="UsersSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the users in ProjectsDashboard.
    /// </summary>
    public static partial class UsersSeeder
    {
        /// <summary>
        /// Defines the seed.
        /// </summary>
        /// <param name="modelBuilder">The ModelBuilder entity.</param>
        public static void SeedUsers([NotNull] this ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ApplicationUser>().HasData
            (
                new ApplicationUser(UserConsts.SystemUserId, "System", "System", true, null, null, null, null, false, null, null, null, null, false, false, null, false, 0),
                new ApplicationUser(UserConsts.InboxUserId, "Inbox", "Inbox", true, null, null, null, null, false, null, null, null, null, false, false, null, false, 0));
        }
    }
}