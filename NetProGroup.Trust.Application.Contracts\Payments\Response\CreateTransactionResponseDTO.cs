// <copyright file="CreateTransactionResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Payments.Models.PaymentsAPI.V2;

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Represents a generic response object for creating a payment.
    /// </summary>
    public class CreateTransactionResponseDTO
    {
        /// <summary>
        /// Gets or sets the response from the payment processor.
        /// </summary>
        /// <value>
        /// The response object provided by the payment processor after a payment request is made.
        /// </value>
        public StartPaymentResponse PaymentProcessorResponse { get; set; }
    }
}