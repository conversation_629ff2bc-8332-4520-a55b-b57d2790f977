// <copyright file="ReportDownloadResponseDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Errors;

namespace NetProGroup.Trust.Application.Contracts.Reports
{
    /// <summary>
    /// Response model for downloading a report.
    /// </summary>
    public class ReportDownloadResponseDTO
    {
        /// <summary>
        /// Gets or sets the file content.
        /// </summary>
        public MemoryStream FileContent { get; set; }

        /// <summary>
        /// Gets or sets the file name.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the content type.
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// Gets or sets the extension.
        /// </summary>
        public string Extension { get; set; }

        /// <summary>
        /// Creates a new ReportDownloadResponseDTO for the filename and content.
        /// </summary>
        /// <param name="fileName">The filename for the content.</param>
        /// <param name="fileContent">The filecontent as byte array.</param>
        /// <returns>The created ReportDownloadResponseDTO.</returns>
        /// <exception cref="ContentTypeNotSupportedException">Thrown when the extension of the file is not supported.</exception>
        public static ReportDownloadResponseDTO Create(string fileName, byte[] fileContent)
        {
            var extension = System.IO.Path.GetExtension(fileName).Substring(1);

            var contenttype = extension switch
            {
                "xlsx" => ReportContentTypes.Excel,
                _ => throw new ContentTypeNotSupportedException("Extension {0} not supported", extension)
            };

            return new ReportDownloadResponseDTO
            {
                FileContent = new MemoryStream(fileContent),
                FileName = fileName,
                ContentType = contenttype,
                Extension = extension
            };
        }

        /// <summary>
        /// Creates a new ReportDownloadResponseDTO for the filename and content.
        /// </summary>
        /// <param name="fileName">The filename for the content.</param>
        /// <param name="stream">The filecontent as memory stream.</param>
        /// <returns>The created ReportDownloadResponseDTO.</returns>
        /// <exception cref="ContentTypeNotSupportedException">Thrown when the extension of the file is not supported.</exception>
        public static ReportDownloadResponseDTO Create(string fileName, MemoryStream stream)
        {
            var extension = System.IO.Path.GetExtension(fileName).Substring(1);

            var contenttype = extension switch
            {
                "xlsx" => ReportContentTypes.Excel,
                _ => throw new ContentTypeNotSupportedException("Extension {Extension} not supported", extension)
            };

            return new ReportDownloadResponseDTO
            {
                FileContent = stream,
                FileName = fileName,
                ContentType = contenttype,
                Extension = extension
            };
        }
    }
}