﻿// <copyright file="SubmissionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Models.Exceptions;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Application.Contracts.Tools;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Submissions
{
    /// <summary>
    /// Use this controller for submission related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/submissions")]
    public class SubmissionsController : TrustAPIControllerBase
    {
        private readonly ISubmissionsAppService _submissionsAppService;
        private readonly ISubmissionReportsAppService _submissionReportsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="submissionsAppService">The service for submissions.</param>
        /// <param name="submissionReportsAppService">The service for the submission reports.</param>
        public SubmissionsController(
            ILogger<SubmissionsController> logger,
            ISubmissionsAppService submissionsAppService,
            ISubmissionReportsAppService submissionReportsAppService)
            : base(logger)
        {
            _submissionsAppService = submissionsAppService;
            _submissionReportsAppService = submissionReportsAppService;
        }

        /// <summary>
        /// Updates the payment status of multiple submissions.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/submissions/payment-status
        ///     {
        ///         "submissionIds": ["guid1", "guid2", "guid3"],
        ///         "isPaid": true
        ///     }
        /// .
        /// </remarks>
        /// <param name="request">The request containing submission IDs and payment status.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>s.</returns>
        [HttpPut("payment-status")]
        [SwaggerOperation(OperationId = "Management_MarkAsPaid")]
        [ProducesResponseType(typeof(MarkSubmissionsAsPaidResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed, Type = typeof(APIExceptionModel))]
        public async Task<IActionResult> MarkAsPaid([FromBody] MarkSubmissionsAsPaidRequestDTO request)
        {
            MarkSubmissionsAsPaidResponse items = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNullOrEmpty(request.SubmissionIds, nameof(request.SubmissionIds));
                },

                executeAsync: async () =>
                {
                    items = await _submissionsAppService.MarkSubmissionsAsPaidAsync(request.SubmissionIds, request.IsPaid);
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Updates the payment status of multiple submissions based on company code and filing year.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/submissions/payment-status-by-company-year
        ///     {
        ///         "moduleId": "guid",
        ///         "companyFilingYears": [
        ///             { "CompanyVPCode": "COMP1", "FinancialYear": 2021 },
        ///             { "CompanyVPCode": "COMP2", "FinancialYear": 2022 }
        ///         ],
        ///         "isPaid": true
        ///     }
        /// .
        /// </remarks>
        /// <param name="request">The request containing company code and filing year pairs and payment status.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>s.</returns>
        [HttpPut("payment-status-by-company-year")]
        [SwaggerOperation(OperationId = "Management_MarkAsPaidByCompanyAndYear")]
        [ProducesResponseType(typeof(MarkSubmissionsAsPaidByCompanyYearResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed, Type = typeof(APIExceptionModel))]
        public async Task<IActionResult> MarkAsPaidByCompanyAndYear([FromBody] MarkSubmissionsAsPaidByCompanyYearsRequestDTO request)
        {
            MarkSubmissionsAsPaidByCompanyYearResponse items = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNullOrEmpty(request.CompanyFilingYears, nameof(request.CompanyFilingYears));
                },

                executeAsync: async () =>
                {
                    items = await _submissionsAppService.MarkSubmissionsAsPaidByCompanyAndYearAsync(request.CompanyFilingYears, request.IsPaid, request.ModuleId);
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the payment status of submissions based on company code and filing year.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/management/submissions/payment-status-by-company-year
        ///     {
        ///         "moduleId": "guid",
        ///         "companyFilingYears": [
        ///             { "CompanyVPCode": "COMP1", "FinancialYear": 2021 },
        ///             { "CompanyVPCode": "COMP2", "FinancialYear": 2022 }
        ///         ]
        ///     }
        /// .
        /// </remarks>
        /// <param name="request">The request containing company code and filing year pairs.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the payment status for each company/year pair.</returns>
        [HttpPost("payment-status-by-company-year")]
        [SwaggerOperation(OperationId = "Management_GetPaidStatusByCompanyAndYear")]
        [ProducesResponseType(typeof(SubmissionsPaidStatusResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPaidStatusByCompanyAndYear([FromBody] GetSubmissionsPaidStatusRequestDTO request)
        {
            SubmissionsPaidStatusResponse items = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNullOrEmpty(request.CompanyFilingYears, nameof(request.CompanyFilingYears));
                },

                executeAsync: async () =>
                {
                    items = await _submissionsAppService.GetSubmissionsPaidStatusByCompanyAndYearAsync(request.CompanyFilingYears, request.ModuleId);
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given submission with optionally the FormDocument data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/submissions/{submissionId}?includeFormDocument=true
        /// .
        /// </remarks>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <param name="includeFormDocument">Denotes wether to include the form document in the result.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpGet("{submissionId}")]
        [SwaggerOperation(OperationId = "Management_GetSubmission")]
        [ProducesResponseType(typeof(Swagger.ResponseTypes.SubmissionDTOResponseTypes), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubmission(Guid submissionId, bool includeFormDocument)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _submissionsAppService.GetSubmissionAsync(submissionId, includeFormDocument, true);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Create a new revision of the submission (re-opening it).
        /// </summary>
        /// <remarks>
        /// This will create a new revision based on the last finalized revision.
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/management/submissions/{submissionId}/reopen-request
        ///             {
        ///                 Comments: "Comment 123"
        ///             }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to reopen.</param>
        /// <param name="data">The necessary data used to reopen a submission.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionDTO"/>.</returns>
        [HttpPost("{submissionId}/reopen-request")]
        [SwaggerOperation(OperationId = "Management_ReopenSubmission")]
        [ProducesResponseType(typeof(SubmissionDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed, Type = typeof(APIExceptionModel))]
        public async Task<IActionResult> ReopenSubmission(
            Guid submissionId,
            ReopenSubmissionDTO data = null)
        {
            SubmissionDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));
                },

                executeAsync: async () =>
                {
                    data ??= new ReopenSubmissionDTO();
                    data.SubmissionId = submissionId;
                    item = await _submissionsAppService.ReopenSubmissionAsync(data);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of submissions that match the criteria.
        /// </summary>
        /// <remarks>
        /// Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office (OR)\
        /// or use the specific searchterms to search for a combination (AND).
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/submissions.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_ListSubmissions")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListSubmissions([FromQuery] SearchSubmissionsRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                 {
                     Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

                     if (!string.IsNullOrEmpty(request.GeneralSearchTerm))
                     {
                         if (!string.IsNullOrEmpty(request.LegalEntitySearchTerm) ||
                             !string.IsNullOrEmpty(request.MasterClientSearchTerm) ||
                             !string.IsNullOrEmpty(request.ReferralOfficeSearchTerm))
                         {
                             ModelState.AddModelError(nameof(request.GeneralSearchTerm), $"Can not combine {nameof(request.GeneralSearchTerm)} with {nameof(request.LegalEntitySearchTerm)}, " +
                                                                                         $"{nameof(request.MasterClientSearchTerm)} or {nameof(request.ReferralOfficeSearchTerm)}");
                         }
                     }
                 },

                 executeAsync: async _ =>
                 {
                     return await _submissionsAppService.SearchSubmissionsAsync(request);
                 });

            return result.AsResponse();
        }

        /// <summary>
        /// Generate a submission report given a module and a filter request.
        /// </summary>
        /// <remarks>
        /// Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/submissions/report.
        /// </remarks>
        /// <param name="request">Request model holding filter and searching parameters.</param>
        /// <returns>
        /// A <see cref="FileContentResult"/> containing the financial report file with appropriate content type
        /// and file name if the download is successful. Returns an appropriate response code if the request is unauthorized or forbidden.
        /// </returns>
        [HttpGet("report")]
        [SwaggerOperation(OperationId = "Management_GenerateNevisSubmissionsReport")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> GenerateNevisSubmissionsReport(
            [FromQuery] SubmissionsReportRequestNevisDTO request)
        {
            ReportDownloadResponseDTO item = null;
            var result = await ProcessRequestAsync<SubmissionsReportRequestNevisDTO>(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNull(request.ModuleId, nameof(request.ModuleId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _submissionReportsAppService.GenerateSubmissionsReportForNevisAsync(request);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }

        /// <summary>
        /// Exports the submissions specified in the request for the ITA.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///    PUT /api/v1/management/submissions/export.
        /// </remarks>
        /// <param name="request">The request containing the submission IDs to export.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpPut("export")]
        [SwaggerOperation(OperationId = "Management_ExportSubmission")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportSubmission(ExportSubmissionDTO request)
        {
            ReportDownloadResponseDTO item = null;
            var result = await ProcessRequestAsync<ExportSubmissionResponseDTO>(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNull(request.SubmissionIds, nameof(request.SubmissionIds));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _submissionReportsAppService.ExportSubmissionAsync(request);
                });

            if (result.Exception != null)
            {
                return result.AsResponse();
            }
            else
            {
                return new FileContentResult(item.FileContent.ToArray(), item.ContentType)
                {
                    FileDownloadName = item.FileName
                };
            }
        }

        /// <summary>
        /// Generate a zip file containing the submission documents.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/submissions/{submissionId}/documents.
        /// </remarks>
        /// <param name="submissionId">The submission id as Guid.</param>
        /// <returns>
        /// A <see cref="FileContentResult"/> containing the zip file generted.
        /// </returns>
        [HttpGet("{submissionId}/documents")]
        [SwaggerOperation(OperationId = "Management_DownloadSubmissionDocuments")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadSubmissionDocuments(
            Guid submissionId)
        {
            ZipFileDTO item = null;
            var result = await ProcessRequestAsync<ZipFileDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _submissionsAppService.RetrieveSubmissionDocumentsAsync(submissionId);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }

        /// <summary>
        /// Generate a zip file containing the submission documents.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/submissions/documents.
        /// </remarks>
        /// <param name="submissionIds">The submission ids as a list of Guid.</param>
        /// <returns>
        /// A <see cref="FileContentResult"/> containing the zip file generted.
        /// </returns>
        [HttpPut("documents")]
        [SwaggerOperation(OperationId = "Management_DownloadSubmissionsDocuments")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadSubmissionsDocuments(
            List<Guid> submissionIds)
        {
            ZipFileDTO item = null;
            var result = await ProcessRequestAsync<ZipFileDTO>(
                validate: () =>
                {
                    Check.NotNull(submissionIds, nameof(submissionIds));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _submissionsAppService.RetrieveSubmissionsDocumentsAsync(submissionIds);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }

        /// <summary>
        /// Updates the submission general information.
        /// </summary>
        /// <remarks>
        /// This update basic information fo a submission.
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/management/submissions/{submissionId}/information
        ///         {
        ///             data
        ///         }.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to delete as Guid.</param>
        /// <param name="data">The necessary data to update the submission general information.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{submissionId}/information")]
        [SwaggerOperation(OperationId = "Management_UpdateSubmissionInformation")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> UpdateSubmissionInformation(
            Guid submissionId,
            UpdateSubmissionInformationDTO data)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));
                    Check.NotNull(data, nameof(data));
                },
                executeAsync: async () =>
                {
                    await _submissionsAppService.UpdateSubmissionGeneralInformationManagementAsync(submissionId, data);
                });

            return result.AsResponse();
        }
    }
}
