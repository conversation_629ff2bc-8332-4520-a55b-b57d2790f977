// <copyright file="BoDirDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Extensions;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.DomainShared.Enums;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.BoDir
{
    /// <summary>
    /// Manager class for handling Beneficial Owners and Directors data operations.
    /// </summary>
    public class BoDirDataManager : IBoDirDataManager
    {
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private readonly IConfigurationProvider _mapperConfigurationProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirDataManager"/> class.
        /// </summary>
        /// <param name="legalEntitiesRepository">The repository for legal entities.</param>
        /// <param name="authorizationFilterExpressionFactory">The factory for authorization filter expressions.</param>
        /// <param name="mapper">The AutoMapper instance for object mapping.</param>
        public BoDirDataManager(ILegalEntitiesRepository legalEntitiesRepository,
            IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory,
            IMapper mapper)
        {
            _legalEntitiesRepository = legalEntitiesRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
            _mapperConfigurationProvider = mapper.ConfigurationProvider;
        }

        /// <inheritdoc />
        public async Task<IPagedList<BoDirItemDTO>> SearchBoDirsAsync(SearchBoDirRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Build base query with conditional includes
            var baseQuery = BuildBaseQuery(request);

            // Apply filters and sorting
            var filteredQuery = ApplyFiltersAndSort(baseQuery, request);

            // Paginate the results
            var paginatedQueryResult = await PaginateQueryAsync(filteredQuery, request);

            return paginatedQueryResult;
        }

        #region Private Methods

        private static IQueryable<BoDirItemDTO> BoDirItemDtoFilters(SearchBoDirRequest request, IQueryable<BoDirItemDTO> query)
        {
            var requestNoBoDir = request.Specifics?.Contains(BoDirSpecifics.NoBoDirInformation) == true;
            var requestMissing = request.Specifics?.Contains(BoDirSpecifics.MissingInformation) == true;
            var requestComplete = request.Specifics?.Contains(BoDirSpecifics.BoDirInformation) == true;

            if (!request.Specifics.IsNullOrEmpty())
            {
                query = query.Where(dto =>
                    (requestMissing && dto.HasMissingInformation) ||
                    (requestComplete && dto.HasBoDirInformation && !dto.HasMissingInformation) ||
                    (requestNoBoDir && !dto.HasBoDirInformation));
            }

            if (request.ConfirmedDateFrom.HasValue && request.ConfirmedDateTo.HasValue)
            {
                query = query.Where(dto =>
                    dto.ConfirmedDate.HasValue &&
                    dto.ConfirmedDate.Value >= request.ConfirmedDateFrom.Value &&
                    dto.ConfirmedDate.Value <= request.ConfirmedDateTo.Value);
            }
            else
            {
                query = request.ConfirmedDateFrom.HasValue
                    ? query.Where(dto =>
                        dto.ConfirmedDate.HasValue &&
                        dto.ConfirmedDate.Value >= request.ConfirmedDateFrom.Value)
                    : request.ConfirmedDateTo.HasValue
                        ? query.Where(dto =>
                            dto.ConfirmedDate.HasValue &&
                            dto.ConfirmedDate.Value <= request.ConfirmedDateTo.Value)
                        : query;
            }

            if (request.DataStatuses.Count > 0)
            {
                query = query.Where(dto =>
                    request.DataStatuses.Contains(dto.Status));
            }

            if (request.ProductionOffice != null)
            {
                query = query.Where(dto =>
                    dto.ProductionOffice.Contains(request.ProductionOffice));
            }

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                query = query.Where(dto =>
                    dto.LegalEntityName.Contains(request.SearchTerm) ||
                    dto.DirectorName.Contains(request.SearchTerm) ||
                    dto.MasterClientCode.Contains(request.SearchTerm) ||
                    dto.VPEntityNumber.Contains(request.SearchTerm) ||
                    dto.EntityPortalCode.Contains(request.SearchTerm) ||
                    dto.ReferralOffice.Contains(request.SearchTerm) ||
                    dto.ProductionOffice.Contains(request.SearchTerm));
            }

            return query;
        }

        /// <summary>
        /// Applies sorting based on the request parameters.
        /// </summary>
        private static IQueryable<BoDirItemDTO> ApplySorting(IQueryable<BoDirItemDTO> query, SearchBoDirRequest request)
        {
            var overrides = new Dictionary<string, Expression<Func<BoDirItemDTO, object>>>()
            {
                {
                    nameof(BoDirItemDTO.Status), s => s.Status == LegalEntityRelationStatus.Initial ? 1 :
                        s.Status == LegalEntityRelationStatus.Refreshed ? 2 :
                        s.Status == LegalEntityRelationStatus.Confirmed ? 3 :
                        s.Status == LegalEntityRelationStatus.PendingUpdateRequest ? 4 :
                        s.Status == LegalEntityRelationStatus.UpdateReceived ? 5 :
                        -1
                }
            };

            Expression<Func<BoDirItemDTO, object>> defaultSort = s => s.LegalEntityName;

            return query.SortBySpecification<BoDirItemDTO, SearchBoDirRequestDTO>(request.ToSortingInfo(), defaultSort, overrides);
        }

        /// <summary>
        /// Paginates the query and returns a paged result.
        /// </summary>
        private static async Task<IPagedList<BoDirItemDTO>> PaginateQueryAsync(IQueryable<BoDirItemDTO> query, SearchBoDirRequest request) => await query.ToPagedListAsync(request.PageNumber, request.PageSize);

        /// <summary>
        /// Builds the base query with necessary includes and optimizations.
        /// </summary>
        private IQueryable<LegalEntity> BuildBaseQuery(SearchBoDirRequest request)
        {
            return _legalEntitiesRepository
                   .GetQueryable()
                    .Where(le => le.IsActive) // Only include active entities
                    .Where(_authorizationFilterExpressionFactory.GetLegalEntityJurisdictionFilterPredicate(request))
                    .AsNoTracking()
                    .AsSplitQuery();
        }

        /// <summary>
        /// Transforms the base query of legal entities into a filtered and projected DTO query based on the search request.
        /// </summary>
        /// <param name="baseQuery">The base query of legal entities.</param>
        /// <param name="request">The search request containing filtering criteria.</param>
        /// <returns>An IQueryable of BoDirItemDTO matching the search criteria.</returns>
        private IQueryable<BoDirItemDTO> TransformToDTO(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            var directorQuery = baseQuery.SelectMany(le => le.Directors).ProjectTo<BoDirItemDTO>(_mapperConfigurationProvider);
            var beneficialOwnerQuery = baseQuery.SelectMany(le => le.BeneficialOwners).ProjectTo<BoDirItemDTO>(_mapperConfigurationProvider);

            var query = request.Position switch
            {
                BoDirPosition.Director => directorQuery,
                BoDirPosition.BeneficialOwner => beneficialOwnerQuery,
                _ => directorQuery.Concat(beneficialOwnerQuery)
            };

            if (!request.Position.HasValue)
            {
                // Add legalEntities that do not have any BoDir information
                query = query.Concat(baseQuery.ProjectTo<BoDirItemDTO>(_mapperConfigurationProvider)
                                              .Where(dto => !dto.HasBoDirInformation));
            }

            return query;
        }

        /// <summary>
        /// Applies filters and sorting based on the request.
        /// </summary>
        private IQueryable<BoDirItemDTO> ApplyFiltersAndSort(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // Transform the base query into DTOs based on request criteria
            var query = TransformToDTO(baseQuery, request);

            // Apply filters to the DTO query
            query = BoDirItemDtoFilters(request, query);

            // Apply sorting
            return ApplySorting(query, request);
        }

        #endregion
    }
}
