// <copyright file="IBulkOperationProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;

namespace NetProGroup.Trust.DataManager.Bulk
{
    /// <summary>
    /// Interface for bulk operation providers.
    /// </summary>
    public interface IBulkOperationProvider
    {
        /// <summary>
        /// Performs a bulk insert operation.
        /// </summary>
        /// <typeparam name="T">The entity type.</typeparam>
        /// <param name="entities">The entities to insert.</param>
        /// <param name="context">The database context.</param>
        /// <param name="batchSize">The batch size (optional).</param>
        /// <param name="includeGraph">Whether to include the related entities (optional).</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task BulkInsertAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class;

        /// <summary>
        /// Performs a bulk update operation.
        /// </summary>
        /// <typeparam name="T">The entity type.</typeparam>
        /// <param name="entities">The entities to update.</param>
        /// <param name="context">The database context.</param>
        /// <param name="batchSize">The batch size (optional).</param>
        /// <param name="includeGraph">Whether to include the related entities (optional).</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task BulkUpdateAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class;

        /// <summary>
        /// Performs a bulk delete operation.
        /// </summary>
        /// <typeparam name="T">The entity type.</typeparam>
        /// <param name="entities">The entities to delete.</param>
        /// <param name="context">The database context.</param>
        /// <param name="batchSize">The batch size (optional).</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task BulkDeleteAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null) where T : class;

        /// <summary>
        /// Performs a bulk save changes operation.
        /// </summary>
        /// <param name="context">The database context.</param>
        /// <returns>A task representing the asynchronous operation with the number of entities written to the database.</returns>
        Task<int> BulkSaveChangesAsync(DbContext context);
    }
}
