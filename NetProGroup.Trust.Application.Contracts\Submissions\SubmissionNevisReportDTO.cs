﻿// <copyright file="SubmissionNevisReportDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents a submission for Nevis report generation.
    /// </summary>
    public class SubmissionNevisReportDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionNevisReportDTO"/> class.
        /// </summary>
        public SubmissionNevisReportDTO()
        {
        }

        /// <summary>
        /// Gets or sets the financial year of the submission.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the contact information email from form document attributes.
        /// </summary>
        public string CreatedByEmail { get; set; }

        /// <summary>
        /// Gets or sets the legal entity name.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the legal entity legacy code.
        /// </summary>
        public string LegalEntityLegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the legal entity code.
        /// </summary>
        public string LegalEntityCode { get; set; }

        /// <summary>
        /// Gets or sets the master client code.
        /// </summary>
        public string LegalEntityMasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number.
        /// </summary>
        public string LegalEntityIncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission is paid.
        /// </summary>
        public bool IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the submission status.
        /// </summary>
        public SubmissionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the submission.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the submission date.
        /// </summary>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the payment date.
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// Gets or sets the payment reference.
        /// </summary>
        public string PaymentReference { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string LegalEntityReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the time zone ID for date formatting.
        /// </summary>
        public string LegalEntityJurisdictionCode { get; set; }
    }
}
