// <copyright file="AuditsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Audits.Controllers
{
    /// <summary>
    /// Controller for the 'audits' endpoints.
    /// </summary>
    [ApiController]
    [Area("Audits")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class AuditsController : TrustAPIControllerBase
    {
        private readonly IAuditsAppService _auditsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AuditsController"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="auditsAppService">Implementation for Audits.</param>
        public AuditsController(
            ILogger<AuditsController> logger,
            IAuditsAppService auditsAppService) : base(logger)
        {
            _auditsAppService = auditsAppService;
        }

        /// <summary>
        /// Returns a paginated list of audits for a given entity.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/audits/audits?entityId=5701384D-0411-49E6-8378-D6263AAD497B&amp;pagenumber=1.
        ///
        /// </remarks>
        /// <param name="entityId">The ID of the entity to get audits for.</param>
        /// <param name="pageNumber">Optional. Page number for pagination (default is 1).</param>
        /// <param name="pageSize">Optional. Number of records per page (default is 20).</param>
        /// <returns>A <see cref="Task{IActionResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "ListAudits")]
        [ProducesResponseType(typeof(PaginatedResponse<AuditUnitOfWorkDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListAudits(
            [Required] Guid entityId,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(entityId, nameof(entityId));
                },

                executeAsync: async (pagingInfo) =>
                {
                    return await _auditsAppService.GetAuditsAsync(entityId, pagingInfo.PageNumber, pagingInfo.PageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Returns a specific audit.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/audits/audits/{auditId}.
        ///
        /// </remarks>
        /// <param name="auditId">Id of the Audit to get.</param>
        /// <returns>A <see cref="Task{IActionResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpGet("{auditId}")]
        [SwaggerOperation(OperationId = "GetAudit")]
        [ProducesResponseType(typeof(AuditUnitOfWorkDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAudit(Guid auditId)
        {
            AuditUnitOfWorkDTO item = null;

            var result = await ProcessRequestAsync(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(auditId, nameof(auditId));
                },

                executeAsync: async () =>
                {
                    item = await _auditsAppService.GetAuditAsync(auditId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
