﻿// <copyright file="FormDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a form document.
    /// </summary>
    public class FormDocumentDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentDTO"/> class.
        /// </summary>
        public FormDocumentDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the document.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the document was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the document.
        /// </summary>
        public FormDocumentStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the document as text.
        /// </summary>
        public string StatusText { get; set; }
    }
}
