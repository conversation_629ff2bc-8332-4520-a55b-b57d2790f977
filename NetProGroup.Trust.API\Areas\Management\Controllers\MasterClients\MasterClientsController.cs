﻿// <copyright file="MasterClientsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;
using NetProGroup.Trust.Application.Contracts.Settings;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.MasterClients
{
    /// <summary>
    /// Use this controller to execute imports of MasterClients.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/master-clients")]
    public class MasterClientsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IMasterClientsAppService _masterClientsAppService;
        private readonly ILegalEntitiesAppService _legalEntitiesAppService;
        private readonly ISettingsAppService _settingsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="masterClientsAppService">The service for master clients.</param>
        /// <param name="legalEntitiesAppService">The service for legal entities (companies).</param>
        /// <param name="settingsAppService">The service for settings.</param>
        public MasterClientsController(
            ILogger<MasterClientsController> logger,
            IConfiguration configuration,
            IMasterClientsAppService masterClientsAppService,
            ILegalEntitiesAppService legalEntitiesAppService,
            ISettingsAppService settingsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _masterClientsAppService = masterClientsAppService;
            _legalEntitiesAppService = legalEntitiesAppService;
            _settingsAppService = settingsAppService;
        }

        /// <summary>
        /// Gets a single master client.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/master-clients/{masterclientId}.
        /// </remarks>
        /// <param name="masterclientId">The id of the masterclient to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the master clients.</returns>
        [HttpGet("{masterclientId}")]
        [SwaggerOperation(OperationId = "GetMasterClient")]
        [ProducesResponseType(typeof(MasterClientDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClient(
            Guid masterclientId)
        {
            MasterClientDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(masterclientId, nameof(masterclientId));
                },

                executeAsync: async () =>
                {
                    item = await _masterClientsAppService.GetMasterClientAsync(masterclientId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the master clients with pagination.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/master-clients?searchTerm={searchTerm}&amp;jurisdictionid={jurisdictionid}&amp;pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="searchTerm">Optional search term which searches in Code and owner email addresses.</param>
        /// <param name="jurisdictionId">Optional id of the jurisdiction.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the master clients.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetMasterClients")]
        [ProducesResponseType(typeof(PaginatedResponse<MasterClientDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClients(
            string searchTerm = null,
            Guid? jurisdictionId = null,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },
                executeAsync: async (pagingInfo) =>
                {
                    return await _masterClientsAppService.GetMasterClientsAsync(searchTerm, jurisdictionId, pageNumber, pageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the companies with pagination for the master client.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/master-clients/{masterclientid}/companies?pageNumber={pageNumber}&amp;pageSize={pageSize}.
        ///
        /// Possible values for OnboardingStatus:\
        ///   0 =  Onboarding\
        ///   1 =  Approved\
        ///   2 =  Declined\
        /// .
        /// </remarks>
        /// <param name="masterclientId">Id of the master client.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <param name="sortBy">Name of the field to sort by.</param>
        /// <param name="sortOrder">The sort direction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the companies.</returns>
        [HttpGet("{masterclientId}/companies")]
        [SwaggerOperation(OperationId = "GetMasterClientCompanies")]
        [ProducesResponseType(typeof(PaginatedResponse<CompanyDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClientCompanies(
            Guid masterclientId,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize,
            string sortBy = null, string sortOrder = null)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },

                executeAsync: async (pagingInfo) =>
                    await _legalEntitiesAppService.ListCompaniesAsync(masterclientId, null, null, null, pagingInfo.PageNumber, pagingInfo.PageSize, sortBy, sortOrder));

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the users with pagination for the master client.
        /// </summary>
        /// <remarks>
        /// Each user has information about its registration and invitation status.
        ///
        /// Sample request:
        ///
        ///     GET /api/management/master-clients/{masterclientid}/users?pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="masterclientId">Id of the master client.</param>
        /// <param name="request">A request DTO for the parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the users.</returns>
        [HttpGet("{masterclientId}/users")]
        [SwaggerOperation(OperationId = "GetMasterClientUsers")]
        [ProducesResponseType(typeof(PaginatedResponse<ListUserDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClientUsers(
            Guid masterclientId,
            [FromQuery] ListUsersRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(null,
                validate: () => { },

                executeAsync: async (pagingInfo) =>
                {
                    return await _masterClientsAppService.ListUsersAsync(masterclientId, request);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets a set of settings for a specific purpose, identied by the key.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/master-clients/{masterclientid}/settings?key=fees.
        /// </remarks>
        /// <param name="masterclientId">Id of the master client.</param>
        /// <param name="key">The key to identify the type of settings.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpGet("{masterclientId}/settings")]
        [SwaggerOperation(OperationId = "GetMasterClientSettings")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMasterClientSettings(
            Guid masterclientId,
            string key)
        {
            object item = null;

            var result = await ProcessRequestAsync<object>(null,
                validate: () =>
                {
                    Check.NotNullOrEmpty(key, nameof(key));
                },

                executeAsync: async () =>
                {
                    item = await _settingsAppService.ReadSettingsForMasterClientAsync(masterclientId, key);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts a set of settings for a specific purpose, identied by the key.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/management/master-clients/{masterclientid}/settings?key=fees
        ///     {
        ///
        ///     }.
        /// </remarks>
        /// <param name="masterclientId">Id of the master client.</param>
        /// <param name="key">The key to identify the type of settings.</param>
        /// <param name="data">The data to save as settings.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpPost("{masterclientId}/settings")]
        [SwaggerOperation(OperationId = "SetMasterClientSettings")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SetMasterClientSettings(
            Guid masterclientId,
            string key,
            object data)
        {
            var result = await ProcessRequestAsync<object>(null,
                validate: () =>
                {
                    Check.NotNullOrEmpty(key, nameof(key));
                },

                executeAsync: async () =>
                {
                    await _settingsAppService.SaveSettingsForMasterClientAsync(masterclientId, key, data.ToString());
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Adds a user by emailaddress to a master client.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/management/master-clients/{masterclientId}/users
        ///     {
        ///         "emailAddress": "<EMAIL>"
        ///     }.
        /// </remarks>
        /// <param name="masterclientId">Id of the masterclient to add the user to.</param>
        /// <param name="model">The DTO containing the user email address.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("{masterclientId}/users")]
        [SwaggerOperation(OperationId = "AddUserToMasterClient", Summary = "Adds a user by emailaddress to a master client.")]
        [ProducesResponseType(typeof(MasterClientUserDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddUserToMasterClient(
            Guid masterclientId,
            [FromBody] CreateMasterClientUserDTO model)
        {
            MasterClientUserDTO item = null;

            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    model.MasterClientId = masterclientId;

                    Check.NotDefaultOrNull<Guid>(model.MasterClientId, nameof(model.MasterClientId));
                    Check.NotNullOrWhiteSpace(model.EmailAddress, nameof(model.EmailAddress));
                },

                executeAsync: async () =>
                {
                    item = await _masterClientsAppService.CreateUserToMasterClientAsync(model);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Removes a user from a master client.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE /api/management/master-clients/{masterclientId}/users/{userId}.
        /// </remarks>
        /// <param name="masterclientId">Id of the masterclient to remove the user from.</param>
        /// <param name="userId">Id of the user to remove from the masterclient.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpDelete("{masterclientId}/users/{userId}")]
        [SwaggerOperation(OperationId = "RemoveUserFromMasterClient", Summary = "Removes a user from a master client.")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> RemoveUserFromMasterClient(
            Guid masterclientId,
            Guid userId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(masterclientId, nameof(masterclientId));
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    await _masterClientsAppService.RemoveUserFromMasterClientAsync(new RemoveMasterClientUserDTO { MasterClientId = masterclientId, UserId = userId });
                });

            return result.AsNoContentResponse();
        }
    }
}
