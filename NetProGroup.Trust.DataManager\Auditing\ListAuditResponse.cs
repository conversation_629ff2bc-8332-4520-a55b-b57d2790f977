// <copyright file="ListAuditResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.EFAuditing.EFModels;
using <PERSON>.<PERSON>dList;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Response model for listing audits.
    /// </summary>
    public class ListAuditResponse
    {
        /// <summary>
        /// Gets or sets the list of audit items.
        /// </summary>
        public IPagedList<AuditUnitOfWorkDTO> AuditItems { get; set; }
    }
}
