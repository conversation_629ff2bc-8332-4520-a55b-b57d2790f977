﻿// <copyright file="MFAController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NetProGroup.Framework.Mvc;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.API.Areas.Tools
{
    /// <summary>
    /// Use this controller to execute tools for access.
    /// </summary>
    [Area("Tools")]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    [ApiController]
    public class MFAController : APIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IUsersDataManager _usersDataManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="MFAController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="usersDataManager">An instance of IUsersDataManager.</param>
        public MFAController(
            ILogger<MFAController> logger,
            IConfiguration configuration,
            IUsersDataManager usersDataManager)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;
            _usersDataManager = usersDataManager;
        }

        /// <summary>
        /// Turns usage of this controller on/off.
        /// </summary>
        /// <param name="context">An instance of ActionExecutingContext.</param>
        public override void OnActionExecuting([NotNull] ActionExecutingContext context)
        {
            if (!_configuration.GetValue<bool>("Diagnose"))
            {
                context.Result = NotFound();
                return;
            }

            base.OnActionExecuting(context);
        }

        /// <summary>
        /// Gets the MFA Info for test user using authenticator.
        /// </summary>
        /// <returns>Result.</returns>
        [AllowAnonymous]
        [HttpGet("mfa-info-authenticator")]
        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch any")]
        public async Task<IActionResult> GetMFAInfo_Authenticator()
        {
            try
            {
                var userId = new Guid("27B7408E-7421-4AC2-91EB-E6A491A5452C");

                await _usersDataManager.SetMFAMethodAsync(userId, MFAMethodConsts.Authenticator);

                var response = await _usersDataManager.GetUserMFAInfoAsync(new NetProGroup.Trust.DataManager.Users.RequestResponses.GetUserMFARequest { UserId = new Guid("27B7408E-7421-4AC2-91EB-E6A491A5452C") });
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generate access token failed.");
                return Ok($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the MFA Info for test user using email.
        /// </summary>
        /// <returns>Result.</returns>
        [AllowAnonymous]
        [HttpGet("mfa-info-email")]
        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch any")]
        public async Task<IActionResult> GetMFAInfo_Email()
        {
            try
            {
                var userId = new Guid("27B7408E-7421-4AC2-91EB-E6A491A5452C");

                await _usersDataManager.SetMFAMethodAsync(userId, MFAMethodConsts.EmailCode);

                var response = await _usersDataManager.GetUserMFAInfoAsync(new NetProGroup.Trust.DataManager.Users.RequestResponses.GetUserMFARequest { UserId = new Guid("27B7408E-7421-4AC2-91EB-E6A491A5452C") });
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generate access token failed.");
                return Ok($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the MFA Info for test user using email.
        /// </summary>
        /// <param name="code">The verification code to check.</param>
        /// <returns>Result.</returns>
        [AllowAnonymous]
        [HttpGet("mfa-verification")]
        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch any")]
        public async Task<IActionResult> GetMFAVerification(string code)
        {
            try
            {
                var userId = new Guid("27B7408E-7421-4AC2-91EB-E6A491A5452C");

                var response = await _usersDataManager.VerifyUserMFAResponseAsync(
                    new NetProGroup.Trust.DataManager.Users.RequestResponses.VerifyUserMFARequest
                    {
                        UserId = userId,
                        ResponseCode = code
                    });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Verification access token failed.");
                return Ok($"Error: {ex.Message}");
            }
        }
    }
}
