﻿// <copyright file="CreateLegalEntityDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities
{
    /// <summary>
    /// Represents a LegalEntity to create.
    /// </summary>
    public abstract class CreateLegalEntityDTO : EntityDTO
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the id of the Jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the name of the LegalEntity.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the LegalEntity.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company should be created as active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number.
        /// </summary>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the incorporation date.
        /// </summary>
        public DateTime? IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the legacy code.
        /// </summary>
        public string LegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the production office.
        /// </summary>
        public string ProductionOffice { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string ReferralOffice { get; set; }
    }
}
