// <copyright file="IPaymentTransactionAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Payments.Request;

namespace NetProGroup.Trust.Application.Contracts.Payments.Transactions
{
    /// <summary>
    /// Provides methods for handling payment-transactions-related operations in the application.
    /// </summary>
    public interface IPaymentTransactionAppService : IScopedService
    {
        /// <summary>
        /// Adds a new payment transaction to the system.
        /// </summary>
        /// <param name="createTransactionRequestDto">The payment information to be added.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created <see cref="PaymentDTO"/>.</returns>
        Task<CreateTransactionResponseDTO> AddPaymentTransactionAsync(CreateTransactionRequestDTO createTransactionRequestDto);

        /// <summary>
        /// Adds a new payment transaction to the system.
        /// </summary>
        /// <remarks>
        /// This method processes the payment transaction and returns a response containing the result of the operation.
        /// </remarks>
        /// <param name="createPaymentRequestDto">The payment information to be added, including details such as transaction ID, amount, currency, payment method, and payer information.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the status of the payment submission as a <see cref="SubmitPaymentResponseDTO"/>.</returns>
        Task<SubmitPaymentResponseDTO> SubmitPaymentTransactionAsync(SubmitPaymentRequestDTO createPaymentRequestDto);
    }
}