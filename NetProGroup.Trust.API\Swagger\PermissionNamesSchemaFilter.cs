// <copyright file="PermissionNamesSchemaFilter.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Permissions;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace NetProGroup.Trust.API.Swagger
{
    /// <summary>
    ///     Schema filter that adds permission name values from WellKnownPermissionNames to swagger documentation.
    /// </summary>
    public class PermissionNamesSchemaFilter : ISchemaFilter
    {
        /// <summary>
        ///     Applies the filter to the specified schema using the given context.
        /// </summary>
        /// <param name="schema">The schema to apply the filter to.</param>
        /// <param name="context">The current schema filter context.</param>
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            ArgumentNullException.ThrowIfNull(schema, nameof(schema));
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            if (context.Type == null)
            {
                return;
            }

            var properties = context.Type.GetProperties();
            foreach (var property in properties)
            {
                if (!property.IsDefined(typeof(PermissionNameAttribute)))
                {
                    continue;
                }

                var propertySchema = schema.Properties.FirstOrDefault(x => x.Key.Equals(property.Name, StringComparison.OrdinalIgnoreCase)).Value;
                if (propertySchema == null)
                {
                    continue;
                }

                var permissionNames = typeof(WellKnownPermissionNames)
                                      .GetFields(BindingFlags.Public | BindingFlags.Static)
                                      .Where(f => f.FieldType == typeof(string))
                                      .Select(x => (string)x.GetValue(null))
                                      .ToList();

                if (permissionNames.Count == 0)
                {
                    continue;
                }

                propertySchema.Enum = permissionNames.Select(x => new OpenApiString(x)).Cast<IOpenApiAny>().ToList();
                propertySchema.Type = "string";
                propertySchema.Format = null;
            }
        }
    }
}