// <copyright file="InboxMessageListItemDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Application.Contracts.MasterClients;

namespace NetProGroup.Trust.Application.Contracts.Inboxes
{
    /// <summary>
    /// Represents an item in the list with inbox items.
    /// </summary>
    public class InboxMessageListItemDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the sender, or null if sent by the system.
        /// </summary>
        public Guid? FromUserId { get; set; }

        /// <summary>
        /// Gets or sets the name of the sender, or null if sent by the system.
        /// </summary>
        public string FromUserName { get; set; }

        /// <summary>
        /// Gets or sets the id of the recipient.
        /// </summary>
        public Guid ToUserId { get; set; }

        /// <summary>
        /// Gets or sets the name of the recipient.
        /// </summary>
        public string ToUserName { get; set; }

        /// <summary>
        /// Gets or sets the subject of the message.
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the message was read.
        /// </summary>
        public bool IsRead { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the message was read or null if not read yet.
        /// </summary>
        public DateTime? ReadAt { get; set; } = DateTime.MinValue;

        /// <summary>
        /// Gets or sets the date and time (local) that the message was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the message has attachments.
        /// </summary>
        public bool HasAttachments { get; set; }

        /// <summary>
        /// Gets or sets the Master Client names related to the inbox entity.
        /// </summary>
        public List<string> MasterClients { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the Jurisdiction names related to the inbox entity.
        /// </summary>
        public List<string> Jurisdictions { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the Legal entity names related to the inbox entity.
        /// </summary>
        public List<string> LegalEntities { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the user names related to the inbox entity.
        /// </summary>
        public List<string> Users { get; set; } = new List<string>();
    }
}
