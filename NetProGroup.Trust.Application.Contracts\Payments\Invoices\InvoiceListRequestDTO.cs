// <copyright file="InvoiceListRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.Payments.Invoices
{
    /// <summary>
    /// Data Transfer Object for requesting a list of invoices with various filtering and pagination options.
    /// </summary>
    public class InvoiceListRequestDTO : IUserIdFilteredRequest
    {
        /// <summary>
        /// Gets or sets the search term for filtering invoices.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the start date for filtering invoices.
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Gets or sets the end date for filtering invoices.
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Gets or sets the company ID for filtering invoices.
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the payment status for filtering invoices.
        /// </summary>
        public InvoicePaymentStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets the financial year for filtering invoices.
        /// </summary>
        public int? FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the field name to sort the invoices by. Defaults to "InvoiceDate".
        /// </summary>
        public string SortBy { get; set; } = "InvoiceDate";

        /// <summary>
        /// Gets or sets the master client id for filtering invoices.
        /// </summary>
        public Guid? MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the sort order for invoices. Defaults to "desc" (descending).
        /// </summary>
        public string SortOrder { get; set; } = "desc";

        /// <summary>
        /// Gets or sets the page number for pagination. Defaults to 1.
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Gets or sets the page size for pagination. Defaults to 10.
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <inheritdoc />
        public Guid UserId { get; set; }
    }
}