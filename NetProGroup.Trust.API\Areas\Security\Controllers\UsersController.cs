﻿// <copyright file="UsersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Users;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Security.Controllers
{
    /// <summary>
    /// Use this controller to get a user after the user has signed in.
    /// </summary>
    [Area("Security")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class UsersController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IUsersAppService _usersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UsersController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="usersAppService">The service for users.</param>
        public UsersController(
            ILogger<UsersController> logger,
            IConfiguration configuration,
            IUsersAppService usersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _usersAppService = usersAppService;
        }

        /// <summary>
        /// Puts the signed-in status for the user with the objectid.
        /// </summary>
        /// <remarks>
        /// For ExternallId users provide the email address also.
        /// If no email address, the user is assumed to be an Entra user.
        ///
        /// Sample request:
        ///
        ///     PUT  /api/v1/security/users/signed-in?email={email}&amp;objectid={objectid}.
        ///
        /// </remarks>
        /// <param name="email">The email address of the user.</param>
        /// <param name="objectId">The objectId of the user in ExternalId.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the user info.</returns>
        [HttpPut("signed-in")]
        [SwaggerOperation(OperationId = "Security_SetUserSignedIn")]
        [ProducesResponseType(typeof(PCPApplicationUserDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetUserSignedIn(
            [FromQuery] string email,
            [FromQuery] Guid objectId)
        {
            PCPApplicationUserDTO item = null;

            var result = await ProcessRequestAsync<ApplicationUserDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(objectId, nameof(objectId));
                },
                executeAsync: async () =>
                {
                    if (string.IsNullOrEmpty(email))
                    {
                        item = await _usersAppService.EntraUserSignedInAsync(objectId);
                        await _usersAppService.ComplementWithPermissionsAsync(item);
                    }
                    else
                    {
                        item = await _usersAppService.ExternalIdUserSignedInAsync(email, objectId);
                    }
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Puts the signed-out status for the given user.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT  /api/v1/security/users/{userId}/signed-out.
        ///
        /// </remarks>
        /// <param name="userId">The id of the user.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{userId}/signed-out")]
        [SwaggerOperation(OperationId = "Security_SetUserSignedOut")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetUserSignedOut(Guid userId)
        {
            var result = await ProcessRequestAsync<ApplicationUserDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },
                executeAsync: async () =>
                {
                    await _usersAppService.UserSignedOutAsync(userId);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Retrieves the permissions for the given user within a specific jurisdiction context (optional).
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET  /api/v1/security/users/{userId}/permissions?jurisdictionId={jurisdictionId}
        /// .
        /// </remarks>
        /// <param name="userId">Id of the user to check the permissions for.</param>
        /// <param name="jurisdictionId">The optional unique identifier jurisdiction.</param>
        /// <returns>An <see cref="IActionResult"/> containing a collection of user permissions.</returns>
        [HttpGet("{userId}/permissions")]
        [SwaggerOperation(OperationId = "GetPermissions")]
        [ProducesResponseType(typeof(IList<UserPermissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPermissions(Guid userId, Guid? jurisdictionId = null)
        {
            IList<UserPermissionDTO> items = new List<UserPermissionDTO>();

            var result = await ProcessRequestAsync(

                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    items = await _usersAppService.GetPermissionsAsync(userId, jurisdictionId);
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Retrieves the authorized jurisdictions for current user.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET  /api/v1/security/users/jurisdictions
        /// .
        /// </remarks>
        /// <returns>An <see cref="IActionResult"/> containing a collection of jurisdictions.</returns>
        [HttpGet("{userId}/jurisdictions")]
        [SwaggerOperation(OperationId = "GetJurisdictions")]
        [ProducesResponseType(typeof(IList<Guid>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAuthorizedJurisdictions()
        {
            IList<Guid> items = new List<Guid>();

            var result = await ProcessRequestAsync(

                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    items = await _usersAppService.GetAuthorizedJurisdictionsAsync();
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }
    }
}
