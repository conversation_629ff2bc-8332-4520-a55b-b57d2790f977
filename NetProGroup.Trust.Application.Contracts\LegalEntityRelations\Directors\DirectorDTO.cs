﻿// <copyright file="DirectorDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors
{
    /// <summary>
    /// Represents a Director.
    /// </summary>
    public class DirectorDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the unique relation code.
        /// </summary>
        public string UniqueRelationCode { get; set; }

        /// <summary>
        /// Gets or sets the id of the LegalEntity.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the name of the LegalEntity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the relation is an individual.
        /// </summary>
        public bool IsIndividual { get; set; }

        /// <summary>
        /// Gets or sets the officer type.
        /// </summary>
        public string OfficerTypeName { get; set; }

        /// <summary>
        /// Gets or sets the director type.
        /// </summary>
        public string DirectorType { get; set; }

        /// <summary>
        /// Gets or sets the code of the director.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the former name.
        /// </summary>
        public string FormerName { get; set; }

        /// <summary>
        /// Gets or sets the optional date of birth (individual).
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the place of birth (individual).
        /// </summary>
        public string PlaceOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country of birth (individual).
        /// </summary>
        public string CountryOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country code of birth (individual).
        /// </summary>
        public string CountryCodeOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the nationality (individual).
        /// </summary>
        public string Nationality { get; set; }

        /// <summary>
        /// Gets or sets the TIN or other identification (individual and company).
        /// </summary>
        public string TIN { get; set; }

        /// <summary>
        /// Gets or sets the residential address (individual).
        /// </summary>
        public string ResidentialAddress { get; set; }

        /// <summary>
        /// Gets or sets the service address (individual).
        /// </summary>
        public string ServiceAddress { get; set; }

        /// <summary>
        /// Gets or sets the address (company).
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number (company).
        /// </summary>
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Gets or sets the appointment date (individual).
        /// </summary>
        public DateTime? AppointmentDate { get; set; }

        /// <summary>
        /// Gets or sets the cessation date (individual).
        /// </summary>
        public DateTime? CessationDate { get; set; }

        /// <summary>
        /// Gets or sets the optional date of incorporation (company).
        /// </summary>
        public DateTime? DateOfIncorporation { get; set; }

        /// <summary>
        /// Gets or sets the place of incorporation (company).
        /// </summary>
        public string IncorporationPlace { get; set; }

        /// <summary>
        /// Gets or sets the country of incorporation (company).
        /// </summary>
        public string IncorporationCountry { get; set; }

        /// <summary>
        /// Gets or sets the country code of incorporation (company).
        /// </summary>
        public string IncorporationCountryCode { get; set; }

        /// <summary>
        /// Gets or sets the metadata for the relation.
        /// </summary>
        public LegalEntityRelationMetaData MetaData { get; set; }
    }
}