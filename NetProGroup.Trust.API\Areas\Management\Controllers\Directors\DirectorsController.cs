﻿// <copyright file="DirectorsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Directors
{
    /// <summary>
    /// Use this controller for director related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/directors")]
    public class DirectorsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IManagementDirectorsAppService _directorsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="directorsAppService">The service for directors.</param>
        public DirectorsController(
            ILogger<DirectorsController> logger,
            IConfiguration configuration,
            IManagementDirectorsAppService directorsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _directorsAppService = directorsAppService;
        }

        /// <summary>
        /// Gets the given director.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/directors/{directorId}.
        ///
        /// </remarks>
        /// <param name="directorId">The id of the director to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorDTO"/>.</returns>
        [HttpGet("{directorId:guid}")]
        [SwaggerOperation(OperationId = "Management_GetDirector")]
        [ProducesResponseType(typeof(DirectorDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDirector(Guid directorId)
        {
            DirectorDTO item = null;

            var result = await ProcessRequestAsync<DirectorDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _directorsAppService.GetDirectorAsync(directorId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given director with the current and the prior version.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/directors/{directorId}/comparison
        /// .
        /// </remarks>
        /// <param name="directorId">The id of the director to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorComparisonDTO"/>.</returns>
        [HttpGet("{directorId:guid}/comparison")]
        [SwaggerOperation(OperationId = "Management_GetDirectorForComparison")]
        [ProducesResponseType(typeof(DirectorComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBeneficialOwnerForComparison(Guid directorId)
        {
            DirectorComparisonDTO item = null;

            var result = await ProcessRequestAsync<DirectorComparisonDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _directorsAppService.GetDirectorForComparisonAsync(directorId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
