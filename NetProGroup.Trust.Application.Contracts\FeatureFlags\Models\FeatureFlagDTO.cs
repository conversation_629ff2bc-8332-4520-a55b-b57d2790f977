﻿// <copyright file="FeatureFlagDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>
namespace NetProGroup.Trust.Application.Contracts.FeatureFlags.Models
{
    /// <summary>
    /// Represents a Data Transfer Object (DTO) for feature flags.
    /// </summary>
    public class FeatureFlagDTO
    {
        /// <summary>
        /// Gets or sets the name of the feature flag.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the feature flag is enabled.
        /// </summary>
        public bool IsEnabled { get; set; }
    }
}
