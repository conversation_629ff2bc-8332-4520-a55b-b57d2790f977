// <copyright file="MessageTemplateSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Services.Communication.EFModels;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.MessageTemplates;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the Message templates in the Trust application.
    /// </summary>
    public static partial class MessageTemplateSeeder
    {
        private static readonly DateTime _seederDate = new DateTime(2024, 01, 01);

        /// <summary>
        /// Defines the seed.
        /// </summary>
        /// <param name="modelBuilder">The ModelBuilder entity.</param>
        public static void SeedMessageTemplates(this ModelBuilder modelBuilder)
        {
            ArgumentNullException.ThrowIfNull(modelBuilder, nameof(modelBuilder));

            modelBuilder.Entity<MessageTemplate>().HasData
            (
                SetupMFACodeTemplate(),
                SetupMFAResetRequestTemplate(),
                SetupMasterClientUserInvitationTemplate(),
                SetupMasterClientUserInvitationReregistrationTemplate(),
                SetupRequestUpdateTemplate(),
                SetupRequestAssistanceTemplate(),
                SetupNewAnnouncementTemplate());
        }

        /// <summary>
        /// Creates the template for an MFA code message.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupMFACodeTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{9D59BD15-CD2F-4674-A3F5-EA6D9BE11452}"),
                    SystemNames.MFACodeMessage, // System name
                    "MFACode Message.", // Display name
                    "Message with the verification code for MFA.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "Your Trident Trust Verification Code", // Subject of the message
                    Properties.Resources.Security_MFAVerificationCode_HtmlBody.RemoveEOL(),
                    Properties.Resources.Security_MFAVerificationCode_PlainText.RemoveEOL(),
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template for an MFA reset message.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupMFAResetRequestTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{CD3B60AB-98C0-403C-9360-FDD93D2AB62B}"),
                    SystemNames.MFAResetRequestMessage, // System name
                    "MFACode Reset Message.", // Display name
                    "Message with the verification code for reset of MFA.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "Your Trident Trust Reset Verification Code", // Subject of the message
                    Properties.Resources.Security_MFAResetCode_HtmlBody.RemoveEOL(),
                    Properties.Resources.Security_MFAResetCode_PlainText.RemoveEOL(),
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template for an UpdateRequest.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupRequestUpdateTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{A68733E2-DE6B-4F82-BB0A-F8F547BE3183}"),
                    SystemNames.RequestUpdateMessage, // System name
                    "RequestUpdate Message.", // Display name
                    "Message for notification of a 'request for update'.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "{company.name} - Request update for Portal", // Subject of the message
                    Properties.Resources.Notification_RequestUpdate_HtmlBody.RemoveEOL(),
                    Properties.Resources.Notification_RequestUpdate_PlainText.RemoveEOL(),
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template for an UpdateRequest.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupRequestAssistanceTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{C382294B-67CB-456D-A59B-986CDDAAEAC3}"),
                    SystemNames.RequestAssistanceMessage, // System name
                    "RequestAssistance Message.", // Display name
                    "Message for notification of a 'request for assistance'.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "{company.name} - {issue} found in the Portal", // Subject of the message
                    Properties.Resources.Notification_RequestAssistance_HtmlBody.RemoveEOL(),
                    Properties.Resources.Notification_RequestAssistance_PlainText.RemoveEOL(),
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template for an invitation for masterclient user.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupMasterClientUserInvitationTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{598C0BC9-C358-402A-A9EC-04E7F7BCA20D}"),
                    SystemNames.UserInvitationMessage, // System name
                    "UserInvitation Message.", // Display name
                    "Message for the inviation of a MasterClient User.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "Invitation for Portal", // Subject of the message
                    string.Empty,
                    string.Empty,
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template for an invitation for masterclient user.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupMasterClientUserInvitationReregistrationTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("{E71CA423-DD1D-4476-80AA-57ED35C0270F}"),
                    SystemNames.UserInvitationReregistrationMessage, // System name
                    "UserInvitation Reregistration Message.", // Display name
                    "Message for the invitation of a MasterClient User for reregistration.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "Invitation for Portal", // Subject of the message
                    string.Empty,
                    string.Empty,
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }

        /// <summary>
        /// Creates the template used to notifiy a new announcement.
        /// </summary>
        /// <returns>The created MessageTemplate.</returns>
        public static MessageTemplate SetupNewAnnouncementTemplate()
        {
            return new MessageTemplate(
                    Guid.Parse("41c4e26a-5fb4-4b1d-aafa-2be666ddbbe7"),
                    SystemNames.NewAnnouncementMessage, // System name
                    "New announcement.", // Display name
                    "Message for notification of a new announcement created in the application.", // Description
                    null, // BBC email addresses
                    null, // To email addresses
                    null, // CC email addresses
                    "{emailSubject}", // Subject of the message
                    Properties.Resources.Notification_New_Announcement_HtmlBody.RemoveEOL(),
                    Properties.Resources.Notification_New_Announcement_PlainText.RemoveEOL(),
                    MessageTemplateBodyTypeEnum.Tokenized,
                    isActive: true,
                    emailAccountId: null)
            { CreatedAt = _seederDate, UpdatedAt = _seederDate };
        }
    }
}
