﻿// <copyright file="FormTemplatesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.Application.AppServices.Forms
{
    /// <summary>
    /// Application service for form templates.
    /// </summary>
    public class FormTemplatesAppService : IFormTemplatesAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly IFormsDataManager _formsDataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplatesAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The workcontext for the request.</param>
        /// <param name="securityManager">The security manager for permissions.</param>
        /// <param name="formsDataManager">The manager to use for form templates.</param>
        public FormTemplatesAppService(ILogger<FormTemplatesAppService> logger,
                                       IMapper mapper,
                                       IWorkContext workContext,
                                       ISecurityManager securityManager,
                                       IFormsDataManager formsDataManager)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _securityManager = securityManager;
            _formsDataManager = formsDataManager;
        }

        /// <inheritdoc/>
        public async Task<ListFormTemplatesDTO> GetFormTemplatesAsync(Guid jurisdictionId, Guid? moduleId)
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var request = new DataManager.Forms.RequestResponses.ListFormTemplatesRequest
            {
                JurisdictionId = jurisdictionId,
                Moduleid = moduleId
            };

            var response = await _formsDataManager.ListFormTemplatesAsync(request);

            return new ListFormTemplatesDTO { FormTemplates = response.FormTemplateItems };
        }

        /// <inheritdoc/>
        public async Task<FormTemplateWithVersionsDTO> GetFormTemplateAsync(Guid formTemplateId, bool allVersions)
        {
            await _securityManager.RequireManagementUserAsync();

            var request = new DataManager.Forms.RequestResponses.GetFormTemplateRequest
            {
                FormTemplateId = formTemplateId,
                GetAllVersions = allVersions
            };

            var response = await _formsDataManager.GetFormTemplateAsync(request);

            await _securityManager.RequireManagementAccessToJurisdictionAsync(response.FormTemplate.JurisdictionId);

            return response.FormTemplate;
        }

        /// <inheritdoc/>
        public async Task<FormTemplateWithVersionsDTO> CreateFormTemplateVersionAsync(CreateFormTemplateVersionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var formTemplate = await _formsDataManager.CheckFormTemplateByIdAsync(model.FormTemplateId);
            await _securityManager.RequireManagementAccessToJurisdictionAsync(formTemplate.JurisdictionId);

            var response = await _formsDataManager.CreateFormTemplateVersionAsync(model);

            return response;
        }

        /// <inheritdoc/>
        public async Task<FormTemplateWithVersionsDTO> UpdateFormTemplateVersionAsync(UpdateFormTemplateVersionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var formTemplate = await _formsDataManager.CheckFormTemplateByIdAsync(model.Id);
            await _securityManager.RequireManagementAccessToJurisdictionAsync(formTemplate.JurisdictionId);

            var response = await _formsDataManager.UpdateFormTemplateVersionAsync(model);
            return response;
        }
    }
}
