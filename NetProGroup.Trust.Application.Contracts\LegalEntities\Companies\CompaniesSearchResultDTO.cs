﻿// <copyright file="CompaniesSearchResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// Model holding the results of the search for master-client/jurisdiction.
    /// </summary>
    public class CompaniesSearchResultDTO
    {
        /// <summary>
        /// Gets or sets the id of the company.
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the incorporationnr. of the company.
        /// </summary>
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the id of the Jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the name of the Jurisdiction.
        /// </summary>
        public string JurisdictionName { get; set; }

        /// <summary>
        /// Gets or sets the id of the MasterClient.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the type of the entity.
        /// </summary>
        public string EntityType { get; set; }

        /// <summary>
        /// Gets or sets the status of the entiy as synced from ViewPoint.
        /// </summary>
        public string VPEntityStatus { get; set; }

        /// <summary>
        /// Gets or sets the date of incorporation date of the company.
        /// </summary>
        public DateTime? IncorporationDate { get; set; }
    }
}
