﻿// <copyright file="BoFaker.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoBogus;
using Bogus;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;

namespace NetProGroup.Trust.Application.AppServices.Tools
{
    /// <summary>
    /// Beneficial owner fakes helper class.
    /// </summary>
    internal static class BoFaker
    {
        /// <summary>
        /// Creates a new Faker for the <see cref="SyncBeneficialOwner"/> class, filling all fields with (hopefully) valid data.
        /// </summary>
        /// <returns>A new Faker for the <see cref="SyncBeneficialOwner"/> class.</returns>
        public static Faker<SyncBeneficialOwner> Create()
        {
            var boFaker = new AutoFaker<SyncBeneficialOwner>()
                .UseSeed(1)
                .RuleFor(owner => owner.Name, (string)null)
                .RuleFor(owner => owner.ToDate, faker => faker.Date.Future().Date)
                .RuleFor(owner => owner.FromDate, faker => faker.Date.Past().Date)
                .RuleFor(owner => owner.Code, faker => faker.Random.Number(100000, 1000000).ToString())
                .RuleFor(owner => owner.PlaceOfBirthOrIncorp, faker => faker.Address.City())
                .RuleFor(owner => owner.CountryOfBirthOrIncorp, faker => faker.Address.Country())
                .RuleFor(owner => owner.CountryCodeOfBirthOrIncorp, faker => faker.Address.CountryCode())
                .RuleFor(owner => owner.DateOfBirthOrIncorp, faker => faker.Date.Past(18))
                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, faker => faker.Address.FullAddress())
                .RuleFor(owner => owner.Country, faker => faker.Address.Country())
                .RuleFor(owner => owner.Nationality, faker => faker.Address.Country())
                .RuleFor(owner => owner.BoDirIncorporationNumber, faker => faker.Random.Number(10000, 1000000).ToString())
                .RuleFor(owner => owner.RelationType, (faker, owner) => "Owner/Controller")
                .RuleFor(owner => owner.OfficerTypeCode, (faker, owner) =>
                {
                    var suffix = faker.Random.Number(1, 6);
                    return "KNTP0" + suffix;
                })
                .RuleFor(owner => owner.TIN, faker => faker.Random.Number(1000, 10000).ToString())
                .FinishWith((faker, owner) =>
                {
                    owner.UniqueRelationId = owner.CompanyNumber + "/" + owner.DateOfBirthOrIncorp.Value.ToString("yyyy-MM-dd");
                    if (owner.OfficerTypeCode.EndsWith('1')) // KNTP01 is always individual, KNTP02-KNTP06 are always companies
                    {
                        owner.FileType = "individual";
                        owner.Name ??= faker.Person.FullName;
                    }
                    else
                    {
                        owner.FileType = "company";
                        owner.Name ??= faker.Company.CompanyName();
                    }

                    owner.OfficerTypeName = owner.OfficerTypeCode switch
                    {
                        "KNTP01" => "Individual",
                        "KNTP02" => "Nevis Company",
                        "KNTP03" => "Legal Person (RLE)",
                        "KNTP04" => "Trusts",
                        "KNTP05" => "Listed Company",
                        "KNTP06" => "Other",
                        "VGTP01" => "BOSS UBO S6(1)(a) Beneficial Owner/S6(1)(b) Controlling Person",
                        "VGTP02" => "BOSS RLE S8(1)(i) Exempt Person",
                        "VGTP03" => "BOSS RLE S8(b)(iii) Licensee or Foreign Regulated",
                        "VGTP04" => "BOSS RLE S8(b)(iv) Sovereign State or Subsidiary",
                        "VGTP05" => "BOSS RLE S8(b)(ii) Listed on Recognised Exchange",
                        "VGTP06" => "BOSS Beneficial Owner – Non RLE",
                        _ => "Unknown"
                    };
                });

            return boFaker;
        }
    }
}
