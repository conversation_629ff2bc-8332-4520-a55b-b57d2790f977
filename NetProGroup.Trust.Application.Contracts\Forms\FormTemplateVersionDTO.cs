﻿// <copyright file="FormTemplateVersionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a version of a form template.
    /// </summary>
    public class FormTemplateVersionDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplateVersionDTO"/> class.
        /// </summary>
        public FormTemplateVersionDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the version of the template.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the version of the template.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the date when the template may be used.
        /// </summary>
        public DateTime? StartAt { get; set; }

        /// <summary>
        /// Gets or sets the actual template (form). Is only set when getting a single item and requested.
        /// </summary>
        public FormBuilder FormBuilder { get; set; }
    }
}
