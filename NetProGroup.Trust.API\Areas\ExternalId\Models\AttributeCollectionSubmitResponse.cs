﻿// <copyright file="AttributeCollectionSubmitResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Areas.ExternalId.Models
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1300 // Element should begin with upper-case letter
    public class AttributeCollectionSubmitResponse
    {
        public AttributeCollectionSubmitResponse()
        {
            data = new AttributeCollectionSubmitResponse_Data();
            data.odatatype = "microsoft.graph.onAttributeCollectionSubmitResponseData";

            this.data.actions = new List<AttributeCollectionSubmitResponse_Action>();
            this.data.actions.Add(new AttributeCollectionSubmitResponse_Action());
        }

        [JsonPropertyName("data")]
        public AttributeCollectionSubmitResponse_Data data { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionSubmitResponse_Data
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        public List<AttributeCollectionSubmitResponse_Action> actions { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionSubmitResponse_Action
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonPropertyName("@odata.type")]
        public string odatatype { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string message { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public AttributeCollectionSubmitResponse_Attribute attributes { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public AttributeCollectionSubmitResponse_AttributeError attributeErrors { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionSubmitResponse_Attribute
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("displayName")]
        public string DisplayName { get; set; }
    }

#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionSubmitResponse_AttributeError
#pragma warning restore SA1402 // File may only contain a single type
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string city { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("displayName")]
        public string DisplayName { get; set; }
    }

#pragma warning disable CA1052 // Static holder types should be Static or NotInheritable
#pragma warning disable SA1402 // File may only contain a single type
    public class AttributeCollectionSubmitResponse_ActionTypes
#pragma warning restore SA1402 // File may only contain a single type
#pragma warning restore CA1052 // Static holder types should be Static or NotInheritable
    {
        public const string ShowValidationError = "microsoft.graph.attributeCollectionSubmit.showValidationError";
        public const string ContinueWithDefaultBehavior = "microsoft.graph.attributeCollectionSubmit.continueWithDefaultBehavior";
        public const string ModifyAttributeValues = "microsoft.graph.attributeCollectionSubmit.modifyAttributeValues";
        public const string ShowBlockPage = "microsoft.graph.attributeCollectionSubmit.showBlockPage";
    }
#pragma warning restore SA1300 // Element should begin with upper-case letter
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
