// <copyright file="CreateTransactionRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Payments.Request
{
    /// <summary>
    /// Represents the data required to create a payment request.
    /// </summary>
    public class CreateTransactionRequestDTO
    {
        /// <summary>
        /// Gets or sets the payment ID.
        /// </summary>
        public Guid PaymentId { get; set; }

        /// <summary>
        /// Gets or sets the description of the order.
        /// </summary>
        /// <value>A brief description of the order.</value>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters.")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the order ID.
        /// </summary>
        /// <value>The unique identifier for the order.</value>
        [StringLength(50, ErrorMessage = "Order ID cannot exceed 50 characters.")]
        public string OrderId { get; set; }

        /// <summary>
        /// Gets or sets the payment redirect URL.
        /// </summary>
        /// <value>The URL to which the customer is redirected for payment processing.</value>
        [Url(ErrorMessage = "Invalid URL format.")]
#pragma warning disable CA1056 // URI-like properties should not be strings
        public string PaymentRedirectUrl { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        /// Gets or sets the cancel URL.
        /// </summary>
        /// <value>The URL to which the customer is redirected if the payment is canceled.</value>
        [Url(ErrorMessage = "Invalid URL format.")]
#pragma warning disable CA1056 // URI-like properties should not be strings
        public string CancelUrl { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        /// Gets or sets the name of the company associated with the cardholder.
        /// </summary>
        /// <value>The company name of the cardholder.</value>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the first name of the cardholder.
        /// </summary>
        /// <value>The cardholder's first name.</value>
        [Required(ErrorMessage = "First name is required.")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters.")]
        public string FirstName { get; set; }

        /// <summary>
        /// Gets or sets the last name of the cardholder.
        /// </summary>
        /// <value>The cardholder's last name.</value>
        [Required(ErrorMessage = "Last name is required.")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters.")]
        public string LastName { get; set; }

        /// <summary>
        /// Gets or sets the email address of the cardholder.
        /// </summary>
        /// <value>The cardholder's email address.</value>
        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email format.")]
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the phone number of the cardholder.
        /// </summary>
        /// <value>The cardholder's phone number.</value>
        [Phone(ErrorMessage = "Invalid phone number format.")]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets the email address of the merchant.
        /// </summary>
        /// <value>The email address of the merchant handling the payment.</value>
        [EmailAddress(ErrorMessage = "Invalid email format.")]
        public string MerchantEmail { get; set; }
    }
}
