﻿// <copyright file="IManagementDirectorsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors
{
    /// <summary>
    /// Interface for the Directors AppService for management.
    /// </summary>
    public interface IManagementDirectorsAppService : IScopedService
    {
        /// <summary>
        /// Gets the current version of a particular Director using the id.
        /// </summary>
        /// <param name="directorId">The director ID.</param>
        /// <returns>The found director.</returns>
        Task<DirectorDTO> GetDirectorAsync(Guid directorId);

        /// <summary>
        /// Gets both current and prior version of a particular Director using the id so they can be compared.
        /// </summary>
        /// <param name="directorId">The director ID.</param>
        /// <returns>A comparson model between the current and previous director.</returns>
        Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(Guid directorId);
    }
}
