﻿// <copyright file="CustomExtensionsValidator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.API.Security
{
    /// <summary>
    /// Custom authentication validation methods class.
    /// </summary>
    public static class CustomExtensionsValidator
    {
        /// <summary>
        /// Authenticates the caller of the extension.
        /// </summary>
        /// <remarks>
        /// https://learn.microsoft.com/en-us/entra/external-id/customers/concept-custom-extensions.
        /// </remarks>
        /// <param name="request">The request holding a Bearer Token.</param>
        /// <param name="configuration">The configuration instance.</param>
        /// <param name="logger">The logger instance.</param>
        /// <returns>True if authenticated.</returns>
        public static async Task<bool> AuthenticateCallerAsync(HttpRequest request, IConfiguration configuration, ILogger logger)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(configuration, nameof(configuration));
            ArgumentNullException.ThrowIfNull(logger, nameof(logger));

            char[] separator = new[] { ' ' };

            var expectedAudience = configuration.GetValue<string>("ExternalId:ClientId");
            var expectedTenant = configuration.GetValue<string>("ExternalId:TenantId");
            var expectedIssuer = $"https://{expectedTenant}.ciamlogin.com/{expectedTenant}/v2.0";

            var expectedAZP = "99045fe1-7639-4a75-9d4a-577b6ca3810f";

            if (request.Headers.TryGetValue("Authorization", out var headerAuth))
            {
                var jwtToken = headerAuth.First().Split(separator, StringSplitOptions.RemoveEmptyEntries)[1];
                var jwtSecurityToken = await TokenHelpers.GetValidatedJwtSecurityTokenAsync(jwtToken, expectedIssuer, expectedAudience, logger);

                if (jwtSecurityToken == null)
                {
                    return false;
                }

                var claimAZP = jwtSecurityToken.Claims.FirstOrDefault(x => x.Type == "azp");

                if (claimAZP == null)
                {
                    logger.LogError("azp claim not found in the Bearer Token.");
                    return false;
                }

                if (claimAZP.Value != expectedAZP)
                {
                    logger.LogError("zap claim not found or not the correct value (expected {Expected} but found {Found}).", expectedAZP, claimAZP.Value);
                    return false;
                }

                return true;
            }

            return false;
        }
    }
}
