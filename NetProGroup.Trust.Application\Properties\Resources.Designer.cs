﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace NetProGroup.Trust.Application.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NetProGroup.Trust.Application.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;html&gt;
        ///
        ///&lt;head&gt;
        ///&lt;meta http-equiv=Content-Type content=&quot;text/html; charset=windows-1252&quot;&gt;
        ///&lt;meta name=Generator content=&quot;Microsoft Word 15 (filtered)&quot;&gt;
        ///&lt;style&gt;
        ///&lt;!--
        /// /* Font Definitions */
        /// @font-face
        ///	{font-family:&quot;Cambria Math&quot;;
        ///	panose-1:2 4 5 3 5 4 6 3 2 4;}
        ///@font-face
        ///	{font-family:Aptos;}
        /// /* Style Definitions */
        /// p.MsoNormal, li.MsoNormal, div.MsoNormal
        ///	{margin-top:0in;
        ///	margin-right:0in;
        ///	margin-bottom:8.0pt;
        ///	margin-left:0in;
        ///	line-height:107%;
        ///	font-size:11.0pt;
        ///	font-family:&quot;Ap [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string Notification_RequestAssistance_HtmlBody {
            get {
                return ResourceManager.GetString("Notification_RequestAssistance_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entity Name: {company.name} 
        ///Entity Code: {company.code}
        ///Master Client Code: {masterclient.code}
        ///Requestor: {requestor}
        ///Type of Request: {request}
        ///.
        /// </summary>
        internal static string Notification_RequestAssistance_PlainText {
            get {
                return ResourceManager.GetString("Notification_RequestAssistance_PlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;html&gt;
        ///
        ///&lt;head&gt;
        ///&lt;meta http-equiv=Content-Type content=&quot;text/html; charset=windows-1252&quot;&gt;
        ///&lt;meta name=Generator content=&quot;Microsoft Word 15 (filtered)&quot;&gt;
        ///&lt;style&gt;
        ///&lt;!--
        /// /* Font Definitions */
        /// @font-face
        ///	{font-family:&quot;Cambria Math&quot;;
        ///	panose-1:2 4 5 3 5 4 6 3 2 4;}
        ///@font-face
        ///	{font-family:Aptos;}
        /// /* Style Definitions */
        /// p.MsoNormal, li.MsoNormal, div.MsoNormal
        ///	{margin-top:0in;
        ///	margin-right:0in;
        ///	margin-bottom:8.0pt;
        ///	margin-left:0in;
        ///	line-height:107%;
        ///	font-size:11.0pt;
        ///	font-family:&quot;Ap [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string Notification_RequestUpdate_HtmlBody {
            get {
                return ResourceManager.GetString("Notification_RequestUpdate_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entity Name: {company.name} 
        ///Entity Code: {company.code}
        ///Master Client Code: {masterclient.code}
        ///VP {masterfile.label} Masterfile Code: {masterfile.code}
        ///Requestor: {requestor} 
        ///Position: {position} 
        ///Type of Request: {request}
        ///Comment: {comment}
        ///
        ///.
        /// </summary>
        internal static string Notification_RequestUpdate_PlainText {
            get {
                return ResourceManager.GetString("Notification_RequestUpdate_PlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The confirmation code for resetting MFA is: {code}.
        /// </summary>
        internal static string Security_MFAResetCode_HtmlBody {
            get {
                return ResourceManager.GetString("Security_MFAResetCode_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The confirmation code for resetting MFA is: {code}.
        /// </summary>
        internal static string Security_MFAResetCode_PlainText {
            get {
                return ResourceManager.GetString("Security_MFAResetCode_PlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The verification code is: {code}.
        /// </summary>
        internal static string Security_MFAVerificationCode_HtmlBody {
            get {
                return ResourceManager.GetString("Security_MFAVerificationCode_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The verification code is: {code}.
        /// </summary>
        internal static string Security_MFAVerificationCode_PlainText {
            get {
                return ResourceManager.GetString("Security_MFAVerificationCode_PlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap trident_logo {
            get {
                object obj = ResourceManager.GetObject("trident_logo", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html lang=&quot;en-US&quot;&gt;
        ///
        ///&lt;head&gt;
        ///    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;
        ///    &lt;meta charset=&quot;utf-8&quot;&gt;
        ///    &lt;title&gt;
        ///    &lt;/title&gt;
        ///    &lt;style&gt;
        ///        body {
        ///            line-height: 108%;
        ///            font-family: Aptos;
        ///            font-size: 11pt
        ///        }
        ///
        ///        h1,
        ///        h2,
        ///        h3,
        ///        h4,
        ///        h5,
        ///        h6,
        ///        p {
        ///            margin: 0pt 0pt 8pt
        ///        }
        ///
        ///        li {
        ///            margin-top: 0pt;
        ///            marg [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserInvitation_HtmlBody {
            get {
                return ResourceManager.GetString("UserInvitation_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are pleased to welcome you to our upgraded Caribbean Private Client Portal, a secure online platform designed to streamline your interaction with Trident Trust and enhance your experience.
        ///
        ///To create your user account for the portal, you must use the Master Client Code and registered email address we have on file for you:
        ///
        ///Master Client Code: {masterclient.code}
        ///Registered email address: {email}
        /// 
        ///
        ///To activate your account, simply click on the link below and enter your registered email address on [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserInvitation_PlainText {
            get {
                return ResourceManager.GetString("UserInvitation_PlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html lang=&quot;en-US&quot;&gt;
        ///
        ///&lt;head&gt;
        ///    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;
        ///    &lt;meta charset=&quot;utf-8&quot;&gt;
        ///    &lt;title&gt;
        ///    &lt;/title&gt;
        ///    &lt;style&gt;
        ///        body {
        ///            line-height: 108%;
        ///            font-family: Aptos;
        ///            font-size: 11pt
        ///        }
        ///
        ///        h1,
        ///        h2,
        ///        h3,
        ///        h4,
        ///        h5,
        ///        h6,
        ///        p {
        ///            margin: 0pt 0pt 8pt
        ///        }
        ///
        ///        li {
        ///            margin-top: 0pt;
        ///            marg [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserInvitation_ReReg_HtmlBody {
            get {
                return ResourceManager.GetString("UserInvitation_ReReg_HtmlBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are pleased to welcome you to our upgraded Caribbean Private Client Portal, a secure online platform designed to streamline your interaction with Trident Trust and enhance your experience.
        ///
        ///Our new portal is built on completely new technology infrastructure, which means you will need to create a new user account for the new portal. You will not be able to use your login details for the older portal that you have been using. To create your account you must use the Master Client Code and registered email [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserInvitation_ReReg_PlainText {
            get {
                return ResourceManager.GetString("UserInvitation_ReReg_PlainText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Notification_New_Announcement_HtmlBody
        {
            get
            {
                return ResourceManager.GetString("Notification_NewAnnouncement_HtmlBody", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Notification_New_Announcement_PlainText
        {
            get
            {
                return ResourceManager.GetString("Notification_NewAnnouncement_PlainText", resourceCulture);
            }
        }
    }
}
