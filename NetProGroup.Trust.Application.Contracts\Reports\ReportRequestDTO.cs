// <copyright file="ReportRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Report.Enum;

namespace NetProGroup.Trust.Application.Contracts.Reports
{
    /// <summary>
    /// Request model for getting financial reports.
    /// </summary>
    public class ReportRequestDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the search term.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the report types to filter by.
        /// </summary>
        public IEnumerable<ReportType> ReportTypes { get; set; }
    }
}