﻿// <copyright file="AccessController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Text;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NetProGroup.Framework.Azure.Security.AccessTokens;
using NetProGroup.Framework.Mvc;

namespace NetProGroup.Trust.API.Areas.Tools
{
    /// <summary>
    /// Use this controller to execute tools for access.
    /// </summary>
    [Area("Tools")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    public class AccessController : APIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IAccessTokenService _accessTokenService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="accessTokenService">An instance of IAccessTokenService.</param>
        public AccessController(
            ILogger<AccessController> logger,
            IConfiguration configuration,
            IAccessTokenService accessTokenService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;
            _accessTokenService = accessTokenService;
        }

        /// <summary>
        /// Turns usage of this controller on/off.
        /// </summary>
        /// <param name="context">An instance of ActionExecutingContext.</param>
        public override void OnActionExecuting([NotNull] ActionExecutingContext context)
        {
            if (!_configuration.GetValue<bool>("Diagnose"))
            {
                context.Result = NotFound();
                return;
            }

            base.OnActionExecuting(context);
        }

        /// <summary>
        /// Retrieve an access token for swagger authentication using the parameters used in the request.
        /// </summary>
        /// <remarks>
        /// This endpoint is only going to be used for and by swagger auth flow.
        /// This endpoint is hidden from swager UI.
        /// </remarks>
        /// <returns>The generated access token.</returns>
        [AllowAnonymous]
        [HttpPost("client-credentials-token")]
        [ApiExplorerSettings(IgnoreApi = true)]
        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch any")]
        public async Task<IActionResult> GetAccessTokenFromRequest()
        {
            try
            {
                // Retrieve the variables used in the request, ClientId and ClientSecret.
                var authHeader = Request.Headers.Authorization;
                var authHeaderInfo = authHeader.ToString().Split(' ');
                var base64TokenInfo = Convert.FromBase64String(authHeaderInfo[1]);
                var base64DecodedClientIdAndSecret = Encoding.UTF8.GetString(base64TokenInfo);

                AccessTokenRequest tokenRequest = new AccessTokenRequest(
                    _configuration.GetValue<string>("AzureAd:TenantId"),
                    base64DecodedClientIdAndSecret.Split(':')[0],
                    base64DecodedClientIdAndSecret.Split(':')[1],
                    $"{_configuration.GetValue<string>("AzureAd:Audience")}/.default");

                var result = await _accessTokenService.AcquireTokenAsync(tokenRequest);

                return Ok(new { access_token = result.Token });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generate access token failed.");
                return Ok($"Error: {ex.Message}");
            }
        }
    }
}
