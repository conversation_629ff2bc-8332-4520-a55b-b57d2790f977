﻿// <copyright file="IJurisdictionTaxRatesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>
using NetProGroup.Framework.DependencyInjection.Interfaces;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates
{
    /// <summary>
    /// Interface for managing jurisdiction tax rates.
    /// </summary>
    public interface IJurisdictionTaxRatesAppService : IScopedService
    {
        /// <summary>
        /// Asynchronously retrieves tax rates for a specific jurisdiction with pagination.
        /// </summary>
        /// <param name="jurisdictionId">The ID of the jurisdiction.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains tax rates for the jurisdiction.</returns>
        Task<IPagedList<JurisdictionTaxRateDTO>> GetTaxRatesByJurisdictionAsync(Guid jurisdictionId, int pageNumber, int pageSize);

        /// <summary>
        /// Creates or updates a tax rate for a specific jurisdiction.
        /// </summary>
        /// <param name="model">The data transfer object containing the tax rate details.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task CreateOrUpdateTaxRateAsync(CreateOrUpdateTaxRateDTO model);

        /// <summary>
        /// Deletes a jurisdiction tax rate by its ID.
        /// </summary>
        /// <param name="jurisdictionTaxRateId">The ID of the tax rate to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteTaxRateAsync(Guid jurisdictionTaxRateId);
    }
}
