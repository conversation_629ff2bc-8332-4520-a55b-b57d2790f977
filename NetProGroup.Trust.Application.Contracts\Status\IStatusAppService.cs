// <copyright file="IStatusAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Status.Models;

namespace NetProGroup.Trust.Application.Contracts.Status
{
    /// <summary>
    /// Application service interface for system status operations.
    /// </summary>
    public interface IStatusAppService : IScopedService
    {
        /// <summary>
        /// Gets the ViewPoint sync status including details of the last successful sync.
        /// </summary>
        /// <returns>The ViewPoint sync status with details for each entity type.</returns>
        Task<ViewPointSyncStatusDTO> GetViewPointSyncStatusAsync();
    }
}
