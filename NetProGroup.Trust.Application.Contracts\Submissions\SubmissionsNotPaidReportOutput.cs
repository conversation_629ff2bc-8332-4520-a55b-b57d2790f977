// <copyright file="SubmissionsNotPaidReportOutput.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// The submissions not paid output model.
    /// </summary>
    public class SubmissionsNotPaidReportOutput
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsNotPaidReportOutput"/> class.
        /// </summary>
        /// <param name="fileContent">The file content.</param>
        /// <param name="legalEntities">Output legal entites.</param>
        public SubmissionsNotPaidReportOutput(byte[] fileContent, IEnumerable<LegalEntity> legalEntities)
        {
            FileContent = fileContent;
            LegalEntities = legalEntities;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsNotPaidReportOutput"/> class.
        /// </summary>
        /// <param name="legalEntities">The Submissions.</param>
        public SubmissionsNotPaidReportOutput(IEnumerable<LegalEntity> legalEntities)
        {
            LegalEntities = legalEntities;
        }

        /// <summary>
        /// Gets or sets the file content.
        /// </summary>
#pragma warning disable CA1819 // Properties should not return arrays
        public byte[] FileContent { get; set; }
#pragma warning restore CA1819 // Properties should not return arrays

        /// <summary>
        /// Gets or sets the submissions.
        /// </summary>
        public IEnumerable<LegalEntity> LegalEntities { get; set; }
    }
}