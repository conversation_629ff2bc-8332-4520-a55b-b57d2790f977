﻿// <copyright file="UpdateLegalEntityDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.LegalEntities
{
    /// <summary>
    /// Represents a LegalEntity.
    /// </summary>
    public abstract class UpdateLegalEntityDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid? MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the id of the Jurisdiction.
        /// </summary>
        public Guid? JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the code of the LegalEntity.
        /// </summary>
        public string Name { get; set; }
    }
}
