// <copyright file="ContactsInfoReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Reports.Nevis.ContactsInfo;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.ContactsInfoReports
{
    /// <summary>
    /// Service for generating contact information reports.
    /// </summary>
    public class ContactsInfoReportService : IContactsInfoReportService
    {
        private readonly IContactsInfoReportGenerator _contactsInfoReportGenerator;
        private readonly IReportsDataManager _reportDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly ILockManager _lockManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ContactsInfoReportService"/> class.
        /// </summary>
        /// <param name="contactsInfoReportGenerator">The contact info report generator.</param>
        /// <param name="reportDataManager">The report data manager.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="lockManager">The lock manager.</param>
        public ContactsInfoReportService(
            IContactsInfoReportGenerator contactsInfoReportGenerator,
            IReportsDataManager reportDataManager,
            IDocumentManager documentManager,
            ILockManager lockManager)
        {
            _contactsInfoReportGenerator = contactsInfoReportGenerator;
            _reportDataManager = reportDataManager;
            _documentManager = documentManager;
            _lockManager = lockManager;
        }

        /// <inheritdoc/>
        public async Task GenerateReportAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock);
            var jobLockId = Check.NotDefaultOrNull(jobLock.Id, nameof(jobLock));

            await _lockManager.RefreshLockAsync(jobLockId);

            var fileTemplate = _contactsInfoReportGenerator.GenerateReportNameForTodayAsync();
            var fileName = $"{fileTemplate}.xlsx";

            if (await _reportDataManager.ReportExists(new Report
            {
                ReportName = fileTemplate,
                Type = ReportType.ContactsInfo
            }))
            {
                return;
            }

            var reportOutput = await _contactsInfoReportGenerator.GenerateReportAsync();

            var documentId = await _documentManager.CreateDocumentAsync(
                documentType: (int)ReportType.ContactsInfo,
                description: fileTemplate,
                fileName: fileName,
                data: reportOutput.FileContent,
                saveChanges: false);

            await _reportDataManager.AddReport(new Report
            {
                DocumentId = documentId,
                ReportName = fileTemplate,
                Type = ReportType.ContactsInfo,
            });
        }
    }
}
