// <copyright file="IBoDirAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using <PERSON>.PagedList;

namespace NetProGroup.Trust.Application.Contracts.BoDir
{
    /// <summary>
    /// Interface for Beneficial Owner and Director (BO/Dir) operations within the application.
    /// </summary>
    public interface IBoDirAppService : IScopedService
    {
        /// <summary>
        /// Lists Beneficial Owners and Directors using parameters in the request.
        /// </summary>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <returns>A paged list of BO/Dir entries matching the search criteria.</returns>
        Task<IPagedList<BoDirItemDTO>> SearchBoDirsAsync(SearchBoDirRequestDTO request);

        /// <summary>
        /// Downloads a financial report for the Beneficial Owners and Directors using parameters in the request.
        /// </summary>
        /// <param name="request">The request DTO holding the parameters for the download.</param>
        /// <returns>The file content and metadata for the report.</returns>
        Task<BoDirDownloadResponseDTO> GenerateBoDirRSearchResultsExportAsync(SearchBoDirRequestDTO request);
    }
}
