// <copyright file="IFeatureFlagsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.FeatureFlags.Models;

namespace NetProGroup.Trust.Application.Contracts.FeatureFlags
{
    /// <summary>
    /// Interface for documents operations within the application.
    /// </summary>
    public interface IFeatureFlagsAppService : IScopedService
    {
        /// <summary>
        /// Retrieve a list of feature flags settings.
        /// </summary>
        /// <returns>A list of Feature Flag settings.</returns>
        Task<List<FeatureFlagDTO>> GetFeatureFlagsAsync();
    }
}