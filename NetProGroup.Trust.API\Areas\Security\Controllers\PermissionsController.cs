﻿// <copyright file="PermissionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Security;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Security.Controllers
{
    /// <summary>
    /// Use this controller to get the permissiosn for the calling user (x-userid).
    /// </summary>
    [Area("Security")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class PermissionsController : TrustAPIControllerBase
    {
        private readonly IPermissionsAppService _permissionsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="PermissionsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="permissionsAppService">Instance of the permission app service.</param>
        public PermissionsController(
            ILogger<PermissionsController> logger,
            IPermissionsAppService permissionsAppService)
            : base(logger)
        {
            _permissionsAppService = permissionsAppService;
        }

        /// <summary>
        /// Retrieves a list with all permissions for the portal.
        /// </summary>
        /// <returns>An <see cref="IActionResult"/> containing a collection of permissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetAllPermissions")]
        [ProducesResponseType(typeof(IList<PermissionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPermissions()
        {
            var items = new List<PermissionDTO>();

            var result = await ProcessRequestAsync(

                executeAsync: async () =>
                {
                    items = _permissionsAppService.GetAllPermissions().Select(x => new PermissionDTO { PermissionName = x }).ToList();
                    await Task.CompletedTask;
                },

                createResponseModel: () =>
                {
                    return items;
                });

            return result.AsResponse();
        }
    }
}
