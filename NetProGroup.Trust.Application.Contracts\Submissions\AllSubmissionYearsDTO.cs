﻿// <copyright file="AllSubmissionYearsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents the list of years that the user can choose from when searching for submissions.
    /// </summary>
    public class AllSubmissionYearsDTO
    {
        /// <summary>
        /// Gets or sets the id of the module this list of years applies to.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the collection of years.
        /// </summary>
        public IReadOnlyCollection<int> Years { get; set; } = new List<int>();
    }
}
