﻿// <copyright file="CompanyWithAnnualFeeStatusSearchResultDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.LegalEntities.Companies
{
    /// <summary>
    /// Model holding the results of the search for master-client/jurisdiction.
    /// </summary>
    public class CompanyWithAnnualFeeStatusSearchResultDTO
    {
        /// <summary>
        /// Gets or sets the id of the company.
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the legacy code of the company.
        /// </summary>
        public string CompanyLegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the VP code of the company.
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// Gets or sets the id of the MasterClient.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the code of the MasterClient.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the date when the submission was created.
        /// </summary>
        public DateTime? DateSubmissionCreated { get; set; }

        /// <summary>
        /// Gets or sets the date when the submission was submitted.
        /// </summary>
        public DateTime? DateSubmissionSubmitted { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the fee is paid.
        /// </summary>
        public bool IsPaid { get; set; }
    }
}
