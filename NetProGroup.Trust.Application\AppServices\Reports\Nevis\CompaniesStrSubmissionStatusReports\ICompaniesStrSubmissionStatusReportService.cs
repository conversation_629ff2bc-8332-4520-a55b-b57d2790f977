// <copyright file="ICompaniesStrSubmissionStatusReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.AppServices.Reports.Nevis.CompaniesStrSubmissionStatusReports
{
    /// <summary>
    /// Interface for companies without submissions report service.
    /// </summary>
    public interface ICompaniesStrSubmissionStatusReportService : ITransientService, IReportService;
}
