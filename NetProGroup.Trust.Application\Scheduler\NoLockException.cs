﻿// <copyright file="NoLockException.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Scheduler
{
    /// <summary>
    /// Exception when acquiring a lock fails.
    /// </summary>
    public class NoLockException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NoLockException"/> class.
        /// </summary>
        public NoLockException() : base()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="NoLockException"/> class.
        /// </summary>
        /// <param name="message">A message that describes the error.</param>
        public NoLockException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="NoLockException"/> class.
        /// </summary>
        /// <param name="message">A message that describes the error.</param>
        /// <param name="innerException">The exception that causes this exception to occur.</param>
        public NoLockException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
