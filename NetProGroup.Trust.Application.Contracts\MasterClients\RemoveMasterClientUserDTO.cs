﻿// <copyright file="RemoveMasterClientUserDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Represents a MasterClient for removal.
    /// </summary>
    public class RemoveMasterClientUserDTO : EntityDTO
    {
        /// <summary>
        /// Gets or sets the id of the Master Client.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the id of the User.
        /// </summary>
        public Guid UserId { get; set; }
    }
}
