// <copyright file="CreateRFIDocumentDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Documents;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represent the necessary data to create a request for information document.
    /// </summary>
    public class CreateRFIDocumentDTO : CreateDocumentDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the upload of documents is finished or not.
        /// </summary>
        /// <remarks>
        /// If the value is true the request for information status will be set to Active.
        /// If the value is false the request for information status will remain Draft.
        /// </remarks>
        public bool UploadComplete { get; set; }
    }
}