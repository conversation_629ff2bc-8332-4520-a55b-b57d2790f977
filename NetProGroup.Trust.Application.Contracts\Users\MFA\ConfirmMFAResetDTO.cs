﻿// <copyright file="ConfirmMFAResetDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.MFA
{
    /// <summary>
    /// DTO for confirmation of the MFA reset.
    /// </summary>
    public class ConfirmMFAResetDTO
    {
        /// <summary>
        /// Gets or sets the id of the user to confirm the reset for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the confirmation code.
        /// </summary>
        public string ConfirmationCode { get; set; }
    }
}
