﻿// <copyright file="ConfigurationSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Services.Configuration.EFModels;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the default configuration.
    /// </summary>
    public static partial class ConfigurationSeeder
    {
        /// <summary>
        /// Defines the seeding.
        /// </summary>
        /// <param name="modelBuilder">Instance of ModelBuilder.</param>
        public static void SeedConfigurations(this ModelBuilder modelBuilder)
        {
            ArgumentNullException.ThrowIfNull(modelBuilder);

            var date = new DateTime(2024, 1, 1);

            var keyPrefix = ConfigurationConsts.NOTIFICATION_UPDATEREQUEST_RECIPIENT_PREFIX;

            // Configuration of recipients for various production offices
            modelBuilder.Entity<Configuration>().HasData(
                new Configuration(new Guid("{BBCF4766-03D3-40E4-825E-0F9E21133174}"), $"{keyPrefix}.default", "<EMAIL>", null)
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Configuration(new Guid("{3FED38CC-1CB7-4846-80B2-AE7C93B1BD75}"), $"{keyPrefix}.tpanvg", "<EMAIL>", null)
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Configuration(new Guid("{1D3E063B-C066-499E-A7AE-FC1793673DD4}"), $"{keyPrefix}.tcyp", "<EMAIL>", null)
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Configuration(new Guid("{7BACFBC7-E987-4F05-BAE8-CC9EFBBC151D}"), $"{keyPrefix}.thko", "<EMAIL>", null)
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Configuration(new Guid("{F9A6D733-D01B-4DEC-AE22-B9A3C7BD5362}"), $"{keyPrefix}.tbvi", "<EMAIL>", null)
                {
                    CreatedAt = date,
                    UpdatedAt = date
                });
        }
    }
}
