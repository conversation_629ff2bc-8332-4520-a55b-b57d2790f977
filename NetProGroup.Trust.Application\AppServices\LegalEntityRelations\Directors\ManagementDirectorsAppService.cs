﻿// <copyright file="ManagementDirectorsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Permissions;

namespace NetProGroup.Trust.Application.AppServices.LegalEntityRelations.Directors
{
    /// <summary>
    /// Application service for Directors.
    /// </summary>
    public class ManagementDirectorsAppService : IManagementDirectorsAppService
    {
        private readonly IDirectorsDataManager _dataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ManagementDirectorsAppService"/> class.
        /// </summary>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="securityManager">The security manager for getting roles etc.</param>
        public ManagementDirectorsAppService(IDirectorsDataManager dataManager,
                                   ISecurityManager securityManager)
        {
            _dataManager = dataManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> GetDirectorAsync(Guid directorId)
        {
            Check.NotDefaultOrNull<Guid>(directorId, nameof(directorId));

            var director = await _dataManager.CheckDirectorByIdAsync(directorId);
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.BODIRModule_View, director.LegalEntityId);

            return await _dataManager.GetDirectorAsync(directorId);
        }

        /// <inheritdoc/>
        public async Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(Guid directorId)
        {
            Check.NotDefaultOrNull<Guid>(directorId, nameof(directorId));

            var director = await _dataManager.CheckDirectorByIdAsync(directorId);
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.BODIRModule_View, director.LegalEntityId);

            return await _dataManager.GetDirectorForComparisonAsync(directorId);
        }
    }
}
