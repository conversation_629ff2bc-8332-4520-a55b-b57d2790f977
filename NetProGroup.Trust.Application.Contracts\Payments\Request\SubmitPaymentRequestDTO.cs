// <copyright file="SubmitPaymentRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Payments
{
    /// <summary>
    /// Represents the data required to submit a payment request.
    /// </summary>
    public class SubmitPaymentRequestDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the payment transaction.
        /// </summary>
        /// <value>The unique transaction ID.</value>
        [Required(ErrorMessage = "Transaction ID is required.")]
        public Guid TransactionId { get; set; }

        /// <summary>
        /// Gets or sets the token ID for authorizing the payment.
        /// </summary>
        /// <value>The token ID representing the payment authorization.</value>
        [Required(ErrorMessage = "Token ID is required.")]
        [StringLength(250, ErrorMessage = "Token ID cannot exceed 250 characters.")]
        public string TokenId { get; set; }
    }
}