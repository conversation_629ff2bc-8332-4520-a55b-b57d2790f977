// <copyright file="ListSubmissionsByMasterClientRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.MasterClients
{
    /// <summary>
    /// Request for exporting a submission data report.
    /// </summary>
    public class ListSubmissionsByMasterClientRequestDTO : PagedRequest
    {
        /// <summary>
        /// Gets or sets the financial year to search the submisisons for.
        /// </summary>
        public List<int> FinancialYears { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the fee is paid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the list of legal entity IDs.
        /// </summary>
        public List<Guid> LegalEntityIds { get; set; }

        /// <summary>
        /// Gets or sets the value indicating whether the submission has an invoice.
        /// </summary>
        public bool? HasInvoice { get; set; }

        /// <summary>
        /// Gets or sets the list of submission statuses to filter by.
        /// </summary>
        public List<SubmissionStatus> SubmissionStatuses { get; set; } = new List<SubmissionStatus>();
    }
}