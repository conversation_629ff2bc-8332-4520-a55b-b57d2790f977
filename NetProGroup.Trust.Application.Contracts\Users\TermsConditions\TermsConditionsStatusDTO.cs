// <copyright file="TermsConditionsStatusDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users.TermsConditions
{
    /// <summary>
    /// DTO for Terms and Conditions acceptance status.
    /// </summary>
    public class TermsConditionsStatusDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether Terms and Conditions have been accepted.
        /// </summary>
        public bool IsAccepted { get; set; }

        /// <summary>
        /// Gets or sets the version of Terms and Conditions that was accepted.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets when Terms and Conditions were accepted.
        /// </summary>
        public DateTime? AcceptedAt { get; set; }
    }
}
