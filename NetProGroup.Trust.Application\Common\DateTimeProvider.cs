// <copyright file="DateTimeProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Common;

namespace NetProGroup.Trust.Application.Common
{
    /// <summary>
    /// Provides the current date and time, using the system clock.
    /// </summary>
    public class DateTimeProvider : IDateTimeProvider
    {
        /// <summary>
        /// Gets the current date and time in Coordinated Universal Time (UTC).
        /// </summary>
        public DateTime UtcNow => DateTime.UtcNow;

        /// <summary>
        /// Gets the current date and time in the local timezone of the system.
        /// </summary>
        public DateTime Now => DateTime.Now;

        /// <summary>
        /// Gets the current date without the time component in the local timezone.
        /// The time component is set to midnight (00:00:00).
        /// </summary>
        public DateTime Today => DateTime.Today;

        /// <inheritdoc />
        public DateTime NevisNow
        {
            get
            {
                TimeZoneInfo nevisTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Atlantic Standard Time");
                return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, nevisTimeZone);
            }
        }
    }
}
