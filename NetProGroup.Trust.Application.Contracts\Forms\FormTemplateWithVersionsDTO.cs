﻿// <copyright file="FormTemplateWithVersionsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a form template with the versions.
    /// </summary>
    public class FormTemplateWithVersionsDTO : FormTemplateDTO
    {
        /// <summary>
        /// Gets or sets the collection of versions of the template.
        /// </summary>
        public IReadOnlyCollection<FormTemplateVersionDTO> Versions { get; set; }
    }
}
