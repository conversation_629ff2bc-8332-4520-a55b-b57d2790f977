﻿// <copyright file="ISystemAuditItemBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Trust.Application.Contracts.Audits;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Builder for ActivityLogItemDTO.
    /// </summary>
    public interface ISystemAuditItemBuilder : IScopedService
    {
        /// <summary>
        /// Builds the list of ActivityLogItemDTO from the given activitylogs and db audits.
        /// </summary>
        /// <param name="activityLogs">Collection of ActivityLog.</param>
        /// <param name="unitsOfWork">Grouped list of AuditUnitOfWorkDTO.</param>
        /// <returns>Collection of ActivityLogItemDTO.</returns>
        Task<IEnumerable<ActivityLogItemDTO>> BuildActivityLogItemsAsync(IEnumerable<ActivityLog> activityLogs, Dictionary<Guid, List<AuditUnitOfWorkDTO>> unitsOfWork);
    }
}